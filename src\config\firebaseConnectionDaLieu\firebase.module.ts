import { Module, DynamicModule, Provider, Global } from '@nestjs/common';
import { FirebaseServiceDaLieu } from './firebase.service';
import { FIREBASE_SERVICE_ACCOUNT_DA_LIEU } from './constants';
import {
    FirebaseOptions,
    FireBaseAsyncServiceAccount,
    FirebaseOptionsFactory,
} from './interfaces';
import { createFirebaseDaLieuProviders } from './firebase.providers';

import { connectionFactory } from './firebase-connection.provider';

@Global()
@Module({
    providers: [FirebaseServiceDaLieu, connectionFactory],
    exports: [FirebaseServiceDaLieu, connectionFactory],
})
export class FirebaseAdminDaLieuModule {
    /**
     * Registers a configured NestKnex Module for import into the current module
     */
    public static register(options: FirebaseOptions): DynamicModule {
        return {
            module: FirebaseAdminDaLieuModule,
            providers: createFirebaseDaLieuProviders(options),
        };
    }

    /**
     * Registers a configured NestKnex Module for import into the current module
     * using dynamic options (factory, etc)
     */
    public static registerAsync(options: FireBaseAsyncServiceAccount): DynamicModule {
        return {
            module: FirebaseAdminDaLieuModule,
            providers: [...this.createProviders(options)],
        };
    }

    private static createProviders(options: FireBaseAsyncServiceAccount): Provider[] {
        if (options.useExisting || options.useFactory) {
            return [this.createOptionsProvider(options)];
        }

        return [
            this.createOptionsProvider(options),
            {
                provide: options.useClass,
                useClass: options.useClass,
            },
        ];
    }

    private static createOptionsProvider(options: FireBaseAsyncServiceAccount): Provider {
        if (options.useFactory) {
            return {
                provide: FIREBASE_SERVICE_ACCOUNT_DA_LIEU,
                useFactory: options.useFactory,
                inject: options.inject || [],
            };
        }

        // For useExisting...
        return {
            provide: FIREBASE_SERVICE_ACCOUNT_DA_LIEU,
            useFactory: async (optionsFactory: FirebaseOptionsFactory) =>
                await optionsFactory.createServiceAccount(),
            inject: [options.useExisting || options.useClass],
        };
    }
}
