import { HttpService, Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { LOG_SERVICE_EVENT } from 'src/audit-log/constant';
import { UrlConfigService } from 'src/config/config.url.service';
import { UtilService } from 'src/config/util.service';
import { GetAvailableDoctorDto } from './dto/get-available-doctor.dto';

@Injectable()
export class BookingScheduleService {
    private readonly logger: Logger = new Logger(BookingScheduleService.name);
    private GATEWAY_BASE_URL: string;
    constructor(
        private readonly httpService: HttpService,
        private readonly eventEmitter: EventEmitter2,
        private readonly configUrlService: UrlConfigService,
        private readonly utilService: UtilService,
    ) {
        this.GATEWAY_BASE_URL = this.configUrlService.getGatewayAPIUrl();
    }

    async getAvailableDoctor(formData: GetAvailableDoctorDto): Promise<any> {
        try {
            const { pageIndex = 0, pageSize = 10, name, partnerId } = formData;
            const baseUrl = `${this.GATEWAY_BASE_URL}/booking-schedule/booking-available-doctor`;
            return (await this.httpService.get(baseUrl, { params: { pageIndex, pageSize, name, partnerId } }).toPromise()).data;
        } catch (error) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'getAvailableDoctor',
                summary: 'Lấy danh sách bác sĩ cho phép đặt khám',
                nameParent: 'getAvailableDoctor',
                params: formData,
                errorBody: this.utilService.errorHandler(error),
                response: {},
                message: error?.message || 'Lỗi từ Gateway API',
            });
            this.logger.error(`Error when exec getAvailableDoctor(). Cause: ${error.message}`);
            throw error;
        }
    }
}
