import { Module } from '@nestjs/common';
import { CountryMongoController } from './country-mongo.controller';
import { CountryMongoService } from './country-mongo.service';
import { CountrySchema } from './schemas/country.schema';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { COUNTRY_COLLECTION_NAME } from './schemas/constants';
import { MongooseModule } from '@nestjs/mongoose';
import { GlobalSettingModule } from 'src/global-setting/global-setting.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: COUNTRY_COLLECTION_NAME, schema: CountrySchema },
      { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
    ]),
    GlobalSettingModule,
  ],
  controllers: [CountryMongoController],
  providers: [CountryMongoService],
})
export class CountryMongoModule { }
