
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Expose, Exclude } from 'class-transformer';
import { IsNotEmpty, IsDateString } from 'class-validator';

export class CreateEventDTO {
    @ApiProperty({
        description: 'Format kiểu ISO String',
        required: true,
        type: String,
    })
    @IsDateString({ strict: true }, {
        message: 'Thông tin createTime. ISOString',
    })
    createTime: string;

    @ApiProperty({
        description: 'topicId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    topicId: string;

    @ApiProperty({
        description: 'eventData',
        required: true,
        type: Object,
    })
    eventData: object;

    @ApiProperty({
        description: 'appId',
        required: false,
        type: String,
    })
    appId: string;

    @ApiProperty({
        description: 'title',
        required: false,
        type: String,
    })
    title: string;

    @ApiProperty({
        description: 'partnerId',
        required: false,
        type: String,
    })
    partnerId: string;

    @ApiProperty({
        description: 'type',
        required: false,
        type: Number,
        default: 0,
    })
    type?: number;

    @ApiProperty({
        description: 'userId',
        required: false,
        type: Object,
    })
    userId: string;

    translation?: any[] = []

    content?:string
}
