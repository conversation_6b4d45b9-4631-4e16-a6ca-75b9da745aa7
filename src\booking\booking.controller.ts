import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { BookingService } from '../booking/booking.service';

import { ApiTags } from '@nestjs/swagger';
import { GetBookingDetailDTO } from './dto/GetBookingDetailDto';
import { AuthGuard } from '@nestjs/passport';

@Controller('booking-inside')
@ApiTags('Booking - Quản lý Phiếu khám')
export class BookingController {

    constructor(
        private readonly bookingService: BookingService,
    ) { }

    @Post('detail')
    @UseGuards(AuthGuard('jwt'))
    async getBookingDetail(@Body() bookingDetailDto: GetBookingDetailDTO): Promise<any> {
        const transactionCode = bookingDetailDto.code;
        // const bookingInfo = await this.bookingService.getBookingDetailUMCByTransactionCode(transactionCode);
        const bookingInfo = await this.bookingService.getBookingDetailThuDucByTransactionCode(transactionCode);
        const get2charLast = bookingInfo.patientMobile ? bookingInfo.patientMobile.slice(-2) : '';
        const secretMobile = bookingInfo.patientMobile ? `${bookingInfo.patientMobile.slice(0, -4)}xxxx${get2charLast}` : '';
        const bookingStatus = this.bookingService.returnBookingStatus(bookingInfo.status);
        return {
            ...bookingInfo,
            patientMobile: secretMobile,
            bookingStatus,
        };
    }

}
