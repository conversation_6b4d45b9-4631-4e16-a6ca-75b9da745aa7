import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

export class UpdateChildrenInFeatureOfPartnerDTO {
    @ApiProperty({ description: 'appId', required: true, type: String })
    @IsNotEmpty({ message: 'appId is required' })
    @Transform(value => `${value}`.trim())
    readonly appId: string;

    @ApiProperty({ description: 'partnerId', required: true, type: String })
    @IsNotEmpty({ message: 'partnerId is required' })
    @Transform(value => `${value}`.trim())
    readonly partnerId: string;

    @ApiProperty({ description: 'featureId', required: true, type: String })
    @IsNotEmpty({ message: 'featureId is required' })
    @Transform(value => `${value}`.trim())
    readonly featureId: string;

    @ApiProperty({ description: 'childFeatureId', required: true, type: String })
    @IsNotEmpty({ message: 'childFeatureId is required' })
    @Transform(value => `${value}`.trim())
    readonly childFeatureId: string;

    @ApiProperty({ description: 'Name of feature', required: false, type: String })
    @Transform(value => `${value}`.trim())
    name: string;

    @ApiProperty({ description: 'Priority of feature', required: false, type: Number })
    @Transform(value => Number(value))
    priority: number;

    @ApiProperty({ description: 'Status of feature', required: false, type: Boolean })
    @Transform(value => Boolean(value))
    status: boolean;

    @ApiProperty({ description: 'Mobile Status of feature', required: false, type: Boolean })
    @Transform(value => Boolean(value))
    mobileStatus: boolean;
}
