import * as mongoose from 'mongoose';
import * as json<PERSON>ongo from '@meanie/mongoose-to-json';
import { EXPIRED_BOOKING_COLLECTION_NAME } from './constants';
import { SUBJECT_COLLECTION_NAME } from 'src/subject-mongo/schemas/constants';
import { SERVICE_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { BOOKING_SLOT_COLLECTION_NAME } from 'src/his-gateway/schemas/constants';
import { ROOM_COLLECTION_NAME } from 'src/room-mongo/schemas/constants';
import { DOCTOR_COLLECTION_NAME } from 'src/doctor-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const ExpiredBookingSchema = new Schema({
    id: { type: String },
    bookingSlotId: { type: String, required: true },
    bookingSlot: { type: Schema.Types.ObjectId, ref: BOOKING_SLOT_COLLECTION_NAME },
    bookingId: { type: String, unique: true, required: true },
    sequenceNumber: { type: Number },
    date: { type: Date },
    subjectId: { type: String },
    subject: { type: Schema.Types.ObjectId, ref: SUBJECT_COLLECTION_NAME },
    roomId: { type: String },
    room: { type: Schema.Types.ObjectId, ref: ROOM_COLLECTION_NAME },
    doctorId: { type: String },
    doctor: { type: Schema.Types.ObjectId, ref: DOCTOR_COLLECTION_NAME },
    serviceId: { type: String },
    service: { type: Schema.Types.ObjectId, ref: SERVICE_COLLECTION_NAME },
}, {
    collection: EXPIRED_BOOKING_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
