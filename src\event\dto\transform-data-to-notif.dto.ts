import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsDateString, ValidateIf } from 'class-validator';

export class TransformDataToNotifDTO {
    @ApiProperty({
        description: 'Date Format kiểu ISO String',
        required: false,
        type: String,
        default: '',
    })
    @IsDateString({ strict: true }, {
        message: 'Thông tin ngày Booking. ISOString',
    })
    @ValidateIf(o => o.date)
    @Transform(value => `${value}`.trim())
    date?: string;
}
