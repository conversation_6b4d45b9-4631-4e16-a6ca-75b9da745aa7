import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';

@Injectable()
export class MailgunConfigService extends ConfigManager {
    provideConfigSpec() {
        return {
            MAILGUN_API_KEY: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    getListUserRevieveError() {
        return [
            // this.get<string>('SENDGRID_EMAIL_RECEIVE_ERROR_1'),
            this.get<string>('MAILGUN_EMAIL_RECEIVE_ERROR_2'),
            // this.get<string>('SENDGRID_EMAIL_RECEIVE_ERROR_3'),
        ];
    }

    getApiKey(): string {
        return this.get<string>('MAILGUN_API_KEY');
    }
    
    getMailgunDomain(): string {
        return this.get<string>('MAILGUN_DOMAIN');
    }
}
