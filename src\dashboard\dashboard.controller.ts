import { Controller, Get, Post, Body, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiBody } from '@nestjs/swagger';
import { DashboardService } from './dashboard.service';
import { BookingDTO } from './dto/bookingDto';
import { AuthGuard } from '@nestjs/passport';

@Controller('dashboard')
export class DashboardController {
    constructor(private readonly dashboard: DashboardService) { }

    @Post('/booking-list-by-filter')
    @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách booking theo điều kiện', description: '<PERSON><PERSON><PERSON> danh sách booking theo điều kiện' })
    @ApiBody({
        description: '<PERSON><PERSON><PERSON> danh sách booking theo điều kiện.',
        type: BookingDTO,
      })
    async filterBookingList(): Promise<any> {
        return this.dashboard.filterBookingList();
    }

    @Get('/phieu-kham-trong-ngay')
    @UseGuards(AuthGuard('jwt'))
    async findAllPhieuKhamTrongNgay(): Promise<any> {
        return this.dashboard.getPhieuKhamTrongNgay();
    }

    @Get('/user-trong-ngay')
    @UseGuards(AuthGuard('jwt'))
    async findAllUserTrongNgay(): Promise<any> {
        return this.dashboard.getUserTrongNgay();
    }

    @Get('/benh-nhan-trong-ngay')
    @UseGuards(AuthGuard('jwt'))
    async findAllBenhNhanTrongNgay(): Promise<any> {
        return this.dashboard.getBenhNhanTrongNgay();
    }

    @Get('/chart-filter-by-date')
    @UseGuards(AuthGuard('jwt'))
    async chartFilterByDate(): Promise<any> {
        return this.dashboard.getChartInfoByDate();
    }
}
