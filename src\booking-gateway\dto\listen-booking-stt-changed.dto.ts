import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

export class ListenBookingSTTChangedDTO {

    @ApiProperty({
        description: 'bookingId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    bookingId: string;

    @ApiProperty({
        description: 'sequenceNumber',
        required: true,
        type: Number,
    })
    @Transform(value => Number(value))
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    sequenceNumber: number;
}
