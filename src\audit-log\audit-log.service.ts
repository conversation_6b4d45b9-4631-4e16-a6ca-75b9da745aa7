import { ServiceLogRequest } from './dto/service-log.request.dto';
import { IServiceLog, IServiceLogDocument } from './interfaces/service-log.schema';
import { CreateAuditLogDto } from './dto/audit-log.dto';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { IAuditLog, IAuditLogDocument } from './interfaces/audit-log.interface';
import { AUDIT_LOG_NAME, ExposeUrl, LOG_REQUEST_EVENT, LOG_SERVICE_EVENT, SERVICE_LOG_NAME } from './constant';
import { OnEvent } from '@nestjs/event-emitter';
import { AuditLogRequest } from './dto/audit-log.request';
import { CreateServiceLog } from './dto/create-service.dto';
import { ConfigRepoService } from 'src/config/config.repo.service';

@Injectable()
export class AuditLogService {
    constructor(
        @InjectModel(AUDIT_LOG_NAME) private readonly auditLogModel: Model<IAuditLogDocument>,
        @InjectModel(SERVICE_LOG_NAME) private readonly serviceLogModel: Model<IServiceLogDocument>,
        private config: ConfigRepoService,
    ) { }

    @OnEvent(LOG_REQUEST_EVENT, { async: true })
    async onRequestSend(request: any): Promise<void> {
        if (this.filterUrl(request.url)) {
            try {
                const newLog = (({
                    headers,
                    body,
                    params,
                    query,
                    url,
                    method }) => ({
                        headers,
                        body,
                        params,
                        query,
                        url,
                        method,
                    }))(request);
                await this.createAuditLog(newLog);
            } catch (error) {
                throw error;
            }
        }

    }

    @OnEvent(LOG_SERVICE_EVENT, { async: true })
    async onServiceError(payload: any): Promise<void> {
        try {
            await this.createServiceLog(payload);
        } catch (error) {
            throw error;
        }
    }

    async createServiceLog(data: CreateServiceLog): Promise<IServiceLog> {
        try {
            return await this.serviceLogModel.create({ ...data, nameRepo: this.config.getRepoName() });
        } catch (error) {
            throw error;
        }
    }

    async getServiceLogs(formData: ServiceLogRequest): Promise<any> {
        try {
            let filter = {};
            const { pageSize, pageIndex, nameParent, nameRepo } = formData;
            if (nameParent) {
                filter = { ...filter, nameParent };
            }
            if (nameRepo) {
                filter = { ...filter, nameRepo };
            }
            const [rows, totalRows] = await Promise.all([
                this.serviceLogModel
                    .find({})
                    .skip(pageIndex * pageSize)
                    .limit(pageSize)
                    .where(filter)
                    .sort({ createdAt: `desc` })
                    .exec(),
                this.serviceLogModel.find(filter).countDocuments(),
            ]);
            return {
                pageSize,
                pageIndex,
                rows,
                totalRows,
            };
        } catch (error) {
            throw error;
        }
    }

    async getServiceLogById(id: string): Promise<IServiceLog> {
        try {
            return await this.serviceLogModel.findById(id);
        } catch (error) {
            throw error;
        }
    }

    async createAuditLog(data: CreateAuditLogDto): Promise<IAuditLog> {
        try {
            return await this.auditLogModel.create({ ...data, nameRepo: this.config.getRepoName() });
        } catch (error) {
            throw error;
        }
    }

    async getAuditLogs(formData: AuditLogRequest): Promise<any> {
        try {
            let filter = {};
            const { pageSize, pageIndex, nameRepo } = formData;
            if (nameRepo) {
                filter = { ...filter, nameRepo };
            }
            const [rows, totalRows] = await Promise.all([
                this.auditLogModel
                    .find({})
                    .skip(pageIndex * pageSize)
                    .limit(pageSize)
                    .where(filter)
                    .sort({ createdAt: `desc` })
                    .exec(),
                this.auditLogModel.find(filter).countDocuments(),
            ]);
            return {
                pageSize,
                pageIndex,
                rows,
                totalRows,
            };
        } catch (error) {
            throw error;
        }
    }

    async getAuditLogById(id: string): Promise<IAuditLog> {
        try {
            return await this.auditLogModel.findById(id);
        } catch (error) {
            throw error;
        }
    }

    filterUrl(url: string): boolean {
        if (url.length <= 1) {
            return false;
        }
        for (const reg of Object.values(ExposeUrl)) {
            const ex = new RegExp(reg);
            return ex.test(url) ? false : true;
        }
        return true;
    }
}
