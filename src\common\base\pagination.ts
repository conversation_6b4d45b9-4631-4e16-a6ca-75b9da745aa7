import { ApiProperty } from '@nestjs/swagger';
import { IsNumber } from 'class-validator';
import { Transform } from 'class-transformer';

export class Pagination<T = any> {
    @ApiProperty({description: 'Số trang'})
    @IsNumber()
    @Transform(value => Number(value))
    pageIndex: number;

    @ApiProperty({description: 'Số lượng phần tử trong trang'})
    @IsNumber()
    @Transform(value => Number(value))
    pageSize: number;

    totalRows?: number;

    rows?: T[];
}
