
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, ValidateIf, IsMobilePhone } from 'class-validator';

export class SearchBookingCSDTO {

    @ApiProperty({
        description: 'Họ và tên',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.fullname)
    fullname?: string;

    @ApiProperty({
        description: 'Số điện thoại',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    // @IsMobilePhone('vi-VN', { strictMode: false }, {
    //     message: 'Vui lòng nhập đúng định dạng mobile phone',
    // })
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.mobile)
    mobile?: string;

    @ApiProperty({
        description: '<PERSON>à<PERSON> k<PERSON> medproID',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsMobilePhone('vi-VN', { strictMode: false }, {
        message: 'Vui lòng nhập đúng định dạng mobile phone',
    })
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.username)
    username?: string;

    @ApiProperty({
        description: 'Mã số hồ sơ cũ',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.msbn)
    msbn?: string;

    @ApiProperty({
        description: 'Mã phiếu',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.bookingCode)
    bookingCode?: string;

    @ApiProperty({
        description: 'Mã giao dịch',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.transactionId)
    transactionId?: string;

    @ApiProperty({
        description: 'Bệnh viện',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.hospitalId)
    hospitalId?: string;
}
