import { Body, Controller, Delete, Get, Headers, HttpCode, Param, Post, Put } from '@nestjs/common';
import { ApiHeader, ApiOperation, ApiTags, ApiParam } from '@nestjs/swagger';
import { AppIdService } from './app-id.service';
import { AddChildrenFeatureDTO } from './dto/add-child-feature-into-feature.dto';
import { AddFeatureIntoPartnerDTO } from './dto/add-feature-into-partner.dto';
import { AddPartnerIntoAppIdDTO } from './dto/add-partner-into-appId.dto';
import { CopyFeatureFromPartnerDTO } from './dto/copy-feature-from-partner.dto';
import { CreateAppIdDTO } from './dto/create-app-id.dto';
import { UpdateAppIdDTO } from './dto/update-app-id.dto';
import { IAppId } from './interfaces/app-id.inteface';
import { UpdatePartnerFeatureDto } from './dto/update-partner-feature.dto';

@ApiTags('Quản lý AppId')
@Controller('app-id')
export class AppIdController {
    constructor(private readonly appIdService: AppIdService) {}

    @Get('domain')
    @ApiOperation({ summary: 'Lấy tất cả danh sách feature theo bệnh viện app id' })
    async getFeaturesDomain(@Headers('appid') appId: string): Promise<any> {
        return this.appIdService.getFeaturesDomain(appId);
    }

    @Get('list-partner')
    @ApiOperation({ summary: 'Lấy tất cả danh sách partnerId theo appIds' })
    @ApiHeader({
        name: 'appid',
        required: true,
    })
    async getListPartnerByAppIdApi(@Headers('appid') appId: string): Promise<any[]> {
        return this.appIdService.getListPartnerByAppId(appId);
    }

    @Get('list-feature')
    @ApiOperation({ summary: 'Lấy tất cả danh sách feature theo appIds' })
    @ApiHeader({
        name: 'appid',
        required: true,
    })
    async getFeatureInAppIdApi(@Headers('appid') appId: string): Promise<any> {
        return this.appIdService.getFeatureInAppId(appId);
    }

    @Get('list-partner/feature/:featureId')
    @ApiOperation({ summary: 'Lấy tất cả danh sách partner theo appIds và feature' })
    @ApiHeader({
        name: 'appid',
        required: true,
    })
    @ApiParam({
        name: 'featureId',
        required: true,
    })
    async getPartnerByAppIdAndFeatureId(@Headers('appid') appId: string, @Param('featureId') featureId: string ): Promise<any> {
        return this.appIdService.getPartnerByAppIdAndFeatureId(appId, featureId);
    }

    @Get()
    @ApiOperation({ summary: 'Lấy tất cả danh sách appIds' })
    async getListAppId() {
        return this.appIdService.findAll();
    }

    @Get(':appId')
    @ApiOperation({ summary: 'Lấy appId theo appId' })
    async getDataByAppId(@Param('appId') appId: string): Promise<any> {
        return this.appIdService.getDataByAppId(appId);
    }

    @Get(':appId/feature/partner/:partnerId')
    @ApiOperation({ summary: 'Lấy danh sách feature của partner trong appId'})
    async getFeaturePartnerInAppId(@Param('appId') appId: string, @Param('partnerId') partnerId: string): Promise<any> {
        return this.appIdService.getFeatureOfPartnerInAppId(appId, partnerId);
    }

    @Post('feature/partner-in-appId')
    @ApiOperation({ summary: 'Copy tất cả feature hiện có của partner sang partner trong appId' })
    async copyFeatureFromPartnerToPartnerOfAppId(@Body() fromData: CopyFeatureFromPartnerDTO): Promise<any> {
        return this.appIdService.copyFeatureFromBasePartnerToPartnerOfAppId(fromData);
    }

    @Post()
    @ApiOperation({ summary: 'Tạo mới appId' })
    async createAppId(@Body() fromData: CreateAppIdDTO): Promise<any> {
        return this.appIdService.createAppId(fromData);
    }

    @Post('partner')
    @ApiOperation({ summary: 'Thêm partner vào appId bằng partnerId' })
    async addPartnerIntoAppId(@Body() fromData: AddPartnerIntoAppIdDTO): Promise<any> {
        return this.appIdService.addPartnerIntoAppId(fromData);
    }

    @Post('partner/feature')
    @ApiOperation({ summary: 'Thêm feature vào partner trong appId' })
    async addFeatureIntoPartnerOfAppId(@Body() fromData: AddFeatureIntoPartnerDTO): Promise<any> {
        return this.appIdService.addFeatureIntoPartnerOfAppId(fromData);
    }

    @Put('partner/feature')
    @ApiOperation({ summary: 'Thêm feature vào partner trong appId' })
    async update(@Body() fromData: UpdatePartnerFeatureDto): Promise<any> {
        return this.appIdService.updatePartnerFeature(fromData);
    }

    @Post('partner/feature/child-feature')
    @ApiOperation({ summary: 'Thêm children feature vào feature của partnerId trong appId' })
    async addChildFeatureIntoFeatureOfPartner(@Body() fromData: AddChildrenFeatureDTO): Promise<any> {
        return this.appIdService.addChildFeatureIntoFeatureOfPartner(fromData);
    }

    @Put()
    @ApiOperation({ summary: 'Cập nhật appId' })
    async updateAppId(@Body() fromData: UpdateAppIdDTO): Promise<any> {
        return this.appIdService.updateAppId(fromData);
    }

    @Delete(':appId')
    @ApiOperation({ summary: 'Xóa appId' })
    @HttpCode(204)
    async deleteAppId(@Param('appId') appId: string): Promise<any> {
        return this.appIdService.deleteAppId(appId);
    }

    @Delete(':appId/partner/:partnerId')
    @ApiOperation({ summary: 'Xóa partner trong appId bằng partnerId' })
    async deletePartnerInAppId(@Param('appId') appId: string, @Param('partnerId') partnerId: string) {
        return this.appIdService.deletePartnerInAppId(appId, partnerId);
    }

    @Delete(':appId/partner/:partnerId/feature/:featureId')
    @ApiOperation({ summary: 'Xóa feature trong partner của appId bằng featureId' })
    @HttpCode(204)
    async deleteFeatureInPartnerOfAppId(
        @Param('appId') appId: string,
        @Param('partnerId') partnerId: string,
        @Param('featureId') featureId: string,
    ): Promise<any> {
        return this.appIdService.deleteFeatureInPartnerOfAppId(appId, partnerId, featureId);
    }

    @Delete(':appId/partner/:partnerId/feature/:featureId/child-feature/:childFeatureId')
    @ApiOperation({ summary: 'Xóa children feature trong feature trong partner của appId bằng featureId' })
    @HttpCode(204)
    async deleteChildFeatureInPartnerOfAppId(
        @Param('appId') appId: string,
        @Param('partnerId') partnerId: string,
        @Param('featureId') featureId: string,
        @Param('childFeatureId') childFeatureId: string,
    ): Promise<any> {
        return this.appIdService.deleteChildFeatureInPartnerOfAppId(appId, partnerId, featureId, childFeatureId);
    }
}
