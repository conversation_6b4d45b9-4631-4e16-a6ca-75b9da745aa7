import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, IsMobilePhone } from 'class-validator';

export class GetInfoByPhoneThirdPartnerDTO {

    @ApiProperty({
        description: 'Số điện thoại',
        required: true,
        type: 'string',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng nhập số điện thoại',
    })
    @IsMobilePhone('vi-VN', { strictMode: false }, {
        message: 'Số điện thoại không hợp lệ.',
    })
    readonly phone: string;

    @ApiProperty({
        description: 'apiKey',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    apiKey: string;
}
