import { Injectable, HttpService, HttpException, HttpStatus } from '@nestjs/common';
import { Observable } from 'rxjs';
import { AxiosResponse } from 'axios';
import * as https from 'https';
import { UrlConfigService } from 'src/config/config.url.service';

@Injectable()
export class PkhHttpService {

    constructor(
        private readonly httpService: HttpService,
        private readonly urlConfigService: UrlConfigService,
    ) { }

    getHttpRequest(url: string): Observable<AxiosResponse<any>> {
        return this.httpService.get(url);
    }

    getHttpRequestWithHeaders(url: string, partnerId: string): Observable<AxiosResponse<any>> {
        return this.httpService.get(url, {
            headers: {
                partnerid: partnerId,
            },
        });
    }

    getHttpRequestWithData(url: string, data: any): Observable<AxiosResponse<any>> {
        return this.httpService.get(url, {
            data,
            headers: {
                'Content-Type': 'application/json',
            },
        });
    }

    postHttpRequest(url: string, params: object): Observable<AxiosResponse<any>> {
        const httpsAgent = new https.Agent({ rejectUnauthorized: false });
        return this.httpService.post(url, params, { httpsAgent });
        // const env = this.urlConfigService.getEnv();
        // if (env === 'PRODUCTION') {
        //     return this.httpService.post(url, params, { httpsAgent });
        // } else {
        //     return this.httpService.post(url, params);
        // }
    }

    postHttpRequestTimeOUt(url: string, params: object, msTimeOut: number): Observable<AxiosResponse<any>> {
        const httpsAgent = new https.Agent({ rejectUnauthorized: false });
        return this.httpService.post(url, params, { httpsAgent, timeout: msTimeOut });
    }

    postHttpRequestWithHeaders(url: string, params: object, partnerId: string): Observable<AxiosResponse<any>> {
        const httpsAgent = new https.Agent({ rejectUnauthorized: false });
        return this.httpService.post(url, params, {
            httpsAgent, headers: {
                partnerid: partnerId,
            },
        });
        // const env = this.urlConfigService.getEnv();
        // if (env === 'PRODUCTION') {
        //     return this.httpService.post(url, params, { httpsAgent });
        // } else {
        //     return this.httpService.post(url, params);
        // }
    }
}
