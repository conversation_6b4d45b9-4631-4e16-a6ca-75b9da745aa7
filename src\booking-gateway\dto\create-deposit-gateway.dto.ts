
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, ValidateIf, IsEmail } from 'class-validator';

export class CreateDepositGatewayDTO {

    @ApiProperty({
        description: 'Email',
        required: false,
        type: String,
    })
    @IsEmail({}, { message: '<PERSON>ail không đúng định dạng' })
    @ValidateIf(o => o.email)
    @Transform(value => `${value}`.trim())
    readonly email: string;

    @ApiProperty({
        description: 'subTotal',
        required: true,
        type: Number,
    })
    @Transform(value => Number(value))
    readonly subTotal: number;

    @ApiProperty({
        description: 'Platform',
        required: false,
        type: String,
        enum: [
            'ios', 'android', 'pc', 'web',
        ],
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.platform)
    platform: string;

    @ApiProperty({
        description: '<PERSON><PERSON>ơng thức thanh toán',
        required: true,
        type: String,
        default: 'momo',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    methodId: string;

    @ApiProperty({
        description: 'Thông tin payment type detail',
        required: true,
        type: String,
        default: 'momo',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    paymentTypeDetail: string;

    @ApiProperty({
        description: 'Redirect URL',
        required: true,
        type: String,
    })
    redirectUrl: string;

    @ApiProperty({
        description: 'cbWebView',
        required: false,
        type: Number,
    })
    @Transform(value => Number(value))
    @ValidateIf(o => o.cbWebView)
    cbWebView?: number;
}
