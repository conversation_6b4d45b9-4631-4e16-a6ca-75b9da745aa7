import { ConfigManager } from '@nestjsplus/config';
import * as <PERSON><PERSON> from 'joi';

export class ConfigMessageService extends ConfigManager {
    provideConfigSpec() {
        return {
            MESSAGE_SERVICE_PARTNER: {
                validate: Joi.string(),
                required: true,
            },
            MESSAGE_SERVICE_ACCESS_KEY: {
                validate: Joi.string(),
                required: true,
            },
            GATEWAY_API_URL: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    getMessageServicePartner(): string {
        return this.get<string>('MESSAGE_SERVICE_PARTNER');
    }

    getMessageServiceAccessKey(): string {
        return this.get<string>('MESSAGE_SERVICE_ACCESS_KEY');
    }

    getMessageServiceProvider(): string {
        return this.get<string>('MESSAGE_SERVICE_PROVIDER') || 'MESSAGE_HUB';
    }

    getMessageServiceBaseUrl(): string {
        return this.get<string>('GATEWAY_API_URL');
    }
}
