import { ValidateIf } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsArray, IsNotEmpty } from 'class-validator';

class DataInfo {
    @ApiProperty()
    title: string;

    @ApiProperty()
    key: string;

    @ApiProperty()
    value: string;
}

// tslint:disable-next-line: max-classes-per-file
export class TelemedInfoBookingDto {
    @ApiProperty({ description: 'Files đính kèm'})
    @Transform(value => `${value}`.trim())
    files?: string[];

    @ApiProperty({ description: '<PERSON><PERSON><PERSON> thông tin chỉ số nhập'})
    @IsArray()
    @ValidateIf(o => o.indicatorsInfo)
    dataInfo?: DataInfo[];
}
