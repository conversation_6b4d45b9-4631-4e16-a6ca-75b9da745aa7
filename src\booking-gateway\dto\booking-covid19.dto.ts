import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsDateString, ValidateIf } from 'class-validator';

export class BookingCovid19DTO {
    @ApiProperty({ description: 'serviceId', required: false, type: String, default: '' })
    @Transform(value => `${value}`.trim())
    serviceId?: string;

    @ApiProperty({
        description: 'Date Format kiểu ISO String',
        required: false,
        type: String,
        default: '',
    })
    @IsDateString({ strict: true }, {
        message: 'Thông tin ngày Booking. ISOString',
    })
    @ValidateIf(o => o.date)
    @Transform(value => `${value}`.trim())
    date?: string;

    @ApiProperty({ description: 'Day: 0 all day, Day 1: is morning, Day: 2 is afternoon', required: false, type: Number, default: 0 })
    @Transform(value => Number(value))
    day?: number;
}
