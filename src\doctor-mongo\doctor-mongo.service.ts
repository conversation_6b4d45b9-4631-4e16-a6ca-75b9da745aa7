import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { DOCTOR_COLLECTION_NAME } from './schemas/constants';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { first } from 'lodash';
import { IDoctor } from './interfaces/doctor.interface';
import { CreateDoctorDTO } from './dto/create-doctor.dto';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { UpdateDoctorDTO } from './dto/update-doctor.dto';

@Injectable()
export class DoctorMongoService {

    constructor(
        @InjectModel(DOCTOR_COLLECTION_NAME) private doctorModel: Model<IDoctor>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitaltModel: Model<IHospital>,
    ) { }

    async create(createDoctorDTO: CreateDoctorDTO): Promise<IDoctor> {
        try {
            const doctor = new this.doctorModel(createDoctorDTO);
            const hospital = await this.hospitaltModel.findOne({ partnerId: createDoctorDTO.partnerId }).exec();
            doctor.hospitalId = hospital._id;
            const insertDoctor = await doctor.save();
            hospital.doctors.push(insertDoctor._id);
            await hospital.save();
            return insertDoctor.toJSON();
        } catch (error) {
            throw new HttpException(error, HttpStatus.BAD_REQUEST);
        }
    }

    async findOne(id: string): Promise<any> {
        try {
            const info = await this.doctorModel
                .findOne({ _id: id })
                .populate('hospitalId')
                .exec();
            if (!info) {
                throw new HttpException('Không tìm thấy thông tin.', HttpStatus.NOT_FOUND);
            }
            return info;
        } catch (error) {
            throw new HttpException('Có lỗi xảy ra.', HttpStatus.BAD_REQUEST);
        }

    }

    async deleteById(id: string): Promise<any> {
        try {
            const result = await this.doctorModel.findByIdAndRemove(id, { select: '_id' }).exec();
            if (!result) {
                throw new HttpException('Thao tác xóa không thành công. Vui lòng kiểm tra lại.', HttpStatus.BAD_REQUEST);
            }
            return result;
        } catch (error) {
            throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async updateOne(updateDoctorDTO: UpdateDoctorDTO): Promise<any> {
        try {
            const { id, ...updateInfo } = updateDoctorDTO;
            return this.doctorModel
                .findByIdAndUpdate(updateDoctorDTO.id, { ...updateInfo }, { new: true })
                .exec();
        } catch (error) {
            throw new HttpException('Có lỗi xảy ra.', HttpStatus.BAD_REQUEST);
        }

    }

    async find(hospitalId: string): Promise<any> {
        const hospitals = await this.hospitaltModel.find({ _id: hospitalId }).limit(1).exec();
        if (hospitals.length === 0) {
            throw new HttpException('Không tìm thấy thông tin bệnh viện', HttpStatus.NOT_FOUND);
        }
        const hospital = first(hospitals);
        const doctors = await this.doctorModel
            .find({ hospitalId: hospital.id })
            .exec();
        return {
            hospital,
            doctors,
        };
    }
}
