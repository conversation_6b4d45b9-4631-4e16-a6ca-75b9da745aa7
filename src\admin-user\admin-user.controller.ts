import { Controller, Post, UseGuards, Body, HttpException, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { AdminUserService } from './admin-user.service';
import { AuthGuard } from '@nestjs/passport';
import { CreateUserPatientDTO } from './dto/create-user-patient.dto';
import { CreateAdminUserDTO } from './dto/create-admin-user.dto';

@Controller('admin-user')
@ApiTags('Người dùng - Quản trị')
export class AdminUserController {

    constructor(
        private readonly adminUserService: AdminUserService,
    ) { }

    @Post('list')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    async getAllUsersByFilter(): Promise<any> {
        return await this.adminUserService.getAllUsersByFilter();
    }

    @Post('permission-list')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    async getAllUsersAndPermissionByFilter(): Promise<any> {
        const users = await this.adminUserService.getAllUsersByFilter();
        const modules = await this.adminUserService.getAllModules();
        const resultModule = Object.assign({}, ...modules.map(s => ({ [s.id]: s.module_customize })));
        return users.map(item => {
            const splitModule = (item.list_module_id || '').split(',');
            const moduleList = (splitModule || []).map(key => {
                return {
                    module_id: key,
                    module_name: resultModule[key],
                };
            });

            return {
                ...item,
                modules: moduleList,
            };
        });
    }

    @Post('create-user-patient')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    async createNewUserPatient(@Body() createUserPatientDTO: CreateUserPatientDTO): Promise<any> {
        return this.adminUserService.createNewUserPatient(createUserPatientDTO);
    }

    // @Post('create-admin-user')
    // @ApiBearerAuth()
    // @UseGuards(AuthGuard('jwt'))
    // async createAdminUser(@Body() createAdminUserDTO: CreateAdminUserDTO): Promise<any> {
    //     return this.adminUserService.createAdminUser(createAdminUserDTO);
    // }

}
