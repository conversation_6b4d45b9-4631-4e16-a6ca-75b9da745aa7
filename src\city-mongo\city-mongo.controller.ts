import { Controller, Headers, Get, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { CityMongoService } from './city-mongo.service';
import { find } from 'lodash';

@Controller('city-mongo')
@ApiTags('Tỉnh thành - Quản lý danh mục Tỉnh thành trên MongoDB')
export class CityMongoController {
    constructor(
        private readonly cityMongoService: CityMongoService,
    ) { }

    @Get('get-all-by-partner')
    // tslint:disable-next-line: variable-name
    async find(@Headers('partnerid') partnerid: string, @Query('country_code') country_code: string): Promise<any> {
        const cities = await this.cityMongoService.find(partnerid, country_code);
        if (cities.length > 0) {
            const findHCMName = find(cities, { code: '79' });
            const findHNName = find(cities, { code: '01' });
            if (typeof findHCMName !== typeof undefined && typeof findHNName !== typeof undefined) {
                const hcm = { ...findHCMName.toJSON() };
                const hn = { ...findHNName.toJSON() };
                // tiếp tục
                const filter = cities.filter(item => item.id !== findHCMName.id && item.id !== findHNName.id);
                return [
                    hcm,
                    hn,
                    ...filter,
                ];
            }

            return cities;
        }
        return [];
    }

    @Get('seed-data')
    async seed(@Headers('partnerid') partnerid: string): Promise<any> {
        return this.cityMongoService.seed(partnerid);
    }
}
