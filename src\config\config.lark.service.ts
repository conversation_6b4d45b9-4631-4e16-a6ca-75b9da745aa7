import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';

export class ConfigLarkService extends ConfigManager {
    provideConfigSpec() {
        return {
            LARK_ENV: {
                validate: Joi.string(),
                required: true,
            },
            LARK_BOOKING_NOT_PAYMENT_YET_URL: {
                validate: Joi.string(),
                required: true,
            },
            LARK_RECEIVE_TRACKING_PATIENT_URL: {
                validate: Joi.string(),
                required: true,
            },
            LARK_RECEIVE_REPAYMENT_SUCCESS_URL: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    get larkEnv(): string {
        return this.get<string>('LARK_ENV');
    }

    get larkBookingNotPaymentYet(): string {
        return this.get<string>('LARK_BOOKING_NOT_PAYMENT_YET_URL');
    }

    get larkRepaymentSuccessUrl(): string {
        return this.get<string>('LARK_RECEIVE_REPAYMENT_SUCCESS_URL');
    }
    get pushDoctorNotLoggedInWarning(): string {
        return this.get<string>('LARK_DOCTOR_NOT_LOGGED_IN');
    }

    get larkReceiveSystemInfoUrl(): string {
        return this.get<string>('LARK_RECEIVE_SYSTEM_INFO_URL');
    }

    get trackingPatientActionInfo(): string {
        return this.get<string>('LARK_RECEIVE_TRACKING_PATIENT_URL');
    }

    get larkBookingSuccessPartnerNotYetSignedContract(): string {
        return this.get<string>('BOOKING_SUCCESS_PARTNER_NOT_YET_SIGNED_CONTRACT_LARK_GROUP');
    }

    get larkBookingFullSlotUmcUrl(): string {
        return this.get<string>('LARK_BOOKIG_FULL_SLOT_UMC_URL');
    }

    get larkNotifNhidong1(): string {
        return this.get<string>('LARK_NOTIF_NHIDONG1');
    }

    get larkBookingCompleteInfo(): string {
        return this.get<string>('BOOKING_SUCCESS_COMPLETE_INFO_LARK_GROUP');
    }

    get getLarkNotiOnTrackingMedproCare(): string {
        return this.get<string>("LARK_NOTIF_ON_TRACKING_MEDPRO_CARE") || "https://open.larksuite.com/open-apis/bot/v2/hook/4ed31f2b-47dc-48fb-a0d9-297def70bf5c"
    }
    
    get getLarkNotifPaymentError(): string {
        return this.get<string>("PAYMENT_GATEWAY_ISSUES_LARK_GROUP") || "https://open.larksuite.com/open-apis/bot/v2/hook/96c3ce6b-2f8b-4a34-8c9c-425a7b6a6f13"
    }
    get getLarkNotifSyncBookingFail(): string {
        return this.get<string>("SYNC_BOOKING_FAIL_LARK_GROUP") || "https://open.larksuite.com/open-apis/bot/v2/hook/96c3ce6b-2f8b-4a34-8c9c-425a7b6a6f13"
    }

    get getLarkCooperateMedpro(): string {
        return this.get<string>("LARK_COOPERATE_MEDPRO_URL") || "https://open.larksuite.com/open-apis/bot/v2/hook/b9e25c69-521b-47a7-8913-1b2d73897ab0"
    }

    get getLarkCooperateClinicMedpro(): string {
        return this.get<string>("LARK_DANG_KY_PHONG_MACH") || "https://open.larksuite.com/open-apis/bot/v2/hook/56f7a31d-17ee-4ceb-8a81-53b4d10996b9"
    }

    get getLarkEnterpriseRegistration(): string {
        return this.get<string>("LARK_ENTERPRISE_REGISTRATION_URL") || "https://open.larksuite.com/open-apis/bot/v2/hook/56f7a31d-17ee-4ceb-8a81-53b4d10996b9"
    }

    get getLarkRecruitment(): string {
        return this.get<string>("LARK_RECRUITMENT_URL") || "https://open.larksuite.com/open-apis/bot/v2/hook/56f7a31d-17ee-4ceb-8a81-53b4d10996b9"
    }

    get getLarkNotifTest(): string {
        return this.get<string>("LARK_NOTIF_TEST") || "https://open.larksuite.com/open-apis/bot/v2/hook/56f7a31d-17ee-4ceb-8a81-53b4d10996b9"
    }
}
