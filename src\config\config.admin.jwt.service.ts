import { JwtOptionsFactory, JwtModuleOptions } from '@nestjs/jwt';
import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';

@Injectable()
export class JwtAdminConfigService extends ConfigManager implements JwtOptionsFactory {

    provideConfigSpec() {
        return {
            ADMIN_JWT_SECRET: {
                validate: Joi.string(),
                required: true,
            },
            ADMIN_JWT_EXPIRES_IN: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    createJwtOptions(): JwtModuleOptions {
        return {
            secret: this.get<string>('ADMIN_JWT_SECRET'),
            signOptions: { expiresIn: this.get<string>('ADMIN_JWT_EXPIRES_IN') },
        };
    }
}
