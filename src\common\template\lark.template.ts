import * as moment from 'moment';

export function notifyDatabaseDisconnectTemplate(larkEnv: string, content: string, stack: string): Object {
    return {
        config: {
            wide_screen_mode: true,
        },
        elements: [
            {
                alt: {
                    content: '',
                    tag: 'plain_text',
                },
                img_key: 'img_v2_cb03ec35-a638-4b93-9e6f-5e2d0e549deg',
                tag: 'img',
            },
            {
                tag: 'hr',
            },
            {
                tag: 'column_set',
                flex_mode: 'none',
                background_style: 'default',
                columns: [
                    {
                        tag: 'column',
                        width: 'weighted',
                        weight: 1,
                        vertical_align: 'top',
                        elements: [
                            {
                                tag: 'div',
                                text: {
                                    tag: 'lark_md',
                                    content: `${content}`,
                                },
                                extra: {
                                    tag: 'img',
                                    img_key: 'img_v2_84f2ebbf-78cc-4b7e-b0b1-bfe820f05edh',
                                    alt: {
                                        tag: 'plain_text',
                                        content: '',
                                    },
                                    mode: 'fit_horizontal',
                                    compact_width: false,
                                },
                            },
                            {
                                tag: 'hr',
                            },
                        ],
                    },
                ],
            },
            {
                elements: [
                    {
                        content: `Detail: ${stack}`,
                        tag: 'plain_text',
                    },
                ],
                tag: 'note',
            },
        ],
        header: {
            template: 'green',
            title: {
                content: `📊 ${larkEnv} Health system report?`,
                tag: 'plain_text',
            },
        },
    };
}

export function larkMsgTrackingPatient({ cskhUser, patientUser, action, patient, partner }): Object {
    return {
        config: {
            wide_screen_mode: true,
        },
        elements: [
            {
                tag: 'div',
                text: {
                    content: `Hệ thống ghi nhận ${cskhUser?.isCS ? 'CSKH' : 'người dùng'} ${cskhUser.fullname} ( MedproId: ${
                        cskhUser.username
                    }) thực hiện thao tác ${action}.`,
                    tag: 'lark_md',
                },
            },
            {
                fields: [
                    ...(patient
                        ? [
                              {
                                  is_short: true,
                                  text: {
                                      content: `**👤Tên Hồ Sơ**\n${patient.surname} ${patient.name} - ${patient.code}`,
                                      tag: 'lark_md',
                                  },
                              },
                          ]
                        : []),
                    {
                        is_short: true,
                        text: {
                            content: `**👤Hành động**\n${action}`,
                            tag: 'lark_md',
                        },
                    },
                    ...(partner
                        ? [
                              {
                                  is_short: true,
                                  text: {
                                      content: `**🏥Cơ sở y tế**\n${partner.name}`,
                                      tag: 'lark_md',
                                  },
                              },
                          ]
                        : []),
                    {
                        is_short: true,
                        text: {
                            content: `**🗳Tài khoản **\n${patientUser.username}`,
                            tag: 'lark_md',
                        },
                    },
                    {
                        is_short: true,
                        text: {
                            content: `**👤Thời gian**\n${moment()
                                .utc()
                                .add(7, 'hours')
                                .format('DD-MM-YYYY HH:mm')}`,
                            tag: 'lark_md',
                        },
                    },
                ],
                tag: 'div',
            },
        ],
        header: {
            template: 'green',
            title: {
                content: `TRACKING PATIENT ACTION`,
                tag: 'plain_text',
            },
        },
    };
}
