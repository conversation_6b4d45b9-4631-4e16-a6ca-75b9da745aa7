import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';

@Injectable()
export class PaymentMethodRateConfigService extends ConfigManager {

    provideConfigSpec() {
        return {
            PAYMENT_METHOD_VP_RATE_ATM: {
                validate: Joi.number(),
                required: true,
            },
            PAYMENT_METHOD_VP_ADD_ATM: {
                validate: Joi.number(),
                required: true,
            },
            PAYMENT_METHOD_MEDPRO_VP_SERVICE_FEE: {
                validate: Joi.number(),
                required: true,
            },
            PAYMENT_METHOD_VP_RATE_INT_CARD: {
                validate: Joi.number(),
                required: true,
            },
            PAYMENT_METHOD_VP_ADD_INT_CARD: {
                validate: Joi.number(),
                required: true,
            },
        };
    }

    getPaymentMethodRate(): any {

        return {
            vp_rate_atm: this.get<string>('PAYMENT_METHOD_VP_RATE_ATM'),
            vp_add_atm: this.get<string>('PAYMENT_METHOD_VP_ADD_ATM'),
            vp_sevice_fee: this.get<string>('PAYMENT_METHOD_MEDPRO_VP_SERVICE_FEE'),
            vp_rate_int_card: this.get<string>('PAYMENT_METHOD_VP_RATE_INT_CARD'),
            vp_add_int_card: this.get<string>('PAYMENT_METHOD_VP_ADD_INT_CARD'),
            vp_rate_offline: this.get<string>('PAYMENT_METHOD_VP_RATE_OFFLINE'),
            vp_rate_qr: this.get<string>('PAYMENT_METHOD_VP_RATE_QR'),
            vp_add_qr: this.get<string>('PAYMENT_METHOD_VP_ADD_QR'),
        };

    }
}
