import {
    Body,
    CacheInterceptor,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    CACHE_MANAGER,
    Controller,
    Delete,
    Get,
    HttpException,
    HttpStatus,
    Inject,
    Post,
    Put,
    Query,
    UseInterceptors,
} from '@nestjs/common';
import { Cache } from 'cache-manager';
import { AUTO_KEY, SECOND, VALUE_KEY } from './cache-manager.constant';
import {
    UpdateValueCache,
    ValueCache,
} from './interface/cache-manager.interface';

@Controller('/cache-manager')
export class CacheManagerController {
    constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

    @Post()
    async createCache(@Body() body: ValueCache): Promise<any> {
        try {
            return await this.cacheManager.set(
                VALUE_KEY,
                { ...body },
                { ttl: 5 * SECOND },
            );
        } catch (error) {
            throw new HttpException(
                `Lỗi Server : ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    @Get()
    async getCache(@Query(`cache`) cache: string): Promise<any> {
        try {
            return await this.cacheManager.get(cache);
        } catch (error) {
            throw new HttpException(
                `Lỗi Server : ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    @Get(`/auto`)
    @CacheKey(AUTO_KEY)
    @CacheTTL(5 * SECOND)
    @UseInterceptors(CacheInterceptor)
    async testAutoCache(): Promise<any> {
        try {
            return [1, 2, 3, 4, 5, 6, 7, 8];
        } catch (error) {
            throw new HttpException(
                `Lỗi Server : ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    @Put()
    async updateCache(@Body() body: UpdateValueCache): Promise<any> {
        try {
            const { key, ...data } = body;
            return await this.cacheManager.set(
                key,
                { ...data },
                { ttl: 5 * SECOND },
            );
        } catch (error) {
            throw new HttpException(
                `Lỗi Server : ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    @Delete()
    async deleteCache(@Query(`cache`) cache: string): Promise<any> {
        try {
            await this.cacheManager.del(VALUE_KEY);
            return { message: 'Xóa thành công' };
        } catch (error) {
            throw new HttpException(
                `Lỗi Server : ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    @Delete(`/reset`)
    async resetAllCache(): Promise<any> {
        try {
            await this.cacheManager.reset();
            return { message: 'Reset thành công' };
        } catch (error) {
            throw new HttpException(
                `Lỗi Server : ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }
}
