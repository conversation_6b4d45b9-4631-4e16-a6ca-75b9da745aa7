import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

export class AddFeatureIntoPartnerDTO {
    @ApiProperty({ description: 'appId', required: true, type: String })
    @IsNotEmpty({ message: 'appId is required' })
    @Transform(value => `${value}`.trim())
    appId: string;

    @ApiProperty({ description: 'partnerId', required: true, type: String })
    @IsNotEmpty({ message: 'partnerId is required' })
    @Transform(value => `${value}`.trim())
    partnerId: string;

    @ApiProperty({ description: 'featureId', required: true, type: String })
    @IsNotEmpty({ message: 'featureId is required' })
    @Transform(value => `${value}`.trim())
    featureId: string;
}
