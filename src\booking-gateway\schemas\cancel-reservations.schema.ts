import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { BOOKING_COLLECTION_NAME, CANCEL_RESERVATIONS_COLLECTION_NAME } from './constants';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';

const Schema = mongoose.Schema;

export const CancelReservationsSchema = new Schema(
    {
        partnerId: { type: String },
        appId: { type: String },
        user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        username: { type: String },
        bookingDate: { type: Date },
        transactionId: { type: String },
        cskh: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        userAction: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
        id: { type: String },
        smsCode: { type: String },
        cancelledBy: { type: String },
    },
    {
        collection: CANCEL_RESERVATIONS_COLLECTION_NAME,
        timestamps: true,
    },
).plugin(jsonMongo);
