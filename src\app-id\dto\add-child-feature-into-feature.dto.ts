import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

export class AddChildrenFeatureDTO {
    @ApiProperty({ description: 'appId', type: String, required: true })
    @IsNotEmpty({ message: 'appId is required' })
    @Transform(value => `${value}`.trim())
    appId: string;

    @ApiProperty({ description: 'partnerId', type: String, required: true })
    @IsNotEmpty({ message: 'partnerId is required' })
    @Transform(value => `${value}`.trim())
    partnerId: string;

    @ApiProperty({ description: 'featureId', type: String, required: true })
    @IsNotEmpty({ message: 'featureId is required' })
    @Transform(value => `${value}`.trim())
    featureId: string;

    @ApiProperty({ description: 'childFeatureId', type: String, required: true })
    @IsNotEmpty({ message: 'childFeatureId is required' })
    @Transform(value => `${value}`.trim())
    childFeatureId: string;
}
