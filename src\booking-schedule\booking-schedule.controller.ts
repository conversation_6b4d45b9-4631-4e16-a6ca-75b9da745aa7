import { Controller, Get, Query } from '@nestjs/common';
import { ApiOperation } from '@nestjs/swagger';
import { BookingScheduleService } from './booking-schedule.service';
import { GetAvailableDoctorDto } from './dto/get-available-doctor.dto';

@Controller('booking-schedule')
export class BookingScheduleController {
    constructor(private readonly bookingScheduleService: BookingScheduleService) {}
    @Get('booking-available-doctor')
    @ApiOperation({ summary: 'L<PERSON>y danh sách bác sĩ cho phép đặt khám' })
    async getListBookingAvailable(@Query() formData: GetAvailableDoctorDto): Promise<any> {
        return this.bookingScheduleService.getAvailableDoctor(formData);
    }
}
