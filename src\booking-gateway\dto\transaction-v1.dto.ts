
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class TransactionV1DTO {

    @ApiProperty({
        description: 'transactionIdV1',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    transactionIdV1: string;

    @ApiProperty({
        description: 'gatewayId',
        required: true,
        type: Number,
    })
    @Transform(value => Number(value))
    methodIdV1: number;

}
