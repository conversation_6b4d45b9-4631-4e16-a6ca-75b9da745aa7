import { AddPartnersUserCskh } from './dto/add-partners-user-cskh.dto';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { isEmpty } from 'lodash';
import { Model } from 'mongoose';
import { Pagination } from 'src/common/base/pagination';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { IUser } from 'src/user/interfaces/user.interface';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { GetUsersCskhDto } from './dto/get-users-cskh.dto';
import { QueryUserCskh } from './dto/query-user-cskh.dto';
import { SearchUserCskh } from './dto/search-user-cskh.dto';
import { UpdateCskhDto } from './dto/update-cskh.dto';
import { QueryOneUserCskh } from './dto/query-one-user-cskh.dto';

@Injectable()
export class CskhService {
    private logger = new Logger(CskhService.name);

    constructor(
        @InjectModel(USER_COLLECTION_NAME) private readonly userModel: Model<IUser>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private readonly hospitalModel: Model<IHospital>,
    ) {}

    async getAllUserCskh(query: QueryUserCskh): Promise<GetUsersCskhDto> {
        try {
            const { pageIndex = 0, pageSize = 5, isCS = true, ...queryData } = query;
            const filter = {
                ...queryData,
                isCS,
            };
            const [rows, totalRows] = await Promise.all([
                this.userModel
                    .find(filter)
                    .skip(pageIndex)
                    .limit(pageSize)
                    .exec(),
                this.userModel.countDocuments(filter),
            ]);
            return {
                pageIndex,
                pageSize,
                totalRows,
                rows,
            };
        } catch (error) {
            this.logger.error(`Error when exec getAllUserCskh()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async searchUserCskhByFullname(search: SearchUserCskh): Promise<GetUsersCskhDto> {
        try {
            const { pageIndex = 0, pageSize = 5, ...searchData } = search;
            const text = !isEmpty(search)
                ? {
                      username: {
                          $regex: searchData.phone,
                          $options: 'i',
                      },
                  }
                : {};
            const filter = {
                ...text,
            };
            const [rows, totalRows] = await Promise.all([
                this.userModel
                    .find(filter)
                    .skip(pageIndex)
                    .limit(pageSize)
                    .exec(),
                this.userModel.countDocuments(filter),
            ]);
            return {
                pageIndex,
                pageSize,
                totalRows,
                rows,
            };
        } catch (error) {
            this.logger.error(`Error when exec searchUserCskhByFullname()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async updateCskh(id: string, formData: UpdateCskhDto): Promise<IUser> {
        try {
            const findUser = await this.userModel.findById(id);
            if (!findUser) {
                throw new HttpException(`User với id: ${id} không tồn tại`, 404);
            }
            await this.userModel.findByIdAndUpdate(id, formData);
            return this.userModel.findById(id);
        } catch (error) {
            this.logger.error(`Error when exec updateCskh()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async insertPartnerIntoUser(userMongoId: string, formData: AddPartnersUserCskh): Promise<IUser> {
        try {
            const { partnerMongoIds } = formData;
            const findUser = await this.userModel.findById(userMongoId);
            if (!findUser) {
                throw new HttpException(`User với id: ${userMongoId} không tồn tại !`, HttpStatus.NOT_FOUND);
            }
            if (!findUser.isCS) {
                throw new HttpException(`User này không thuộc Chăm sóc khách hàng !`, HttpStatus.BAD_REQUEST);
            }
            const partners = await this.hospitalModel.find({ _id: { $in: partnerMongoIds }});
            await this.userModel.findByIdAndUpdate(findUser._id, { partners });
            return this.userModel.findById(findUser._id);
        } catch (error) {
            this.logger.error(`Error when exec addPartnersToUserCskh()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async getOneByCondition(query: QueryOneUserCskh): Promise<IUser> {
        try {
            return this.userModel.findOne({ ...query }).exec();
        } catch (error) {
            this.logger.error(`Error when exec getOneByCondition()\nError: ${error.message || error?.response.data.message} `);
            const errorMessage = error?.response.data.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.response.data.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async getById(id: string): Promise<IUser> {
        try {
            return this.userModel.findById(id).exec();
        } catch (error) {
            this.logger.error(`Error when exec getById()\nError: ${error.message || error?.response.data.message} `);
            const errorMessage = error?.response.data.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.response.data.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }
}
