import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsDateString, IsNotEmpty, ValidateIf } from 'class-validator';

export class ListenBookingDateChangedDTO {

    @ApiProperty({
        description: 'id của booking',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    bookingId: string;

    @ApiProperty({
        description: 'Format kiểu ISO String',
        required: false,
        type: String,
        // default: '2020-06-03T02:12:07.381Z',
    })
    @IsDateString({ strict: true }, {
        message: 'Thông tin ngày đặt khám. ISOString',
    })
    @ValidateIf(o => o.date)
    date: string;

    @ApiProperty({
        description: 'Id Bác sĩ',
        required: false,
        type: String,
        default: 'doctor0001',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.doctorId)
    doctorId: string;

    @ApiProperty({
        description: 'Id phòng khám',
        required: false,
        type: String,
        default: 'room001',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.roomId)
    roomId: string;

    @ApiProperty({
        description: 'Id chuyên khoa',
        required: false,
        type: String,
        default: 'subject0001',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.subjectId)
    subjectId: string;

    @ApiProperty({
        description: 'Service Id',
        type: String,
        default: 'serviceId',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.subjectId)
    serviceId: string;
}
