import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { AdminUserService } from '../admin-user/admin-user.service';
import * as md5 from 'md5';

@Injectable()
export class AuthService {
    constructor(
        private readonly adminUserService: AdminUserService,
        private readonly jwtService: JwtService,
    ) { }

    async validateUser(email: string, pass: string): Promise<any> {
        const user = await this.adminUserService.findOne(email);
        const md5Salt = md5(pass + user.salt);
        if (user && user.password === md5Salt) {
            const { password, ...result } = user;
            return result;
        }
        return null;
    }

    async login(user: any) {
        const payload = { email: user.email, sub: user.id };
        return {
            access_token: this.jwtService.sign(payload),
        };
    }
    async checkEmail(email: string): Promise<any> {
        const user = await this.adminUserService.findUserByEmail(email);
        if (user) {
            const { password, ...result } = user;
            return true;
        }
        return false;
    }
}
