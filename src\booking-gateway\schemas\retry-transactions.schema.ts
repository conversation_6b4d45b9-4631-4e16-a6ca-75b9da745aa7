import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import * as mongoose from 'mongoose';
import { RETRY_TRASACTION_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const RetryTransactionSchema = new Schema(
    {
        dataPaymentUpdate: {
            transactionId: String,
            status: Number,
            message: String,
        },
        constraint: { type: String, unique: true },
        booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
        status: { type: Number, default: 0 },
    },
    {
        collection: RETRY_TRASACTION_COLLECTION_NAME,
        timestamps: true,
    },
);
