import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {  IsPositive } from 'class-validator';

export class GetAvailableDoctorDto {
    @ApiProperty({ description: 'pageSize', type: Number, required: false, default: 10 })
    @Transform(value => Number(value))
    @IsPositive({ message: 'pageSize must be positive value' })
    readonly pageSize: number;

    @ApiProperty({ description: 'pageIndex', type: Number, required: false, default: 0 })
    @Transform(value => Number(value))
    readonly pageIndex: number;

    @ApiProperty({description: 'Tên bác s<PERSON>', type: String, required: false})
    @Transform(value => value.trim())
    readonly name: string;

    @ApiProperty({description: 'partnerId', type: String, required: false})
    @Transform(value => value.trim())
    readonly partnerId: string;
}
