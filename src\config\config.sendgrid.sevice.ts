import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';
import * as admin from 'firebase-admin';
import { SendgridOptions } from './sendgridConnection';

@Injectable()
export class SendgridConfigService extends ConfigManager {
    provideConfigSpec() {
        return {
            SENDGRID_API_KEY: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    setApiKey(): SendgridOptions {
        return {
            apiKey: this.get<string>('SENDGRID_API_KEY'),
        };
    }

    getListUserRevieveError() {
        return [
            // this.get<string>('SENDGRID_EMAIL_RECEIVE_ERROR_1'),
            this.get<string>('SENDGRID_EMAIL_RECEIVE_ERROR_2'),
            // this.get<string>('SENDGRID_EMAIL_RECEIVE_ERROR_3'),
        ];
    }

    getApiKey(): string {
        return this.get<string>('SENDGRID_API_KEY');
    }
}
