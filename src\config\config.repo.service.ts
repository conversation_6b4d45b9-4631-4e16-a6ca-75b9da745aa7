import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';
import { KnexOptions } from './pkhConnection/index';

@Injectable()
export class ConfigRepoService extends ConfigManager {
    provideConfigSpec() {
        return {
            REPO_NAME: {
                validate: Joi.string(),
                required: false,
                default: 'medpro-admin-api',
            },
            TELEMED_SECRET_KEY: {
                validate: Joi.string(),
                required: false,
                default: 'medpro-telemed-api',
            },
        };
    }

    getRepoName(): string {
        return this.get<string>('REPO_NAME');
    }

    getTelemedSecret(): string {
        return this.get<string>('TELEMED_SECRET_KEY');
    }

    getRepoNameValidRunSync(): string {
        return this.get<string>('REPO_NAME_RUN_SYNC');
    }
}
