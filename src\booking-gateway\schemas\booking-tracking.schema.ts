import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { BOOKING_TRACKING_COLLECTION_NAME } from './constants';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';

const Schema = mongoose.Schema;

export const BookingTrackingSchema = new Schema({
    bookingId: { type: String },
    booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
    prevBooking: { type: Schema.Types.Mixed },
    bookingChanged: { type: Schema.Types.Mixed },
    user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
}, {
    collection: BOOKING_TRACKING_COLLECTION_NAME,
    timestamps: true,
    versionKey: false,
}).plugin(jsonMongo);
