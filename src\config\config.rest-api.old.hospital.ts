import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';
import { MappingPartnerIdDTO } from './dto/mapping-api.dto';

@Injectable()
export class RestfulAPIOldHospitalConfigService extends ConfigManager {

    provideConfigSpec() {
        return {
            BASE_URL: {
                validate: Joi.string(),
                required: true,
            },
            API_URL_MEDPRO: {
                validate: Joi.string(),
                required: true,
            },
            API_URL_UMC: {
                validate: Joi.string(),
                required: true,
            },
            API_URL_DA_LIEU: {
                validate: Joi.string(),
                required: true,
            },
            API_URL_NHI_DONG_1: {
                validate: Joi.string(),
                required: true,
            },
            API_URL_CHAN_THUONG_CHINH_HINH: {
                validate: Joi.string(),
                required: true,
            },
            API_URL_MINH_ANH: {
                validate: Joi.string(),
                required: true,
            },
            API_URL_THU_DUC: {
                validate: Joi.string(),
                required: true,
            },
            API_URL_CHO_RAY: {
                validate: Joi.string(),
                required: true,
            },
            API_URL_BINH_DAN: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    getBaseUrl(): string {
        return this.get<string>('BASE_URL');
    }

    MedproRestfulAPI(): string {
        return this.get<string>('API_URL_MEDPRO');
    }

    UMCRestfulAPI(): string {
        return this.get<string>('API_URL_UMC');
    }

    DalieuRestfulAPI(): string {
        return this.get<string>('API_URL_DA_LIEU');
    }

    NhiDong1RestfulAPI(): string {
        return this.get<string>('API_URL_NHI_DONG_1');
    }

    NhiDong1GetPatients(): string {
        const baseUrl = this.NhiDong1RestfulAPI();
        return `${baseUrl}/patient/getbyuserid`;
    }

    NhiDong1InsertPatients(): string {
        const baseUrl = this.NhiDong1RestfulAPI();
        return `${baseUrl}/patient/insert`;
    }

    NhiDong1GetByMSBN(): string {
        const baseUrl = this.NhiDong1RestfulAPI();
        return `${baseUrl}/patient/getbymsbn`;
    }

    NhiDong1UpdatePatients(): string {
        const baseUrl = this.NhiDong1RestfulAPI();
        return `${baseUrl}/patient/update`;
    }

    NhiDong1GetBookings(): string {
        const baseUrl = this.NhiDong1RestfulAPI();
        return `${baseUrl}/booking/getbyuserid`;
    }

    NhiDong1InsertMulti(): string {
        const baseUrl = this.NhiDong1RestfulAPI();
        return `${baseUrl}/booking/insertmulti`;
    }

    NhiDong1UpdateMulti(): string {
        const baseUrl = this.NhiDong1RestfulAPI();
        return `${baseUrl}/booking/update`;
    }

    NhiDong1CancelBooking(): string {
        const baseUrl = this.NhiDong1RestfulAPI();
        return `${baseUrl}/booking/cancel`;
    }

    NhiDong1GetBookingInfo(): string {
        const baseUrl = this.NhiDong1RestfulAPI();
        return `${baseUrl}/booking/detail`;
    }

    NhiDong1CheckOrder(): string {
        const baseUrl = this.NhiDong1RestfulAPI();
        return `${baseUrl}/payment/check_order`;
    }

    ChanThuongChinhHinhRestfulAPI(): string {
        return this.get<string>('API_URL_CHAN_THUONG_CHINH_HINH');
    }

    MinhAnhRestfulAPI(): string {
        return this.get<string>('API_URL_MINH_ANH');
    }

    ThuDucRestfulAPI(): string {
        return this.get<string>('API_URL_THU_DUC');
    }

    ChoRayRestfulAPI(): string {
        return this.get<string>('API_URL_CHO_RAY');
    }

    BinhDanRestfulAPI(): string {
        return this.get<string>('API_URL_BINH_DAN');
    }

    umcMappingUserPatientUrl(): string {
        const baseUrl = this.NhiDong1RestfulAPI();
        return `${baseUrl}/patient/mapping_user_patient`;
    }

    getRestfulAPIOldHospital(partnerId: string): MappingPartnerIdDTO {
        const upperCas = `${partnerId}`.toUpperCase();
        let apiUrl = '';
        let isOld = true;
        let hospitalId = 0;
        switch (upperCas) {
            case 'MEDPRO':
                apiUrl = this.MedproRestfulAPI();
                hospitalId = 0;
                break;
            case 'UMC':
                apiUrl = this.UMCRestfulAPI();
                hospitalId = 2;
                isOld = false;
                break;
            case 'DALIEU':
                apiUrl = this.DalieuRestfulAPI();
                hospitalId = 3;
                isOld = false;
                break;
            case 'NHIDONG1':
                apiUrl = this.NhiDong1RestfulAPI();
                hospitalId = 4;
                isOld = false;
                break;
            case 'MINHANH':
                apiUrl = this.MinhAnhRestfulAPI();
                hospitalId = 5;
                isOld = false;
                break;
            case 'CTCH':
                apiUrl = this.ChanThuongChinhHinhRestfulAPI();
                hospitalId = 6;
                isOld = false;
                break;
            case 'THUDUC':
                apiUrl = this.ThuDucRestfulAPI();
                hospitalId = 7;
                isOld = false;
                break;
            case 'CHORAY':
                apiUrl = this.ChoRayRestfulAPI();
                hospitalId = 8;
                isOld = false;
                break;
            case 'BINHDAN':
                apiUrl = this.BinhDanRestfulAPI();
                hospitalId = 9;
                isOld = false;
                break;
            default:
                apiUrl = this.UMCRestfulAPI();
                isOld = false;
                break;
        }
        return {
            apiUrl,
            isOld,
            hospitalId,
        };

    }

}
