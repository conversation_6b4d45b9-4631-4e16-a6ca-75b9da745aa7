import { Injectable, Inject, HttpException, HttpStatus } from '@nestjs/common';
import { PKH_CONNECTION } from '../config/pkhConnection';
import { UserService } from 'src/user/user.service';
import * as moment from 'moment';
import { CreateUserPatientDTO } from './dto/create-user-patient.dto';
import { UtilService } from 'src/config/util.service';
import { CreateAdminUserDTO } from './dto/create-admin-user.dto';

@Injectable()
export class AdminUserService {

    constructor(
        @Inject(PKH_CONNECTION) private readonly pkhKnex,
        private readonly userService: UserService,
        private readonly utilService: UtilService,
    ) { }

    async createAdminUser(createAdminUserDTO: CreateAdminUserDTO): Promise<any> {
        return true;
    }

    async createNewUserPatient(createUserPatientDTO: CreateUserPatientDTO): Promise<any> {
        /* <PERSON><PERSON><PERSON> tra xem đã có username này hay chưa */
        const findUser = await this.userService.checkExistsUserByUsername(createUserPatientDTO.username);
        if (findUser) {
            throw new HttpException('Username đã tồn tại!', HttpStatus.CONFLICT);
        } else {
            const salt = this.userService.generateSalt();
            const hashpwd = this.userService.generateHashPwd(createUserPatientDTO.password, salt);
            const currentTime = moment().format('YYYY-MM-DD HH:mm:ss');
            const [userId] = await this.userService.createUser({
                username: createUserPatientDTO.username,
                hashpwd,
                salt,
                email: createUserPatientDTO.username,
                fullname: createUserPatientDTO.fullname,
                date_create: currentTime,
                date_update: currentTime,
            });
            return this.userService.getUserInfoByUserId(userId);
        }
    }

    async createNewUserPatientSupportAutoGenerate(createUserPatientDTO: CreateUserPatientDTO): Promise<any> {
        /* Kiểm tra xem đã có username này hay chưa */
        const findUser = await this.userService.checkExistsUserByUsername(createUserPatientDTO.username);
        if (findUser) {
            return findUser;
        } else {
            const salt = this.userService.generateSalt();
            const hashpwd = this.userService.generateHashPwd(createUserPatientDTO.password, salt);
            const currentTime = moment().format('YYYY-MM-DD HH:mm:ss');
            const [userId] = await this.userService.createUser({
                username: createUserPatientDTO.username,
                hashpwd,
                salt,
                email: createUserPatientDTO.username,
                fullname: createUserPatientDTO.fullname,
                date_create: currentTime,
                date_update: currentTime,
            });
            return this.userService.getUserInfoByUserId(userId);
        }
    }

    async findOne(email: string): Promise<any> {
        return this.pkhKnex.select().from('user').where('email', email).first();
    }

    async findUserByEmail(email: string): Promise<any> {
        return this.pkhKnex.select().from('user').where('email', email).first();
    }

    async getAllUsersByFilter(): Promise<any> {
        const medprodb = 'medprodb';
        const hospitalTable = `${medprodb}.hospital`;
        return this.pkhKnex.select(
            'user.id as userId',
            'email',
            'fullname',
            'user.address as  userAddress',
            'list_module_id',
            'hospital_name as clinicName',
            'hospital_id',
            `${hospitalTable}.name as hospitalName`,
            'supper',
            'phone',
            'is_partner',
            'active',
            'createdate',
            'updatedate',

        )
            .from('user')
            .leftJoin(hospitalTable, `${hospitalTable}.id`, `user.hospital_id`)
            .offset(0).limit(10);
    }

    async getAllModules(): Promise<any> {
        return this.pkhKnex.select()
            .from('module');

    }

    generateSaltPassword = (vLength = 30) => {
        const alphabet = 'abcdefghijklmnopqrstuwxyzABCDEFGHIJKLMNOPQRSTUWXYZ0123456789';
        let pass = [];
        const alphaLength = alphabet.length - 1;
        for (let i = 0; i < vLength; i++) {
            const n = this.utilService.getRandomInt(0, alphaLength);
            pass = [...pass, alphabet[n]];
        }
        return pass.join('');
    }
}
