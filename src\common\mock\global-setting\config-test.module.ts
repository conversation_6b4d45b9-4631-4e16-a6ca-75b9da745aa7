import { Module, Global, HttpModule } from '@nestjs/common';
import { ConfigManagerModule } from '@nestjsplus/config';
import { UrlConfigService } from './config-test.url.service';

@Global()
@Module({
    imports: [
        ConfigManagerModule.register({
            useEnv: {
                folder: 'config',
            },
            allowExtras: true,
        }),
        HttpModule,
    ],
    providers: [
        UrlConfigService,
    ],
    exports: [
        UrlConfigService,
    ],
})
export class ConfigTestModule {}
