import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';
import { KnexOptions } from './thuDucHospitalConnection/index';

@Injectable()
export class ChoRayHospitalConfigService extends ConfigManager {
    provideConfigSpec() {
        return {
            CHO_RAY_HOSPITAL_HOST: {
                validate: Joi.string(),
                required: false,
                default: 'localhost',
            },
            CHO_RAY_HOSPITAL_PORT: {
                validate: Joi.number(),
                required: false,
                default: 5432,
            },
            CHO_RAY_HOSPITAL_USER: {
                validate: Joi.string(),
                required: true,
            },
            CHO_RAY_HOSPITAL_DATABASE: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    createKnexOptions(): KnexOptions {
        return {
            client: 'mysql',
            debug: false,
            connection: {
                host: this.get<string>('CHO_RAY_HOSPITAL_HOST'),
                user: this.get<string>('CHO_RAY_HOSPITAL_USER'),
                password: this.get<string>('CHO_RAY_HOSPITAL_PASSWORD'),
                database: this.get<string>('CHO_RAY_HOSPITAL_DATABASE'),
                port: this.get<number>('CHO_RAY_HOSPITAL_PORT'),
            },
        };
    }
}
