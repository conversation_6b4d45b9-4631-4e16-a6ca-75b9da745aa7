import { Controller, Post, UseGuards, HttpCode, Headers, Body } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { BookingCovid19DTO } from 'src/booking-gateway/dto/booking-covid19.dto';
import { BookingCovidService } from './booking-covid.service';

@Controller('booking-covid')
export class BookingCovidController {

    constructor(
        private readonly service: BookingCovidService,
    ) { }

    @Post('covid-19')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({ summary: 'Danh sách Booking xét nghiệm covid-19' })
    @HttpCode(200)
    async getBookingExaminationCovid19(@Headers('partnerid') partnerId: string, @Body() fromData: BookingCovid19DTO): Promise<any> {
        return this.service.allBookingCovid19(partnerId, fromData);
    }
}
