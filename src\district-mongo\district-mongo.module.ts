import { Module } from '@nestjs/common';
import { DistrictMongoController } from './district-mongo.controller';
import { DistrictMongoService } from './district-mongo.service';
import { MongooseModule } from '@nestjs/mongoose';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { COUNTRY_COLLECTION_NAME } from 'src/country-mongo/schemas/constants';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { CitySchema } from 'src/city-mongo/schemas/city.schema';
import { CountrySchema } from 'src/country-mongo/schemas/country.schema';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { DISTRICT_COLLECTION_NAME } from './schemas/constants';
import { DistrictSchema } from './schemas/district.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: DISTRICT_COLLECTION_NAME, schema: DistrictSchema },
      { name: CITY_COLLECTION_NAME, schema: CitySchema },
      { name: COUNTRY_COLLECTION_NAME, schema: CountrySchema },
      { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
    ]),
  ],
  controllers: [DistrictMongoController],
  providers: [DistrictMongoService],
})
export class DistrictMongoModule { }
