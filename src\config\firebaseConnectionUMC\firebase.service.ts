import { Injectable, Inject, Logger } from '@nestjs/common';
import { FIREBASE_SERVICE_ACCOUNT_UMC } from './constants';
import { FirebaseOptions } from './interfaces';
import * as admin4 from 'firebase-admin';
import * as uuid from 'uuid';

interface IFirebaseService {
    createServiceAccount();
}

@Injectable()
export class FirebaseServiceUMC implements IFirebaseService {
    private readonly logger: Logger;
    // tslint:disable-next-line: variable-name
    private _firebaseConnectionUMC: any;
    // tslint:disable-next-line: variable-name
    constructor(@Inject(FIREBASE_SERVICE_ACCOUNT_UMC) private _firebaseOptions: FirebaseOptions) {
        this.logger = new Logger('FirebaseService');
        this.logger.log(`Options: ${JSON.stringify(this._firebaseOptions)}`);
    }

    createServiceAccount() {
        if (!this._firebaseConnectionUMC) {
            this._firebaseConnectionUMC = admin4.initializeApp(this._firebaseOptions, uuid.v4());
        }
        return this._firebaseConnectionUMC;
    }
}
