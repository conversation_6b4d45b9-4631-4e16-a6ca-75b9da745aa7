import { ApiProperty } from '@nestjs/swagger';

export class CreateUserPatientDTO {
    @ApiProperty({
        description: 'Username',
        required: true,
        type: 'string',
    })
    readonly username: string;
    @ApiProperty({
        description: '<PERSON><PERSON>t khẩu đăng nhập',
        required: true,
        type: 'string',
    })
    readonly password: string;
    @ApiProperty({
        description: 'Họ và tên',
        required: true,
        type: 'string',
    })
    readonly fullname: string;
}
