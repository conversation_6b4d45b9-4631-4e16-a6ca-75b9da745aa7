import { ConfigModule } from 'src/config/config.module';
import { AuditLogController } from './audit-log.controller';
import { AUDIT_LOG_NAME, SERVICE_LOG_NAME } from './constant';
import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';
import { AuditLogSchema } from './schemas/audit-log.schema';
import { AuditLogService } from './audit-log.service';
import { ServiceLogSchema } from './schemas/service-log.schema';

@Module({
    imports: [
        ConfigModule,
        MongooseModule.forFeature([
            { name: AUDIT_LOG_NAME, schema: AuditLogSchema },
            { name: SERVICE_LOG_NAME, schema: ServiceLogSchema },
        ]),
    ],
    controllers: [AuditLogController],
    providers: [AuditLogService],
})
export class AuditLogModule {}
