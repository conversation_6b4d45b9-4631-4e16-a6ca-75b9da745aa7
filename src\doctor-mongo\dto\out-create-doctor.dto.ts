
import { ApiProperty } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';
import { CreateDoctorDTO } from './create-doctor.dto';

export class OutCreateDoctorDTO extends CreateDoctorDTO {

    @ApiProperty({
        description: '<PERSON>ui lòng nhập Id',
        required: true,
        type: String,
    })
    readonly id: string;

    @ApiProperty({
        description: 'Ngày tạo',
        required: false,
        type: String,
    })
    @Exclude()
    readonly createdAt: string;

    @ApiProperty({
        description: 'Ngày chỉnh sửa',
        required: false,
        type: String,
    })
    @Exclude()
    readonly updatedAt: string;

}
