import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';
import { UpdateFeatureDTO } from '../../feature-mongo/dto/update-feature.dto';

export class UpdatePartnerFeatureDto extends UpdateFeatureDTO {
    @ApiProperty({ description: 'appId', required: true, type: String })
    @IsNotEmpty({ message: 'appId is required' })
    @Transform(value => `${value}`.trim())
    appId: string;

    @ApiProperty({ description: 'partnerId', required: true, type: String })
    @IsNotEmpty({ message: 'partnerId is required' })
    @Transform(value => `${value}`.trim())
    partnerId: string;

    @ApiProperty({ description: 'id của feature', required: true, type: String })
    @IsNotEmpty({ message: 'id is required' })
    @Transform(value => `${value}`.trim())
    id: string;
}
