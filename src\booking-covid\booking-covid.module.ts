import { Module, HttpModule } from '@nestjs/common';
import { BookingCovidService } from './booking-covid.service';
import { BookingCovidController } from './booking-covid.controller';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { BookingSchema } from 'src/booking-gateway/schemas/booking.schema';
import { JwtUserYTeConfigService } from 'src/config/config.user-yte.jwt.service';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';

@Module({
  imports: [
    HttpModule,
    PassportModule.register({ defaultStrategy: 'user-cbyt-jwt' }),
    JwtModule.registerAsync({
      useExisting: JwtUserYTeConfigService,
    }),
    MongooseModule.forFeature([
      { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
    ]),
  ],
  providers: [BookingCovidService],
  controllers: [BookingCovidController]
})
export class BookingCovidModule {}
