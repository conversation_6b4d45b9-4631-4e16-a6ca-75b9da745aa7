import { IPatientVersion } from './../patient-mongo/intefaces/patient-version.inteface';
import { PATIENT_TRACKING_COLLECTION_NAME, PATIENT_VERSION_COLLECTION_NAME } from './../patient-mongo/schemas/constants';
import { IMailReceiver } from 'src/booking-gateway/intefaces/mail-receiver.interface';
import { MAIL_RECEIVER_COLLECTION_NAME } from './../booking-gateway/schemas/constants';
import { TrackingCancelReserveDto } from './dto/tracking-cancel-reserve.dto';
import { HttpException, HttpService, HttpStatus, Inject, Injectable, Logger } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { chunk, get, isEmpty, last, map, maxBy, reduce, uniq } from 'lodash';
import * as moment from 'moment';
import { Model } from 'mongoose';
import { CreateMessageEventDataMailDto } from 'src/booking-gateway/dto/create-message-event-data-mail.dto';
import { CreateMessageEventPushDto } from 'src/booking-gateway/dto/create-message-event-push.dto';
import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { IHospitalFee } from 'src/booking-gateway/intefaces/hospital-fee.inteface';
import { IPayment } from 'src/booking-gateway/intefaces/payment.inteface';
import {
    BOOKING_COLLECTION_NAME,
    CANCEL_RESERVATIONS_COLLECTION_NAME,
    CRON_REMIDER_BOOKING_NOT_PAYMENT,
    HOSPITAL_FEE_COLLECTION_NAME,
    PAYMENT_COLLECTION_NAME,
    RETRY_TRASACTION_COLLECTION_NAME,
} from 'src/booking-gateway/schemas/constants';
import { RestfulAPIOldHospitalConfigService } from 'src/config/config.rest-api.old.hospital';
import { SendgridConfigService } from 'src/config/config.sendgrid.sevice';
import { UrlConfigService } from 'src/config/config.url.service';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';
import { ICountry } from 'src/country-mongo/interfaces/country.interface';
import { COUNTRY_COLLECTION_NAME } from 'src/country-mongo/schemas/constants';
import { MailTemplateEnum } from 'src/event/enum/mail-template.enum';
import { IMessageEventProcessFailed } from 'src/event/intefaces/message-event-process-failed.interface';
import { IMessageEventProcess } from 'src/event/intefaces/message-event-process.interface';
import { IMessageEventSuccess } from 'src/event/intefaces/message-event-success.interface';
import { IMessageEvent } from 'src/event/intefaces/message-event.interface';
import {
    MESSAGE_EVENT_COLLECTION,
    MESSAGE_EVENT_PROCESS_COLLECTION,
    MESSAGE_EVENT_PROCESS_VEXAM_COLLECTION,
    MESSAGE_EVENT_PROCESS_FAILED_COLLECTION,
    MESSAGE_EVENT_SUCCESS_COLLECTION,
    NOTIFICATION_COLLECTION,
    MEDPRO_DOCTOR_NOTIFICATION_COLLECTION,
    TRACKING_PUSH_NOTIF_TRA_CUU_KET_QUA,
    KPI_CSKH,
} from 'src/event/schemas/constants';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { IPartnerConfig } from 'src/partner-config/intefaces/partner-config.inteface';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { EventInsertPatientDto } from 'src/patient-mongo/dto/event-insert-patient.dto';
import { INSERT_PATIENT_V1 } from 'src/patient-mongo/event/constants';
import { PatientMongoService } from 'src/patient-mongo/patient-mongo.service';
import { IPushDevice } from 'src/push-device/intefaces/push-device.inteface';
import { PUSH_DEVICE_COLLECTION_NAME, PUSH_DEVICE_ERROR_COLLECTION_NAME } from 'src/push-device/schemas/constants';
import { SessionService } from 'src/session/session.service';
import { IUser } from 'src/user/interfaces/user.interface';
import { USER_COLLECTION_NAME, USER_DOCTOR_COLLECTION_NAME } from 'src/user/schemas/constants';
import { SERVICE_LOG_NAME } from './../audit-log/constant';
import { IEvent } from './../event/intefaces/event.inteface';
import { EVENT_COLLECTION_NAME } from './../event/schemas/constants';
import {
    CSKH_SCHEDULERS,
    HANDLE_BOOKING_CANCLE_PAYMENT_LATE,
    HANDLE_PATIENT_SORT,
    HANDLE_UPDATE_STATUS_BOOKING_V1_FAIL,
    LARK_NOTIF_CARE247_AFTER_SUCCESS,
    LARK_NOTIF_COOPERATE_MEDPRO,
    LARK_NOTIF_ENTERPRISE_REGISTRATION,
    LARK_NOTIF_PAYMENT_ERROR,
    LARK_NOTIF_RECRUITMENT,
    LARK_NOTIF_SYNC_BOOKING_FAIL,
    LOG_BOOKING_TREE_TOKEN_USER,
    MESSAGE_EVENT,
    MESSAGE_EVENT_MEDPRO_DOCTOR,
    MESSAGE_EVENT_MESSAGE_CTA,
    PUSH_NOTIF_BOOKING_COMPLETE_INFO,
    PUSH_NOTIF_BOOKING_NEW_NOTICE_LARK,
    PUSH_NOTIF_LARK,
    PUSH_NOTIF_TRACKING_MEDPRO_CARE,
    REMOVE_PATIENT_V1,
    SEND_MAIL_UMC_AFTER_PAYMENT,
    SEND_SMS_CARE247_AFTER_SUCCESS,
    SEND_ZNS_PORTAL,
    SYNC_USER_INFO,
    SYNC_V1_EVENT,
    TRACKING_CANCEL_RESERVATIONS,
    TransporterEvent,
    UPDATE_BOOKING_V1_EVENT,
    V1_TRANSFORM_DATA_SCHEDULE_FAIL,
} from './constant';
import { CreateMessageEventDto } from './dto/create-message-event.dto';
import { MessageEventDTO } from './dto/message-event.dto';
import { BaseResponse } from './interfaces/base-response.interface';
import { CronJob } from 'cron';
import { SchedulerRegistry } from '@nestjs/schedule';
import { EventService } from 'src/event/event.service';
import { CreateEventDTO } from 'src/event/dto/create-event.dto';
import * as uuid from 'uuid';
import { ConfigRepoService } from 'src/config/config.repo.service';
import { IRetryTransaction } from 'src/booking-gateway/intefaces/retry-transactions.interface';
import { UpdateStatusDTO } from 'src/booking-gateway/dto/update-status.dto';
import { UserService } from '../user/user.service';
import { PATIENT_COLLECTION_NAME, PATIENT_SORT_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';
import { IPatientSort } from 'src/patient-mongo/intefaces/patients-sort.interface';
import { PatientSortFormDto } from 'src/patient-mongo/dto/patient-sort-form.dto';
import { IPatient } from 'src/patient-mongo/intefaces/patient.inteface';
import { JwtModuleOptions } from '@nestjs/jwt';
import { JwtUserConfigService } from 'src/config/config.user.jwt.service';
import * as jwt from 'jsonwebtoken';
import { IPushDeviceError } from 'src/push-device/intefaces/push-device-error.interface';
import { PushDeviceErrorDto } from 'src/push-device/dto/push-device-error.dto';
import { RetryUpdateStatusDto } from 'src/booking-gateway/dto/retry-update-status.dto';
import { ICancelReservations } from 'src/booking-gateway/intefaces/cancel-reservations.interface';
import { BookingCancelPaymentLateDto } from './dto/booking-cancel-payment-callback.dto';
import * as queryString from 'query-string';
import { first, groupBy } from 'lodash';
import { UtilService } from 'src/config/util.service';
import { larkMsgTemplate1 } from './template/lark-message.template';
import { ConfigLarkService } from 'src/config/config.lark.service';
import { RE_EXAM_COLLECTION_NAME, RE_EXAM_VERIFY_COLLECTION_NAME } from '../re-exam/schemas/constants';
import { IReExamVerify } from '../re-exam/intefaces/re-exam-verify.inteface';
import { larkMsgBookingNewNoticeTemplate } from './template/lark-booking-new-notice-message.template';
import { larkMsgBookingNewNoticeTemplate2 } from './template/lark-booking-new-notice-message-2.template';
import { SENDGRID_CONNECTION } from 'src/config/sendgridConnection';
import { CreateMessageEventMepdroDoctorDto } from './dto/create-message-event-medpro-doctor.dto';
import { CreateNotifMedproDoctorDto } from '../event/dto/create-notif-medpro-doctor.dto';
import { IUserDoctor } from '../user/interfaces/user-doctor.inteface';
import { larkMsgEventCompleteBookingTelemed } from './template/lark-booking-telemed-complete.template';
import { CreateMedproDoctorNotifDto } from './dto/create-medpro-doctor-notif.dto';
import { DOCTOR_COLLECTION_NAME } from '../doctor-mongo/schemas/constants';
import { IDoctor } from '../doctor-mongo/interfaces/doctor.interface';
import { PushNotifRefundDto } from './dto/push-notif-refund.dto';
import { IZnsLog } from './interfaces/zns-log.interface';
import {
    BOOKING_CARE247_SMS,
    BOOKING_CARE_247,
    BOOKING_CARE_247_CONSTRAINT,
    BOOKING_CARE_247_CONSTRAINT_USER,
    BOOKING_FULL_SLOT_COLLECTION_NAME,
    BOOKING_FULL_SLOT_NOTI_LOG_COLLECTION_NAME,
    BOOKING_PATIENT_CONSTRAINT_COLLECTION_NAME, CONSTRAINTS_USER_PATIENT_BOOKING_ONEDAY, FIX_ZNS_LOG_COLLECTION_NAME, MEDPRO_CARE_TRACKING, PUSH_ZNS_BOOKING, RATING_APP_USER_COLLECTION_NAME,
    REPAYMENT_LOG_COLLECTION_NAME,
    SYNC_BOOKING_FAIL_CONSTRAINT,
    SYNC_V1_FAILED,
    USER_BOOKING_CHORAY_PUSH_NOTIF,
    ZNS_LOG_COLLECTION_NAME,
} from './schemas/constants';
import { InsertUserNotifDto } from './dto/insert-user-notif.dto';
import { CacheManagerService } from '../cache-manager/cache-manager.service';
import { LarkService } from '../config/lark.service';
import { IPatientTracking } from '../patient-mongo/patient-tracking.inteface';
import { PatientTrackingActionTextEnum } from '../common/enums/patient-tracking-action-text.enum';
import { larkMsgRepaymentSuccess } from './template/lark-repayment-success.template';
import { larkBookingFullSlotUmcTemplate } from './template/lark-booking-full-slot-umc.template';
import {IBookingFullSlot} from './interfaces/booking-full-slot.interface';
import { SUBJECT_COLLECTION_NAME } from '../subject-mongo/schemas/constants';
import { ISubject } from '../subject-mongo/interfaces/subject.interface';
import { larkMessageNotifNhidong1 } from './template/lark-message-notif-nhidong1.template';
import { IRepaymentLog } from './interfaces/repayment-log.interface';
import { BookingStatus } from '../his-gateway/dto/bookingStatus.dto';
import { IBookingFullSlotNotiLog } from './interfaces/booking-full-slot-noti-log.interface';
import { IBookingPatientConstraint } from './interfaces/booking-patient-constraint.interface';
import * as OneSignal from 'onesignal-node';
import { larkBookingNotifCompleteInfo } from './template/lark-booking-notif-conplete-info.template';
import { IRatingAppUser } from './interfaces/rating-app-user.interface';
import { IPushZnsBooking } from './interfaces/push-zns-booking.interface';
import { MessageCTADTO } from './dto/message-cta.dto';
import { PushNotifAllUsersDTO } from './dto/push-notif-all-users.dto';
import { larkMsgWarningUserBehaviorTemplate } from './template/lark-form-warning-user-behavior.temple';
import { ICity } from 'src/city-mongo/interfaces/city.interface';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { SendNotiWarningUserBehaviorDto } from './dto/send-noti-warning-user-behavior.dto';
import { larkMsgOnMedproCareBookingTemplate } from './template/lark-form-noti-on-medpro-care-booking.template';
import { SendNotiOnMedproCareBookDto } from './dto/send-noti-on-medpro-care-booking.dto';
import { larkMsgTHTMOnBooking } from './template/lark-booking-thtm.template';
import { SendNotiOnTHTMBookingDto } from './dto/lark-form-thtm-booking.dto';
import { larkTrackingMedproCare } from './template/lark-tracking-medpro-care.template';
import { IMedproCareTracking } from './interfaces/medpro-care-tracking.interface';
import { MedproCareTrackingDTO } from './dto/medpro-care-tracking.dto';
import * as mongoose from 'mongoose';
import { BookingService } from 'src/booking/booking.service';
import { SendNotiToLarkGroupAndSendMessageToPatientDto } from './dto/send-noti-and-push-noti-on-medpro-care-booking.dto';
import { LogService } from 'src/config/log.service';
import { PushNotifService } from 'src/push-notif/push-notif.service';
import { PushZnsCashBackOnPushDeviceFailedDto } from './dto/push-zns-cash-back-on-push-device-failed.dto';
import { larkKioskBVThongNhatOnPayment } from './template/lark-kiosk-bv-thongnhat-payment';
import { larkZaloPayOnLoginOrRegister } from './template/lark-zalo-pay-on-login-or-register.template';
import { PushNotificationDto } from './dto/push-notification.dto';
import { larkCheckExpiredDateTemplate } from './template/check-expired-date.template';
import { larkPaymentError } from './template/lark-payment-error.template';
import { larkBookingNotifSyncFail } from './template/lark-booking-sync-fail-notif.template';
import { ISyncBookingFailConstraint } from './interfaces/sync-booking-fail-constraint.interface';
import { larkMsgOnCashBackSurveyTemplate } from './template/lark-cash-back-form-submit.templete';
import { CashBackFormSubmitDto } from './dto/cash-back-form-submit.dto';
import { IBookingCare247 } from './interfaces/booking-care-247.interface';
import { BookingComplainDTO } from './dto/send-noti-on-complain-booking.dto';
import { larkMsgEventComplainBooking } from './template/lark-complain-booking.template';
import { larkCare247ConsultationRegistrationTemplate } from './template/lark-care247-consultation-registration.template';
import { LarkNotifCare247ConsultationRegistrationDto } from './dto/lark-notif-care247-consultation-registration.dto';
import { IBookingCare247Constraint } from './interfaces/booking-care247-constraint.interface';
import { larkMsgAssignInstructorCare247Template } from './template/lark-form-noti-assign-instructor-care247.template';
import { SmsService } from '../sms/sms.service';
import { IBookingCare247Sms } from './interfaces/booking-care247-sms.interface';
import * as slugify from 'slugify';
import { larkMsgOnMedproCareCancelTemplate } from './template/lark-form-noti-on-medpro-care-cancel.template';
import { IBookingCare247ConstraintUser } from './interfaces/booking-care247-constraint-user.interface';
import { IUserBookingChorayPushNotif } from './interfaces/user-booking-choray-push-notif.interface';
import { ITrackingPushNotifTCKQ } from 'src/event/intefaces/tracking-push-notif-tckq.interface';
import { larkMsgOnBookingTelemedCancelTemplate } from './template/lark-form-noti-on-booking-telemed-cancel.template';
import { ISyncV1Failed } from './interfaces/sync-v1-failed.interface';
import { IKpiCskh } from '../event/intefaces/kpi-cskh.interface';
import { larkMsgPartnerPreventForeignerTemplate } from './template/lark-form-noti-partner-prevent-foreigner.template';
import { ICskhSchedulers } from './interfaces/cskh-schedulers.inteface';
import { IConstraintsUserPatientBookingOneDay } from './interfaces/constraints-user-patient-booking-one-day.inteface';
import { larkSubmitCooperateMedpro } from './template/lark-submit-cooperate-medpro.template';
import { larkMsgRetrySyncBookingTemplate } from './template/lark-form-noti-retry-sync-booking.template';
import { larkSubmitEnterpriseRegistration } from './template/lark-submit-enterprise-registration.template';
import { larkSubmitApplyJob } from './template/lark-submit-recruitment.template';
import { REPO_NAME_BETA } from '../common/constants';
import { larkSubmitCooperateClinicMedpro } from './template/lark-submit-cooperate-clinic-medpro.template';

@Injectable()
export class MessageEventService {
    private logger = new Logger(MessageEventService.name);
    LARK_ENV_TEXT: string

    private pushDeviceTableName = 'push_device';
    private newSetZNSPartnersV2 = new Set().add('bvmathcm')
        .add('nhidonghcm')
        .add('bvpsct')
        .add('nhidong1')
        .add('choray')
        .add('binhthanhhcm')
        .add('umc2')
        .add('umc3')
        .add('leloi')
        .add('hoanmytd')
        .add('ctchhcm')
        .add('dalieuhcm');
    private readonly repoName: string;
    constructor(
        @InjectModel(MESSAGE_EVENT_COLLECTION) private messageEventModel: Model<IMessageEvent>,
        @InjectModel(MESSAGE_EVENT_PROCESS_COLLECTION) private messageEventProcessModel: Model<IMessageEventProcess>,
        @InjectModel(MESSAGE_EVENT_PROCESS_FAILED_COLLECTION) private messageEventProcessFailModel: Model<IMessageEventProcessFailed>,
        @InjectModel(MESSAGE_EVENT_SUCCESS_COLLECTION) private messageEventSuccessModel: Model<IMessageEventSuccess>,
        @InjectModel(TRACKING_PUSH_NOTIF_TRA_CUU_KET_QUA) private trackingPushNotifTCKQ: Model<ITrackingPushNotifTCKQ>,
        @InjectModel(PARTNER_CONFIG_COLLECTION_NAME) private partnerConfigModel: Model<IPartnerConfig>,
        @InjectModel(PUSH_DEVICE_COLLECTION_NAME) private pushDeviceModel: Model<IPushDevice>,
        @InjectModel(EVENT_COLLECTION_NAME) private eventModel: Model<IEvent>,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        @InjectModel(PAYMENT_COLLECTION_NAME) private paymentModel: Model<IPayment>,
        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
        @InjectModel(HOSPITAL_FEE_COLLECTION_NAME) private hospitalFeeModel: Model<IHospitalFee>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        @InjectModel(COUNTRY_COLLECTION_NAME) private readonly countryModel: Model<ICountry>,
        @InjectModel(PATIENT_SORT_COLLECTION_NAME) private readonly patientSortModel: Model<IPatientSort>,
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        @Inject(SENDGRID_CONNECTION) private readonly sendgrid,
        @InjectModel(NOTIFICATION_COLLECTION) private notificationModel: Model<IEvent>,
        @InjectModel(MEDPRO_DOCTOR_NOTIFICATION_COLLECTION) private medproDoctoNotifModel: Model<IEvent>,
        @InjectModel(RETRY_TRASACTION_COLLECTION_NAME) private readonly retryTransactionModel: Model<IRetryTransaction>,
        @InjectModel(PATIENT_COLLECTION_NAME) private readonly patientModel: Model<IPatient>,
        @InjectModel(PUSH_DEVICE_ERROR_COLLECTION_NAME) private readonly pushDeviceErrorModel: Model<IPushDeviceError>,
        @InjectModel(CANCEL_RESERVATIONS_COLLECTION_NAME) private readonly cancelReservationModel: Model<ICancelReservations>,
        @InjectModel(MAIL_RECEIVER_COLLECTION_NAME) private readonly mailReceiveModel: Model<IMailReceiver>,
        @InjectModel(PATIENT_VERSION_COLLECTION_NAME) private readonly patientVersionModel: Model<IPatientVersion>,
        @InjectModel(RE_EXAM_VERIFY_COLLECTION_NAME) private readonly reExamVerifyModel: Model<IReExamVerify>,
        @InjectModel(USER_DOCTOR_COLLECTION_NAME) private readonly userDoctorModel: Model<IUserDoctor>,
        @InjectModel(DOCTOR_COLLECTION_NAME) private readonly doctorModel: Model<IDoctor>,
        @InjectModel(SUBJECT_COLLECTION_NAME) private readonly subjectModel: Model<ISubject>,
        @InjectModel(ZNS_LOG_COLLECTION_NAME) private readonly zndLogModel: Model<IZnsLog>,
        @InjectModel(FIX_ZNS_LOG_COLLECTION_NAME) private readonly fixZndLogModel: Model<IZnsLog>,
        @InjectModel(PATIENT_TRACKING_COLLECTION_NAME) private readonly patientTrackingModel: Model<IPatientTracking>,
        @InjectModel(BOOKING_FULL_SLOT_COLLECTION_NAME) private readonly bookingFullSlotModel: Model<IBookingFullSlot>,
        @InjectModel(BOOKING_FULL_SLOT_NOTI_LOG_COLLECTION_NAME) private readonly bookingFullSlotNotiLog: Model<IBookingFullSlotNotiLog>,
        @InjectModel(REPAYMENT_LOG_COLLECTION_NAME) private readonly repaymentLogModel: Model<IRepaymentLog>,
        @InjectModel(BOOKING_PATIENT_CONSTRAINT_COLLECTION_NAME) private readonly bookingPatientConstraintModel: Model<IBookingPatientConstraint>,
        @InjectModel(RATING_APP_USER_COLLECTION_NAME) private readonly ratingAppUserModel: Model<IRatingAppUser>,
        @InjectModel(PUSH_ZNS_BOOKING) private readonly pushZnsBookingModel: Model<IPushZnsBooking>,
        @InjectModel(CITY_COLLECTION_NAME) private readonly cityModel: Model<ICity>,
        @InjectModel(MEDPRO_CARE_TRACKING) private readonly medproCareTrackingModel: Model<IMedproCareTracking>,
        @InjectModel(SYNC_V1_FAILED) private readonly syncV1FailedModel: Model<ISyncV1Failed>,
        @InjectModel(SYNC_BOOKING_FAIL_CONSTRAINT) private readonly syncBookingFailConstraintModel: Model<ISyncBookingFailConstraint>,
        @InjectModel(BOOKING_CARE_247) private readonly bookingCare247Model: Model<IBookingCare247>,
        @InjectModel(BOOKING_CARE_247_CONSTRAINT) private readonly bookingCare247ConstraintModel: Model<IBookingCare247Constraint>,
        @InjectModel(BOOKING_CARE247_SMS) private readonly bookingCare247SmsModel: Model<IBookingCare247Sms>,
        @InjectModel(BOOKING_CARE_247_CONSTRAINT_USER) private readonly bookingCare247ConstraintUserModel: Model<IBookingCare247ConstraintUser>,
        @InjectModel(USER_BOOKING_CHORAY_PUSH_NOTIF) private readonly userBookingChorayPushNotifModel: Model<IUserBookingChorayPushNotif>,
        @InjectModel(CSKH_SCHEDULERS) private cskhSchedulersModel: Model<ICskhSchedulers>,
        @InjectModel(KPI_CSKH) private kpiCskhModel: Model<IKpiCskh>,
        @InjectModel(CONSTRAINTS_USER_PATIENT_BOOKING_ONEDAY) private constraintUserPatientBookingOneDayModel: Model<IConstraintsUserPatientBookingOneDay>,
        private sendgridConfigService: SendgridConfigService,
        private globalSettingService: GlobalSettingService,
        private eventEmmiter: EventEmitter2,
        private restApiOldHospital: RestfulAPIOldHospitalConfigService,
        private http: HttpService,
        private urlConfigService: UrlConfigService,
        private readonly patientMongoService: PatientMongoService,
        private readonly sessionService: SessionService,
        private scheduler: SchedulerRegistry,
        private repoService: ConfigRepoService,
        private readonly userService: UserService,
        private readonly jwtUserConfigService: JwtUserConfigService,
        private readonly utilService: UtilService,
        private readonly larkConfig: ConfigLarkService,
        private readonly cacheService: CacheManagerService,
        private readonly larkService: LarkService,
        private readonly logService: LogService,
        private readonly pushNotifService: PushNotifService,
        private readonly repoConfigService: ConfigRepoService,
        private readonly smsService: SmsService,
    ) {
        this.LARK_ENV_TEXT = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
        this.repoName = this.repoConfigService.getRepoName();
    }

    @OnEvent(MESSAGE_EVENT, { async: true })
    async onMessageEvent(payload: CreateMessageEventDto) {
        return this.createMessageEvent(payload, (payload?.repoName || ''));
    }

    private generateNotificationMessage (booking: any) {
        // const partner = booking.partner as unknown as IHospital;
        const partnerName = booking.partner.name;
        const partnerId = booking.partner.partnerId;
        const bookingCode = booking.bookingCode;
        const userPhoneOrUserName = booking.user.username;
        
        const linkCashBackForm = queryString.stringifyUrl({
            url: "https://medpro.vn/khao-sat-hoan-tien",
            query: {
                hide_bookingId: 1,
                hide_partnerId: 1,
                hide_userId: 1,
                prefill_bookingId: bookingCode,
                prefill_partnerId: partnerId,
                prefill_userId: userPhoneOrUserName,
            },
        });

        return {
            linkCashBackForm: linkCashBackForm,
            title: `📢 Thông báo: Nhận ngay khuyến mãi hoàn tiền lên đến 5% từ Medpro`,
            message: `Chúc mừng ${booking.patient.surname} ${booking.patient.name} đã sử dụng thành công dịch vụ tại ${partnerName} \nĐể nhận hoàn tiền, bạn vui lòng hoàn tất các bước sau trong vòng 07 ngày kể từ ngày nhận Thông báo này: \n1️⃣ Điền phiếu khảo sát đánh giá dịch vụ \n2️⃣ Cung cấp số tài khoản ngân hàng \n3️⃣ Tải lên hóa đơn đã sử dụng dịch vụ \n👉 Nhấn vào đây để bắt đầu ${linkCashBackForm} \nXem chi tiết các điều kiện và điều khoản về chương trình https://pkh-medpro.larksuite.com/wiki/UXjlwGQDLiBCbMkcpLQuf4zBs5b \n📞 Mọi thắc mắc vui lòng liên hệ tổng đài 1900 2115 để được hỗ trợ. \nCảm ơn bạn đã luôn đồng hành cùng Medpro.`
        }
    }

    async pushCashbackNotiToUser(bookingCode?: string): Promise<any> {


        const dateChoose = moment().utc().subtract(1, 'days').add(7, 'hours').format('YYYY-MM-DD');

        const fromDate = moment(dateChoose, 'YYYY-MM-DD').set({
            hours: 0,
            minutes: 0,
            seconds: 0,
        });

        const toDate = moment(dateChoose, 'YYYY-MM-DD').set({
            hours: 23,
            minutes: 59,
            seconds: 59,
        });

        let condition: any = {};
        if (bookingCode) {
            condition = {
                bookingCode
            }
        } else {
            condition = {
                status: [1, 2], // ? 1: Chưa khám, 2: Đã khám,
                isCashBack: true,
                appId: 'medpro',
                date: {
                    $gte: fromDate.toDate(),
                    $lte: toDate.toDate(),
                },
            }
        }

        console.log(condition);

        const bookings = await this.bookingModel
            .find(condition)
            .exec();


        if (bookings.length === 0) {

            const msTemp =  {
                config: {
                    wide_screen_mode: true,
                },
                elements: [
                    {
                        tag: 'div',
                        text: {
                            content: 'Chưa có phát sinh phiếu khám cashback',
                            tag: 'lark_md',
                        },
                    },
                  
                ],
                header: {
                    template: 'orange',
                    title: {
                        content: '🔔' + ' THÔNG BÁO',
                        tag: 'plain_text',
                    },
                },
            };

            this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                webHub: 'https://open.larksuite.com/open-apis/bot/v2/hook/1fdf464a-d1bb-4e53-a987-f02acf2bca4a',
                data: {
                    msg_type: 'interactive',
                    card: msTemp,
                }
            });

            return false;
        } 
            
        // xử lý tiếp
        console.log('bookings.length: ', bookings.length)
        const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;

        const memtionId = 'ou_25713be9f3f98398b8863313740fc367';
        const memtionName = 'Phạm Lê Diệu Hương';

        for await (const item of bookings) {
            console.log('Xử lý bookingCode: ', item.bookingCode)
            const booking = await
                this.bookingModel.findOne({ _id: item._id }, {
                    service: true, serviceId: true, subject: true, subjectId: true, doctor: true, sectionId: true, section: true,
                    room: true, date: true, patient: true, partnerId: true, appId: true, roomId: true, insuranceType: true,
                    transactionId: true, insuranceCode: true, sequenceNumber: true, checkInRoom: true, bookingNote: true,
                    insuranceChoice: true, patientVersionId: true, patientId: true, status: true, noPayment: true,
                    bookingCodeV1: true, syncStatus: true, bookingCode: true, serviceInfo: true, bookingInternalId: true, serviceType: true,
                    addonServices: true, id: true, treeId: true, implementAgent: true, implementLocation: true, userId: true, createdAt: true, care247: true
                })
                    .populate('service')
                    .populate({ path: 'subject', select: 'name' })
                    .populate({ path: 'room', select: 'name' })
                    .populate({ path: 'doctor', select: 'name phone' })
                    .populate({ path: 'section', select: 'name' })
                    .populate({
                        path: 'patient', select: {
                            name: true, surname: true, sex: true,
                            birthdate: true, birthyear: true, code: true,
                        },
                    })
                    .exec()

            // if (!booking || booking.status !== 1) {
            //     return;
            // }

            let patients = [];
            if (typeof booking.patientVersion !== typeof undefined) {
                patients = await this.patientVersionModel
                    .find({ id: booking.patientVersionId })
                    .populate('city')
                    .populate('district')
                    .populate('ward')
                    .exec();
            } else {
                patients = await this.patientModel
                    .find({ id: booking.patientId })
                    .populate('city')
                    .populate('district')
                    .populate('ward')
                    .exec();
            }

            if (patients.length > 0) {
                const firstPatient = first(patients);
                // const patientFullAddress = this.patientMongoService.getFullAddress(firstPatient);
                const patientObj = firstPatient.toObject();
                const bookingObj = booking.toObject();

                const hospital = await this.hospitalModel.findOne({ partnerId: booking.partnerId }, { name: true, short_name: true, features: true, isContractSigned: true, status: true, partnerId: true }).exec();

                const bookingDateString = moment(booking?.date ? booking.date : booking.createdAt).utc().add(7, 'hours').format('HH:mm DD-MM-YYYY')

                const user = await this.userModel.findById(bookingObj.userId).exec();

                const { userId, appId } = booking;
                // Override bookingCode
                // const overrideBookingCode = booking?.bookingCodeV1 ? bookingCodeV1 : bookingCode;
                /* tìm lại các devices của user theo user_id */
                const listDevices = await this.pushDeviceModel.find({ userId, appId }).exec();
                const partnerConfig = await this.partnerConfigModel.findOne({ partnerId: appId }).exec();
                if (typeof partnerConfig !== typeof undefined && partnerConfig.notifApiKey !== '' && partnerConfig.notifAppId !== '') {
                    const client = new OneSignal.Client(partnerConfig.notifAppId, partnerConfig.notifApiKey, { apiRoot: 'https://onesignal.com/api/v1' });
                    // const deviceInfos = listDevices.filter(item => item.userId === userId && item.appId === appId);
                    if (listDevices.length > 0) {
                        /* lấy thông số config push notif */
                        const mapClients = map(listDevices, 'clientId');
                        const resultData: any = { url: 'https://medpro.com.vn' };
                        resultData.transactionId = booking.transactionId;
                        resultData.type = 100;

                        const {
                            linkCashBackForm: url,
                            title: titleMessage,
                            message: content
                        } = this.generateNotificationMessage({
                            ...bookingObj, user, partner: {
                                name: hospital.name,
                                partnerId: hospital.partnerId
                            }
                        });

                        const defaultNotif = {
                            contents: {
                                en: content,
                                vi: content,
                            },
                            headings: {
                                en: titleMessage,
                                vi: titleMessage,
                            },
                            // ...( icon ? { small_icon: icon } : {} ),
                            data: { ...resultData, content: content, title: titleMessage, url: url, },
                            include_player_ids: mapClients,
                        };

                        // bắn larkgroup 

                        let tagname = 'Tiến hành bắn cashback notification';
                        let msgContent = larkMsgBookingNewNoticeTemplate(larkEnv, tagname, `${user.username} - ${user?.fullname}`, `${patientObj.surname} ${patientObj.name} (${patientObj?.mobile})`, bookingDateString, hospital, (booking?.bookingCodeV1 || booking.bookingCode), this.utilService.getBookingText(bookingObj), memtionId, memtionName);

                        try {
                            const responseData = await client.createNotification({ ...defaultNotif });
                            console.log(JSON.stringify(responseData, null, 2))

                            this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                                webHub: 'https://open.larksuite.com/open-apis/bot/v2/hook/1fdf464a-d1bb-4e53-a987-f02acf2bca4a',
                                data: {
                                    msg_type: 'interactive',
                                    card: msgContent,
                                }
                            });

                            const dataZNS = {
                                tenbn: `${patientObj.surname} ${patientObj.name}`,
                                tenbv: hospital.name,
                                maphieu: booking?.bookingCodeV1 || booking.bookingCode,
                                userphone: user.username,
                                gioitinh: patientObj.sex === 1 ? "Anh" : "Chị"
                            }

                            tagname = 'Tiến hành gửi Zalo ZNS '
                            msgContent = larkMsgBookingNewNoticeTemplate(larkEnv, tagname, `${user.username} - ${user?.fullname}`, `${patientObj.surname} ${patientObj.name} (${patientObj?.mobile})`, bookingDateString, hospital, (booking?.bookingCodeV1 || booking.bookingCode), this.utilService.getBookingText(bookingObj), memtionId, memtionName);

                            await this.pushNotifZNSOnPushDeviceFailed(dataZNS)

                            this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                                webHub: 'https://open.larksuite.com/open-apis/bot/v2/hook/1fdf464a-d1bb-4e53-a987-f02acf2bca4a',
                                data: {
                                    msg_type: 'interactive',
                                    card: msgContent,
                                }
                            });

                        } catch (error) {

                            // tien hanh guui zns

                            const dataZNS = {
                                tenbn: `${patientObj.surname} ${patientObj.name}`,
                                tenbv: hospital.name,
                                maphieu: booking?.bookingCodeV1 || booking.bookingCode,
                                userphone: user.username,
                                gioitinh: patientObj.sex === 1 ? "Anh" : "Chị"
                            }

                            tagname = 'Tiến hành gửi Zalo ZNS '
                            msgContent = larkMsgBookingNewNoticeTemplate(larkEnv, tagname, `${user.username} - ${user?.fullname}`, `${patientObj.surname} ${patientObj.name} (${patientObj?.mobile})`, bookingDateString, hospital, (booking?.bookingCodeV1 || booking.bookingCode), this.utilService.getBookingText(bookingObj), memtionId, memtionName);

                            await this.pushNotifZNSOnPushDeviceFailed(dataZNS)

                            this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                                webHub: 'https://open.larksuite.com/open-apis/bot/v2/hook/1fdf464a-d1bb-4e53-a987-f02acf2bca4a',
                                data: {
                                    msg_type: 'interactive',
                                    card: msgContent,
                                }
                            });

                            // if (error instanceof OneSignal.HTTPError) {
                            //     return error;
                            // }
                            // return error
                        }
                    } else {
                        const dataZNS = {
                            tenbn: `${patientObj.surname} ${patientObj.name}`,
                            tenbv: hospital.name,
                            maphieu: booking?.bookingCodeV1 || booking.bookingCode,
                            userphone: user.username,
                            gioitinh: patientObj.sex === 1 ? "Anh" : "Chị"
                        }

                        const tagname = 'Tiến hành gửi Zalo ZNS '
                        const msgContent = larkMsgBookingNewNoticeTemplate(larkEnv, tagname, `${user.username} - ${user?.fullname}`, `${patientObj.surname} ${patientObj.name} (${patientObj?.mobile})`, bookingDateString, hospital, (booking?.bookingCodeV1 || booking.bookingCode), this.utilService.getBookingText(bookingObj), memtionId, memtionName);

                        await this.pushNotifZNSOnPushDeviceFailed(dataZNS)

                        this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                            webHub: 'https://open.larksuite.com/open-apis/bot/v2/hook/1fdf464a-d1bb-4e53-a987-f02acf2bca4a',
                            data: {
                                msg_type: 'interactive',
                                card: msgContent,
                            }
                        });
                    }
                }
            }
            else {
                console.log('Không có thông tin hồ sơ của booking: ', booking.bookingCode);
            }
        }

    }

    public async pushNotiToUser ({
        bookingCode, 
        titleMessage,
        content,
        url,
        topicId
    }: PushNotificationDto) {
        let response: {
            isOK: boolean,
            message?: string
        }

        const bookingById = await this.bookingModel.findOne({
            bookingCode: bookingCode
        }).exec();

        await this.pushNotifService.pushNotifyToUserWithTemplate({
            bookingId: bookingById.id,
            title: titleMessage,
            message: content,  
            url: url,
        })

        try {
            const eventInfo = {
                id: uuid.v4().replace(/-/g, ''),
                createTime: moment().toISOString(),
                topicId: topicId ?? 'messages.push-inform-notif-cash-back',
                userId: bookingById.userId,
                appId: bookingById.appId,
                partnerId: bookingById.partnerId,
                title: titleMessage,
                isNotif: true,
                isPushNotif: false,
                isSendMail: false,
                content: content,
                ...(url ? { url } : {}),
                eventData: {
                    ...bookingById.toObject(),
                    ...(url ? { url } : {}),
                    content: content,
                    type: 100,
                },
                type: 100,
            };

            const event = new this.eventModel(eventInfo);
            await event.save();
            
            await this.cacheService.delByPattern(`event:events-by-user.*userMongoId=${bookingById.userId}.*`)
            await this.cacheService.delByPattern(`event.*userMongoId=${bookingById.userId}`)
            await this.cacheService.delByPattern(`event:events-unread-by-user.*userMongoId=${bookingById.userId}.*`)

            this.logger.log(`Push noti to user success!`)
            response = {
                isOK: true,
                message: 'Push Noti to User success!'
            }
        } catch (error) {
            this.logger.error(error.message || 'Push Noti to User fail!')
            response = {
                isOK: false,
                message: error?.message || 'Push Noti to User fail!'
            }
        }       
        
        return response
    }
    
    // ? ******************************************************************************************************************************
    // ? GỬI THÔNG BÁO ZALO ZNS CHO NGƯỜI DÙNG 
    // ? ******************************************************************************************************************************
    public async pushNotifZNSOnPushDeviceFailed(payload: PushZnsCashBackOnPushDeviceFailedDto): Promise<any> {
        const TEMPLATE_ID = '353659';
        const { userphone, ...params } = payload;
        const replacePhonePrefix = userphone.replace(/^(\+84)/, '84');

        const data = {
            phone: replacePhonePrefix,
            zaloTemplateId: TEMPLATE_ID,
            params: params,
        }

        console.log('Gửi thông báo zns cho người dùng khi push noti device failed:', data)
        this.eventEmmiter.emit(SEND_ZNS_PORTAL, data);

        return {
            isOK: true,
            message: `Gửi thông báo zns cho >>>user: ${replacePhonePrefix} thành công với templateId: ${TEMPLATE_ID} và params: ${JSON.stringify(params, null, 2)}`
        }
    }

    async larkGroupEventSyncV1(payload: any): Promise<any> {

        try {
            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            const notifLarkGroup = "https://open.larksuite.com/open-apis/bot/v2/hook/3c61d0b8-0442-4459-8150-66dcd65bbcb6";

            const msgContent = larkCheckExpiredDateTemplate({
                message: JSON.stringify(payload, null, 2),
                larkEnv: larkEnv
            });

            const { bookingCode } = payload;
            if (bookingCode) {

                try {

                    await this.syncV1FailedModel.create({ bookingCode: bookingCode });

                    this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                        webHub: notifLarkGroup,
                        data: {
                            msg_type: 'interactive',
                            card: msgContent,
                        }
                    });

                    return {
                        isOK: true,
                        message: 'SUCCESS_SEND_LARK_NOTIF'
                    }
                } catch (error) {

                }

            }

            return {
                isOK: false,
                message: 'SUCCESS_SEND_LARK_NOTIF'
            }

        } catch (error) {
            return {
                isOK: false,
                message: error?.message || 'ERROR_SEND_LARK_NOTIF'
            }
        }
    }

    async seedingPushNotifTCKQ(): Promise<any> {

        const data = await this.messageEventSuccessModel.find({
            topic: 'messages.push-inform-notif-tckqcr',
            clientViewId: { $ne: "" }
        }).exec();

        if (data.length === 0) {
            return;
        }

        const notifs = data.map(item => item.toObject());

        const chunkData = chunk(notifs, 1000);
        for await (const chunkDetail of chunkData) {
            await this.trackingPushNotifTCKQ.insertMany(chunkDetail);
        }

        return {
            isOK: true,
        };
    }

    async pushNotificationCare247Remider({ vdate, bookingCode }: any = {}):Promise<any>{
        let tomorrow = '';

        if (vdate) {
            tomorrow = moment(vdate, 'YYYY-MM-DD').isValid() ? vdate : moment().add(1, 'days').format('YYYY-MM-DD');
        } else {
            tomorrow = moment().utc().add(7, 'hours').add(1, 'days').format('YYYY-MM-DD');
        }

        const fromDate = moment(tomorrow, 'YYYY-MM-DD').set({
            hours: 0,
            minutes: 0,
            seconds: 0,
        }).subtract(7, 'hours');

        const toDate = moment(tomorrow, 'YYYY-MM-DD').set({
            hours: 23,
            minutes: 59,
            seconds: 59,
        }).subtract(7, 'hours');

        let query: any = {}

        if (bookingCode) {
            query.bookingCode = bookingCode
        } else {
            
            const partnerListConfig = await this.globalSettingService.findByKeyAndRepoName('CARE247_PUSH_NOTIFICATION_REMINDER')
            const partnerList = partnerListConfig.split(',');

            query = {
                status: 0,
                appId: 'medpro',
                partnerId: { $in: [...partnerList] },
                platform: { $in: ['ios', 'android'] },
                bookingStatus: 1,
                date: { $gte: fromDate.toDate(), $lte: toDate.toDate() }
            }
        }

        const bookings = await this.bookingCare247Model
        .find(query)
        .populate({
            path: 'booking', select: {
                bookingCode: true, bookingCodeV1: true, date: true, 
                partnerId: true, createdAt: true, userId: true, appId: true, transactionId: true, id: true
            },
        })
        .populate({
            path: 'patient', select: {
                name: true, surname: true, sex: true,
                birthdate: true, birthyear: true, code: true,
                id: true
            },
        })
        .populate({ path: 'user', select: 'username' })
        .populate({ path: 'subject', select: 'name' })
        .populate({ path: 'room', select: 'name' })
        .populate({ path: 'partner', select: 'name' })
        .exec();

        // if(bookings.length > 1){
        //     return false;
        // }

        const { title, content } = await this.globalSettingService.findByKeyAndRepoNameJSON('CONFIG_NOTIF_CARE247_24HOURS_REMINDER');

        const messageInfo: any = {
            id: uuid.v4().replace(/-/g, ''),
            title,
            content,
        };

        for await (const vitem of bookings) {

            const item = vitem.toObject();
            // this.logger.debug(item)

            const booking: any = item.booking;
            const patientObj: any = item.patient;
            // const partner: any = item.partner;
            // const user : any =  item.user;
            // const subject: any = item.subject


            // tìm lại mã bên nhân bên HIS.
            const getPatientCode = await this.patientMongoService.getPatientCodeByPatientId(patientObj.id, booking.partnerId);
            let patientCode = patientObj.code;
            if(getPatientCode){
                patientCode = getPatientCode?.patientCode || patientObj.code
            }

            const eventData = {
                topic: 'care247_24hours_content',
                topicId: 'care247_24hours_content',
                createTime: moment().toISOString(),

                user: `${booking.userId}`,
                booking: `${booking._id}`,

                userId: booking.userId,
                appId: booking.appId,
                partnerId: booking.partnerId,
                // ở ngoài (thanh trạng thái) thì lấy content
                title: messageInfo.title,
                content: messageInfo.content,
                isPushNotif: true,
                eventData: {
                    content: messageInfo.content,
                    title: messageInfo.title,
                    type: 106,
                    tab: 1,
                    transactionId: booking.transactionId,
                },
                type: 106,
                repoName: 'Care247Remider'
            }

            await this.cacheService.delByPattern(`event:events-by-user.*userMongoId=${booking.userId}.*`)
            await this.cacheService.delByPattern(`event.*userMongoId=${booking.userId}`)
            await this.cacheService.delByPattern(`event:events-unread-by-user.*userMongoId=${booking.userId}.*`)


            this.eventEmmiter.emit(MESSAGE_EVENT, eventData);
            
            await this.createEvent({ ...eventData, title: messageInfo.title }, true, false, false);
    
        }

        return true;

        // return {
        //     isOK: true,
        //     query,
        //     date: { $gte: fromDate.toDate(), $lte: toDate.toDate() },
        //     dateMM: moment().add(1, 'day').startOf('day').toDate(),
        //     bookings: {
        //         count: bookings.length
        //     }
        // };
    }

    async pushNotificationBookingRemider({ vdate, bookingCode }: any = {}):Promise<any>{
        let tomorrow = '';

        if (vdate) {
            tomorrow = moment(vdate, 'YYYY-MM-DD').isValid() ? vdate : moment().format('YYYY-MM-DD');
        } else {
            tomorrow = moment().utc().add(7, 'hours').format('YYYY-MM-DD');
        }

        const fromDate = moment(tomorrow, 'YYYY-MM-DD').set({
            hours: 0,
            minutes: 0,
            seconds: 0,
        })
        console.log('fromDate', fromDate.format('YYYY-MM-DD HH:mm:ss'));
        const toDate = moment(tomorrow, 'YYYY-MM-DD').set({
            hours: 23,
            minutes: 59,
            seconds: 59,
        })

        let query: any = {}

        if (bookingCode) {
            query.bookingCode = bookingCode
        } else {
            
            // const partnerListConfig = await this.globalSettingService.findByKeyAndRepoName('PUSH_NOTIFICATION_REMINDER')
            // const partnerList = partnerListConfig.split(',');

            query = {
                status: 1,
                appId: 'medpro',
                // partnerId: { $in: [...partnerList] },
                date: { $gte: fromDate.toDate(), $lte: toDate.toDate() }
            }
        }

        const bookings = await this.bookingModel
        .find(query)
        .populate({
            path: 'patient', select: {
                name: true, surname: true, sex: true,
                birthdate: true, birthyear: true, code: true,
                id: true
            },
        })
        .populate({ path: 'user', select: 'username' })
        .populate({ path: 'subject', select: 'name' })
        .populate({ path: 'room', select: 'name' })
        .populate({ path: 'partner', select: 'name' })
        .exec();
         console.log('>>>>',bookings)
        // if(bookings.length > 1){
        //     return false;
        // }

        const { title, content, url } = await this.globalSettingService.findByKeyAndRepoNameJSON('PUSH_NOTIFICATION_BOOKING_SUCCESS');

        const messageInfo: any = {
            id: uuid.v4().replace(/-/g, ''),
            title,
            content,
            url
        };

        for await (const vitem of bookings) {

            const item = vitem.toObject();
            // this.logger.debug(item)

            const booking: any = item; // item chính là booking object, không cần item.booking

            const eventData = {
                topic: 'booking_reminder_content',
                topicId: 'booking_reminder_content',
                createTime: moment().toISOString(),

                user: `${booking.userId}`,
                booking: `${booking._id}`,

                userId: booking.userId,
                appId: booking.appId,
                partnerId: booking.partnerId,
                // ở ngoài (thanh trạng thái) thì lấy content
                title: messageInfo.title,
                content: messageInfo.content,
                isPushNotif: true,
                eventData: {
                    content: messageInfo.content,
                    title: messageInfo.title,
                    type: 106,
                    tab: 1,
                    transactionId: booking.transactionId,
                    url: messageInfo.url
                },
                type: 106,
                repoName: 'booking_reminder_content'
            }
            console.log('>>>>',eventData)
            await this.cacheService.delByPattern(`event:events-by-user.*userMongoId=${booking.userId}.*`)
            await this.cacheService.delByPattern(`event.*userMongoId=${booking.userId}`)
            await this.cacheService.delByPattern(`event:events-unread-by-user.*userMongoId=${booking.userId}.*`)


            this.eventEmmiter.emit(MESSAGE_EVENT, eventData);
            
            await this.createEvent(eventData, true, false, false);
    
        }

        return true;

        // return {
        //     isOK: true,
        //     query,
        //     date: { $gte: fromDate.toDate(), $lte: toDate.toDate() },
        //     dateMM: moment().add(1, 'day').startOf('day').toDate(),
        //     bookings: {
        //         count: bookings.length
        //     }
        // };
    }

    async pushZnsCare247Remider({ vdate, bookingCode }: any = {}):Promise<any>{

        let tomorrow = '';

        if (vdate) {
            tomorrow = moment(vdate, 'YYYY-MM-DD').isValid() ? vdate : moment().add(1, 'days').format('YYYY-MM-DD');
        } else {
            tomorrow = moment().utc().add(7, 'hours').add(1, 'days').format('YYYY-MM-DD');
        }

        const fromDate = moment(tomorrow, 'YYYY-MM-DD').set({
            hours: 0,
            minutes: 0,
            seconds: 0,
        }).subtract(7, 'hours');

        const toDate = moment(tomorrow, 'YYYY-MM-DD').set({
            hours: 23,
            minutes: 59,
            seconds: 59,
        }).subtract(7, 'hours');

        let query: any = {}

        if (bookingCode) {
            query.bookingCode = bookingCode
        } else {
            const partnerListConfig = await this.globalSettingService.findByKeyAndRepoName('CARE247_PUSH_ZNS_REMINDER')
            const partnerList = partnerListConfig.split(',')
            query = {
                status: 0,
                partnerId: { $in: [...partnerList] },
                bookingStatus: 1,
                // appId: { $ne: 'umc' }, // umc đã push job riêng
                date: { $gte: fromDate.toDate(), $lte: toDate.toDate() }
            }
        }

        const bookings = await this.bookingCare247Model
        .find(query)
        .populate({
            path: 'booking', select: {
                bookingCode: true, bookingCodeV1: true, date: true, 
                partnerId: true, createdAt: true
            },
        })
        .populate({
            path: 'patient', select: {
                name: true, surname: true, sex: true,
                birthdate: true, birthyear: true, code: true,
                id: true
            },
        })
        .populate({ path: 'user', select: 'username' })
        .populate({ path: 'subject', select: 'name' })
        .populate({ path: 'room', select: 'name' })
        .populate({ path: 'partner', select: 'name' })
        .exec();

        // if(bookings.length > 1){
        //     return false;
        // }

        const care247ZnsTemplateId = await this.globalSettingService.findByKeyAndRepoName('CARE247_ZNS_TEMPLATE_ID_REMIDER');

        for await (const vitem of bookings) {

            const item = vitem.toObject();
            // this.logger.debug(item)

            const booking: any = item.booking;
            const patientObj: any = item.patient;
            const partner: any = item.partner;
            const user : any =  item.user;
            const subject: any = item.subject


            // tìm lại mã bên nhân bên HIS.
            const getPatientCode = await this.patientMongoService.getPatientCodeByPatientId(patientObj.id, booking.partnerId);
            let patientCode = patientObj.code;
            if(getPatientCode){
                patientCode = getPatientCode?.patientCode || patientObj.code
            }

            const objZNS :any = {
                zaloTemplateId: care247ZnsTemplateId,
                params: {
                    gioi_tinh: patientObj.sex === 1 ? "Anh" : "Chị",
                    ten_bn: `${patientObj.surname} ${patientObj.name}`,
                    ten_bv: `${partner.name}`,
                    ma_bn: patientCode,
                    ngay_kham: moment(booking.date).utc().add(7, 'hours').format('HH:mm DD/MM/YYYY'),
                    chuyen_khoa:  get(subject, 'name', ''),
                    ngay_dat_lich:  moment(booking.createdAt).utc().add(7, 'hours').format('DD/MM/YYYY'),
                },
                phone: user.username.replace(/^(\+84)/, '84'),
            }
            // this.logger.log(objZNS);

            this.eventEmmiter.emit(SEND_ZNS_PORTAL, objZNS );
        }

        return true;

    }

    // ? GỬI THÔNG BÁO KHI KIỂM TRA NGÀY ĐẶT KHÁM FAILED
    public async handleOnBookingDateExpired(payload: Record<string, any>) {
        try {
            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            const notifLarkGroup = this.larkConfig.getLarkNotifPaymentError;

            const msgContent = larkCheckExpiredDateTemplate({
                message: JSON.stringify(payload),
                larkEnv: larkEnv
            });

            this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                webHub: notifLarkGroup,
                data: {
                    msg_type: 'interactive',
                    card: msgContent,
                }
            })

            return {
                isOK: true,
                message: 'SUCCESS_SEND_LARK_NOTIF'
            }
        } catch (error) {
            return {
                isOK: false,
                message: error?.message || 'ERROR_SEND_LARK_NOTIF'
            }
        }
    }

    // ? GỬI THÔNG BÁO TỚI GROUP LARK KHI NGƯỜI DÙNG ĐĂNG KÝ HOẶC ĐĂNG NHẬP QUA ZALO PAY
    public sendNotiOnZaloPay(payload: Record<string, any>) {
        try {
            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            const notifLarkGroup = this.urlConfigService.getLarkNotiZaloPayUrl

            const msgContent = larkZaloPayOnLoginOrRegister({
                message: JSON.stringify(payload),
                larkEnv: larkEnv
            });

            this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                webHub: notifLarkGroup,
                data: {
                    msg_type: 'interactive',
                    card: msgContent,
                }
            })

            return {
                isOK: true,
                message: 'SUCCESS_SEND_LARK_NOTIF'
            }
        } catch (error) {
            return {
                isOK: false,
                message: error?.message || 'ERROR_SEND_LARK_NOTIF'
            } 
        }
    }

    // ? ******************************************************************************************************************************
    // ? GỬI THÔNG BÁO TỚI GROUP LARK KHHI NGƯỜI DÙNG THANH TOÁN KIOSK TẠI BV THỐNG NHẤT
    // ? ******************************************************************************************************************************
    private async sendNotiOnKioskPayment(payload: {
        medproId: string;
        hoso: string;
        maphieu: string;
        trangthai: string;
        thoigian: string;
        benhvien: string;
        larkTitle?: string
    }) {
        try {
            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            const mentionId = 'ou_3838b5973d31e1b7e7ef960120e600cc';
            const mentionName = "Nguyễn Thị Trúc Ly";
            const { larkTitle } = payload
           
            const notifLarkGroup = this.urlConfigService.getLarkNotiOnKioskBVThongNhatPaymentUrl
            
            const msgContent = larkKioskBVThongNhatOnPayment({
                larkEnv: larkEnv,
                message: larkTitle,
                medproId: payload.medproId,
                hoso: payload.hoso,
                maphieu: payload.maphieu,
                trangthai: payload.trangthai,
                thoigian: payload.thoigian,
                benhvien: payload.benhvien,
                memtionId: mentionId,
                memtionName: mentionName
            });


            this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                webHub: notifLarkGroup,
                data: {
                    msg_type: 'interactive',
                    card: msgContent,
                }
            })

            return {
                isOK: true,
                msgContent
            }
        } catch (error) {
            console.log(error)
            return {
                isOK: false,
                error: error?.message || 'ERROR_SEND_LARK_NOTIF'
            }
        }
    }


    // ? ******************************************************************************************************************************
    // ? GỬI THÔNG BÁO CHO GROUP LARK KHI NGƯỜI DÙNG SUBMIT FORM CASH BACK
    // ? ******************************************************************************************************************************
    public async sendNotiOnCashBackFormSubmit(payload: CashBackFormSubmitDto) {
      try{  
            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            const mentionId = 'ou_9ba7d98005a36fef85ea88562fd34331';
            const mentionName = "Đỗ Khắc Tuấn Anh";
            const tagname = `Hệ thống ghi nhận thông tin thanh toán hoàn tiền của người dùng liên quan tới ${payload.benhvien}.`
            const notifLarkGroup = this.urlConfigService.getLarkNotiOnCashBackSurveyUrl;
            
            const msgContent = larkMsgOnCashBackSurveyTemplate({
                larkEnv: larkEnv,
                messageRemiderNotPamentTop: tagname,
                creator: payload.medproId,
                action: payload.hoso,
                time: payload.thoigian,
                hospitalName: payload.benhvien,
                memtionId: mentionId,
                memtionName: mentionName,
                bookingCode: payload.maphieu,
                bookingStatus: payload.trangthai,
                ratingHospital: payload.danhgiacsyt,
                ratingMedpro: payload.danhgiamedpro,
                bankName: payload.tennganhang,
                bankAccountNumber: payload.sotaikhoan,
                bankAccountName: payload.tentaikhoan,
                totalAmount: payload.tongtien,
            })

            this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                webHub: notifLarkGroup,
                data: {
                    msg_type: 'interactive',
                    card: msgContent,
                }
            })

            return {
                isOK: true,
                msgContent
            }
        } catch (error) {
            this.logger.log({
                error
            })
            console.log(error)
            return {
                isOK: false,
                error: error?.message || 'ERROR_SEND_LARK_NOTIF'
            }
        }
    }


    // ? ******************************************************************************************************************************
    // ? GỬI THÔNG BÁO CHO GROUP LARK KHI NGƯỜI DÙNG KHIẾU NẠI
    // ? ******************************************************************************************************************************
    public async sendNotiOnComplainBooking(payload: BookingComplainDTO) {
        try {
            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            const mentionId = 'ou_3838b5973d31e1b7e7ef960120e600cc';
            const mentionName = 'Nguyễn Thị Trúc Ly';
            const tagname = `Hệ thống đã ghi nhận khiếu nại của ${payload.medproId} liên quan đến phiếu khám tại ${payload.hospitalName}.`;
            const notifLarkGroup = this.urlConfigService.getLarkNotiOnComplainBookingUrl;

            const msgContent = larkMsgEventComplainBooking({
                larkEnv: larkEnv,
                message: tagname,
                bookingCode: payload.bookingCode,
                medproId: payload.medproId,
                complain: payload.complain,
                hospitalName: payload.hospitalName,
                patientName: payload.patientName,
                memtionId: mentionId,
                memtionName: mentionName,
            });
            this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                webHub: notifLarkGroup,
                data: {
                    msg_type: 'interactive',
                    card: msgContent,
                },
            });

            return {
                isOK: true,
                msgContent,
            };
        } catch (error) {
            this.logger.log({
                error,
            });
            console.log(error);
            return {
                isOK: false,
                error: error?.message || 'ERROR_SEND_LARK_NOTIF',
            };
        }
    }

    // ? ******************************************************************************************************************************
    // ? GỬI THÔNG BÁO LARK KHI CÓ ĐĂNG KÝ TƯ VẤN CARE247
    // ? ******************************************************************************************************************************
    public async larkNotifCare247ConsultationRegistration(payload: LarkNotifCare247ConsultationRegistrationDto) {
        try {
            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            const mentionId = 'ou_5fe4fb7b52445dab4fe2e7b3b9b3c704';
            const mentionName = 'Nguyễn Thị Linh Chi';
            const currentTime = moment().utc().add(7, 'hours').format('HH:mm DD-MM-YYYY');
            
            // URL webhook Lark group cho Care247 consultation registration
            const notifLarkGroup = this.urlConfigService.getLarkNotiCare247ConsultationRegistrationUrl;

            const msgContent = larkCare247ConsultationRegistrationTemplate({
                larkEnv: larkEnv,
                fullname: payload.fullname,
                phone: payload.phone,
                email: payload?.email,
                time: currentTime,
                mentionId: mentionId,
                mentionName: mentionName,
            });

            this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                webHub: notifLarkGroup,
                data: {
                    msg_type: 'interactive',
                    card: msgContent,
                },
            });

            return {
                isOK: true,
                msgContent,
            };
        } catch (error) {
            this.logger.log({
                error,
            });
            console.log(error);
            return {
                isOK: false,
                error: error?.message || 'ERROR_SEND_LARK_NOTIF',
            };
        }
    }

    // ? ******************************************************************************************************************************
    // ? GỬI THÔNG BÁO CHO NGƯỜI DÙNG ĐẶT KHÁM THTM
    // ? ******************************************************************************************************************************
    public async sendNotiOnTHTMBooking(payload: SendNotiOnTHTMBookingDto) {
        try {
            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            const mentionId = 'ou_9ba7d98005a36fef85ea88562fd34331';
            const mentionName = "Đỗ Khắc Tuấn Anh";
            const tagname = `[PKH-MEDPRO]-Đặt khám theo chuyên khoa-${payload.benhvien}-${payload.thoigian} [MAIL]`
            const notifLarkGroup = this.urlConfigService.getLarkNotiOnTHTMBookingUrl;

            const msgContent = larkMsgTHTMOnBooking({
                larkEnv: larkEnv,
                messageRemiderNotPament: tagname,
                creator: payload.medproId,
                action: payload.hoso,
                time: payload.thoigian,
                hospitalName: payload.benhvien,
                memtionId: mentionId,
                memtionName: mentionName,
                bookingCode: payload.maphieu,
                status: payload.trangthai,
            });
            
            this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                webHub: notifLarkGroup,
                data: {
                    msg_type: 'interactive',
                    card: msgContent,
                }
            })

            return {
                isOK: true,
                msgContent
            }
        } catch (error) {
            console.log(error)
            return {
                isOK: false,
                error: error?.message || 'ERROR_SEND_LARK_NOTIF'
            }
        }
    }

    // ? ******************************************************************************************************************************
    // ? GỬI THÔNG BÁO CHO NGƯỜI DÙNG CÓ HÀNH VI BẤT THƯỜNG   
    // ? ******************************************************************************************************************************
    public async sendNotiWarningUserBehavior(payload: SendNotiWarningUserBehaviorDto) {
        try {
            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            const mentionId = 'ou_9ba7d98005a36fef85ea88562fd34331';
            const mentionName = "Đỗ Khắc Tuấn Anh";
            const tagname = `Hệ thống ghi nhận hành vi người dùng liên quan tới ${payload.benhvien}.`
            const notifLarkGroup = this.urlConfigService.getLarkNotiAnomalousUserUrl;

            const msgContent = larkMsgWarningUserBehaviorTemplate({
                larkEnv: larkEnv,
                messageRemiderNotPament: tagname,
                creator: payload.medproId,
                action: payload.hoso,
                time: payload.thoigian,
                hospitalName: payload.benhvien,
                memtionId: mentionId,
                memtionName: mentionName,
                cityName: payload.cityName,
                birthday: payload.birthdate,
                sex: payload.sex
            });

            this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                webHub: notifLarkGroup,
                data: {
                    msg_type: 'interactive',
                    card: msgContent,
                }
            })

            return {
                isOK: true
            }
        } catch (error) {
            console.log(error)
            return {
                isOK: false,
                error: error?.message || 'ERROR_SEND_LARK_NOTIF'
            }
        }
    }

    private uniqueClientIds(clientIds: string[]): string[] {
        return uniq(clientIds);
    }

    async createMessageEvent(payload: CreateMessageEventDto, repoName = ''):Promise<any>{
        // kiểm tra các trường hợp sẽ có event data riêng
        let transporter: string;
        let allowSaveMessage = false;
        const { isPushNotif, topic: topicId, topicBooking, type, userId, appId, isSendMail, eventData, pushDevicesV1, locale = 'vi', ...data } = payload;
        let resultData: any = { url: 'https://medpro.com.vn' };
        // console.log(JSON.stringify(payload, null, 2))
        // console.log('type: ', type)
        // console.log('isPushNotif: ', isPushNotif)
        
        if (isPushNotif && [0, 1, 2, 5, 7, 99, 100, 102, 101, 103, 105, 106].includes(type)) {
            allowSaveMessage = true;
            transporter = TransporterEvent.PUSH;

            switch (topicId) {
                case 'bookings.confirm':
                case 'bookings.cancel':
                case 'bookings.reminder':
                case 'bookings.telemed.reminder':
                case 'bookings.not.payment.yet.reminder':
                case 'booking.update':
                case 'bookings.remind.exam':
                    resultData.transactionId = eventData.transactionId;
                    resultData.type = eventData.type;
                    break;
                case 'orders.confirm':
                case 'orders.cancel':
                    resultData.transactionId = eventData.transactionId;
                    resultData.type = eventData.type;
                    break;
                case 'messages.push-inform-notif':
                case 'messages.push-inform-notif-tckqcr':
                    resultData.type = eventData.type;
                    resultData.title = data.title;
                    resultData.content = data.content;
                    break;
                case 'his.confirm':
                    resultData.transactionId = eventData.transactionId;
                    resultData.type = eventData.type;
                    break;
                case 'invoice.confirm' /* thêm phần xem thông tin hóa đơn */:
                    resultData.transactionId = eventData.transactionId;
                    resultData.type = eventData.type;
                    resultData.invoiceUrl = eventData.invoiceUrl;
                    resultData.topicId = topicId;
                    break;
                case 'sync-booking.failed':
                    resultData.transactionId = eventData.transactionId;
                    resultData.type = eventData.type;
                    resultData.content = data.title;
                    resultData.title = data.title;
                    resultData.url = '';
                    break;
                case 'booking.survey.form':
                    resultData.transactionId = eventData.transactionId;
                    resultData.type = eventData.type;
                    resultData.content = eventData?.content || data.title;
                    resultData.title = data.title;
                    resultData.url = eventData?.url || resultData.url;
                    break;
                case 'bookings.tai-kham-nhanh':
                    resultData.urlId = eventData.urlId;
                    resultData.type = type;
                    delete resultData.url;
                    break;
                case 'messages.call-19002115':
                    delete resultData.url;
                    resultData.type = type;
                    resultData.title = eventData.title;
                    resultData.content = eventData.title;
                    break;
                case 'remider.exam.umc':
                case 'booking.notif-telemed-now':
                case' bookings.refund':
                    resultData = { ...eventData };
                    break;
                case 'care247_24hours_content':
                    resultData.transactionId = eventData.transactionId;
                    resultData.type = eventData.type;
                    resultData.content = eventData?.content || data.title;
                    resultData.title = eventData.title;
                    resultData.url = eventData?.url || resultData.url;
                    resultData.tab = eventData?.tab || 1;
                    break;
                default:
                    resultData = { ...eventData };
                    break;
            }

            // console.log('resultData', JSON.stringify(resultData, null, 2))

            const [partners, deviceInfos] = await Promise.all([
                this.partnerConfigModel.findOne({ partnerId: appId }, { partnerId: true, notifAppId: true, notifApiKey: true }).exec(),
                this.pushDeviceModel.find({ userId, appId }).exec(),
            ]);

            // console.log('partners', JSON.stringify(partners.toObject(), null, 2))
            // console.log('deviceInfos', JSON.stringify(deviceInfos, null, 2))

            const messageEventReExam = new Set().add('bookings.tai-kham-nhanh').add('messages.call-19002115');
            let defaultRepo = messageEventReExam.has(topicId) ? 'ReExam' : this.repoService.getRepoName();
            if (repoName) {
                defaultRepo = repoName;
            }

            const clientIds = pushDevicesV1 ? map(pushDevicesV1, 'onesignal_id') : map(deviceInfos, 'clientId');
            // console.log('BEFORE allowSaveMessage && partners && clientIds')
            if (allowSaveMessage && partners && clientIds?.length > 0) {
                // console.log('AFTER allowSaveMessage && partners && clientIds')
                const { notifApiKey, notifAppId } = partners;
                const dataMessageEvent: any = {
                    ...data,
                    locale: locale || 'vi',
                    appId,
                    topic: topicId,
                    topicBooking,
                    type,
                    userId,
                    notifAppId,
                    notifApiKey,
                    clientIds: this.uniqueClientIds(clientIds),
                    eventData: resultData,
                    transporter,
                    repoName: defaultRepo,
                };
                // console.log('dataMessageEvent', JSON.stringify(dataMessageEvent, null, 2))
                const document = new this.messageEventModel(dataMessageEvent);
                await document.save();
            }
        } else if (isSendMail) {
            let mailTemplate: MailTemplateEnum;
            // if (topicId === 'clinic.register' || topicId === 'sync-booking.failed' || topicId === 'tranform-data-schedule-v1.failed' || topicId === 'send.mail.payment.umc') {
            //     allowSaveMessage = true;
            //     transporter = TransporterEvent.MAIL;
            // }
            const topicSet = new Set([
                'clinic.register',
                'sync-booking.failed',
                'tranform-data-schedule-v1.failed',
                'send.mail.payment.umc',
                'invoice.confirm',
                'bookings.confirm',
                'bookings.reExam',
                'update.booking.failed',
                'booking.update',
                'booking-new-notice',
            ]);
            if (topicSet.has(topicId)) {
                if (topicId === 'invoice.confirm') {
                    mailTemplate = MailTemplateEnum.INVOICE_CONFIRM;
                }
                if (topicId === 'booking.update') {
                    mailTemplate = MailTemplateEnum.BOOKING_SUCCESS;
                }
                if ([1, 2].includes(type)) {
                    mailTemplate = MailTemplateEnum.BOOKING_SUCCESS;
                }
                allowSaveMessage = true;
                transporter = TransporterEvent.MAIL;
            }

            if (allowSaveMessage) {
                let defaultRepo = this.repoService.getRepoName();
                switch (topicId) {
                    case 'booking-new-notice':
                        defaultRepo = 'BookingNewNotice';
                        break;
                    default:
                        defaultRepo = this.repoService.getRepoName();
                        break;
                }

                if (repoName) {
                    defaultRepo = repoName;
                }

                this.logger.warn(`Tiến thành lưu event gửi mail vào message event`);
                const document = await this.messageEventModel.create({
                    ...data,
                    appId,
                    topic: topicId,
                    topicBooking,
                    userId,
                    notifAppId: ``,
                    notifApiKey: this.sendgridConfigService.getApiKey(),
                    eventData,
                    transporter,
                    mailTemplate,
                    repoName: defaultRepo,
                });
                await document.save();
            }
        }
        return 'OK';
    }

    private pickAddonServiceAndBuildMessageContentFromMedpoCare(medproCare: any) {
        try {
            const bookedService = medproCare.addonServices[0]
            if (medproCare.type === 'secondary') {
                return `Đặt thêm giờ DV Care247 - ${medproCare.name} - ${bookedService.name}`
            } else if (medproCare.type === 'primary') {
                return `Đặt sau DV Care247 - ${bookedService.name}`
            } else if (medproCare.type === 'independent') {
                return `Đặt riêng DV Care247 - ${bookedService.name}`
            } else {
                return `Phiếu khám & DV Care247 - ${bookedService.name}`
            }
        } catch (error) {
            return 'Dịch vụ Care247'            
        }
    }
    
    // ? ******************************************************************************************************************************
    // ? GỬI THÔNG BÁO CHO NGƯỜI DÙNG ĐẶT MEDPRO CARE 
    // ? ******************************************************************************************************************************
    private generateTitleMessage (payload: SendNotiToLarkGroupAndSendMessageToPatientDto & { booking: IBooking }) {
        const bookingCode = payload.booking.bookingCodeV1 || payload.booking.bookingCode;
        const partner = payload.booking.partner as unknown as IHospital;
        const partnerName = partner.name || '';
        return `Phiếu khám ${bookingCode} tại ${partnerName} nhân viên Care247: ${payload.userCs.fullname} - số điện thoại: ${payload.userCs.username}`
    }

    private async pushNotiToBell ({
        bookingById, 
        titleMessage
    }: {
        bookingById: IBooking,
        titleMessage: string
    }) {
        let response: {
            isOK: boolean,
            message?: string
        }
        try {
            await this.createEvent(
                {
                    topicId: 'bookings.reminder',
                    createTime: moment().toISOString(),
                    userId: bookingById.userId,
                    appId: bookingById.appId,
                    title: titleMessage,
                    partnerId: bookingById.partnerId,
                    eventData: {
                        ...bookingById,
                        type: 1,
                    },
                    type: 1,
                },
                true,
                false,
                false,
            );
            // ? Xóa cache thông báo cho khách hàng
            await this.cacheService.delByPattern(`event.*userMongoId=${bookingById.userId}`)
            
            response = {
                isOK: true,
                message: 'Push Noti to Bell success!'
            }
        } catch (error) {
            response = {
                isOK: false,
                message: error?.message || 'Push Noti to Bell fail!'
            }
        }       
        
        return response
    }

    public async pushNotiAndSendMessageToUserBookingMedproCare ({
        bookingId,
        userCs,
        userCsAdmin,
        id
    }: SendNotiToLarkGroupAndSendMessageToPatientDto) {
        const bookingById = await this.bookingModel.findOne({ id: bookingId })
                .populate({
                    path: 'partner', select: {
                        name: true
                    },
                })
                .populate({
                    path: 'patient', select: {
                        name: true, surname: true, sex: true,
                        birthdate: true, birthyear: true, code: true,
                        mobile: true
                    },
                })
                .read('primary')
                .exec()
        const titleMessage = this.generateTitleMessage({
            bookingId,
            userCs,
            booking: bookingById
        });        

       

        const [ 
            // ongSigalPushError,
            onLarkSend,
            // onPushNotiToBell
        ] = await Promise.all([
            // this.pushNotiToUserBookingMedproCare({
            //     bookingId: bookingId,
            //     message: titleMessage
            // }),
            this.findMedproCareBookingDataByBookingIdAndSendNotiToLark(bookingById, id, userCsAdmin),
            // this.pushNotiToBell({ bookingById, titleMessage })
        ])

        // if(ongSigalPushError instanceof OneSignal.HTTPError ){
        //     this.logService.logError(
        //         'Push noti error to user fail!',
        //         {
        //             message: `Push Noti Error`,
        //             data: ongSigalPushError
        //         }
        //     )
        // }

        if(!onLarkSend.success){
            this.logService.logError(
                'Send Lark Noti to group fail!',
                {
                    message: `Lark Noti Error`,
                    data: onLarkSend
                }
            )
        }

        // if(!onPushNotiToBell.isOK){
        //     this.logService.logError(
        //         'Push Noti to Bell fail!',
        //         {
        //             message: `Push Noti to Bell Error`,
        //             data: onPushNotiToBell
        //     })
        // }

        return {
            isOK: onLarkSend.success
        }
    }
   
    private async findMedproCareBookingDataByBookingIdAndSendNotiToLark(bookingById: IBooking, id: string, userCsAdmin: any) {
        try {
            const bookingObj = bookingById.toObject() as IBooking;
            const patient = bookingObj.patient as unknown as IPatient;

            const userById = await this.userModel.findById({ _id: bookingById.userId }).exec();
            const partner = bookingObj.partner as unknown as IHospital

            const bookingCare247 = await this.bookingCare247Model.findById({ _id: id }).exec();
            const bookingCare247Obj = bookingCare247.toObject()

            let status: string;
            switch (bookingObj.status) {
                case -2:
                    status = 'Đã hủy';
                    break;
                case 0:
                    status = 'Chưa thanh toán';
                    break;
                case 1:
                    status = 'Đã thanh toán';
                    break;
                case 6:
                    status = 'Thanh toán hộ';
                    break;
                case 2:
                    status = 'Đã khám';
                    break;
            }

            let patientFullAddress = '';
            try {
                /* tìm lại booking theo _id */
                const findBookingAddress = await this.bookingModel.findById({ _id: bookingById._id }, { patientVersionId: true, patientId: true }).exec();

                let patients = [];
                if (findBookingAddress && typeof findBookingAddress.patientVersion !== typeof undefined) {
                    patients = await this.patientVersionModel
                        .find({ id: findBookingAddress.patientVersionId })
                        // .populate('profession')
                        // .populate('country')
                        // .populate('nation')
                        .populate('city')
                        .populate('district')
                        .populate('ward')
                        .exec();
                } else {
                    patients = await this.patientModel
                        .find({ id: findBookingAddress.patientId })
                        // .populate('profession')
                        // .populate('country')
                        // .populate('nation')
                        .populate('city')
                        .populate('district')
                        .populate('ward')
                        .exec();
                }

                if (patients.length > 0) {
                    const firstPatient = first(patients);
                    patientFullAddress = this.patientMongoService.getTinhThanh(firstPatient);
                }

            } catch (error) {

            }

            let getCSKHUser = null;
            if (bookingObj?.cskhUserId || '') {
                getCSKHUser = await this.userModel.findById({ _id: bookingObj.cskhUserId }).exec();
            }
            let partnerName = ''
            switch (bookingObj.partnerId) {
                case 'umc':
                    partnerName = `${partner.name} - Cơ sở chính`
                    break;
                case 'umc2':
                    partnerName = `${partner.name} - Cơ sở 2`
                    break;
                default:
                    partnerName = partner.name
                    break;
            }

            let maphieus: string
            if (bookingCare247?.bookingsRelation) {
                const relationsBookingCode = bookingCare247?.bookingsRelation?.map(item => {
                    return item.bookingCodeV1
                })
                maphieus = [bookingCare247.bookingCode, ...relationsBookingCode].join()
            }
            
            const objMedproCare = {
                medproId: `${userById.username} - ${userById.fullname}`,
                hoso: `${patient.surname} ${patient.name} (${patient.mobile})`,
                thoigian: moment(bookingObj.date).utc().add(7, 'hours').format('HH:mm DD-MM-YYYY'),
                benhvien: partnerName || '',
                maphieu: !!maphieus ? maphieus : bookingCare247Obj.bookingCode,
                trangthai: status,
                address: patientFullAddress,
                medproCare: bookingCare247Obj,
                ...(getCSKHUser && { cskhUser: `${getCSKHUser.username} - ${getCSKHUser.fullname}`}) 
            }

            console.log('findMedproCareBookingDataByBookingIdAndSendNotiToLark', JSON.stringify(objMedproCare, null, 2))

            const csGuide = bookingCare247Obj?.instructor?.username + ' - ' + bookingCare247Obj?.instructor?.fullname
            const csAdmin = userCsAdmin?.username + ' - ' + userCsAdmin?.fullname
            const { isOK } = await this.sendLarkNotiAssignInstructorMedproCare(objMedproCare, csGuide, csAdmin)

            return {
                success: isOK
            }
        } catch (error) {
            console.log(`${error?.message || 'Error: CSKH điều phối người hướng dẫn'}`)
            return {
                success: false,
                message: error?.message || 'Error: CSKH điều phối người hướng dẫn'
            }
        }

    }

    async manualFindMedproCareBookingDataByBookingIdAndSendNotiToLark(body: { bookingId: string, transactionId: string }) {
        try {
            const { bookingId, transactionId } = body
            const bookingById = await this.bookingModel.findOne({ id: bookingId })
                .populate({
                    path: 'partner', select: {
                        name: true
                    },
                })
                .populate({
                    path: 'patient', select: {
                        name: true, surname: true, sex: true,
                        birthdate: true, birthyear: true, code: true,
                        mobile: true
                    },
                })
                .read('primary')
                .exec()
            const bookingObj = bookingById.toObject() as IBooking;

            const bookingCare247 = await this.bookingCare247Model.findOne({ transactionId })
                    .populate({ path: 'user', select: { fullname: true, username: true } })
                    .exec();
            const bookingCare247Obj = bookingCare247.toObject();

            const payment: any = await this.paymentModel.findOne({ transactionId }).exec()
            if (payment) {
                await this.bookingCare247Model.findByIdAndUpdate(
                    { _id: bookingCare247._id }, 
                    { $set: { payment: payment._id } },
                ).exec();
            }

            try {
                const bookingCare247Constraint = new this.bookingCare247ConstraintUserModel({
                    userId: bookingCare247.userId,
                    user: bookingCare247.userId,
                    transactionIds: [transactionId],
                    care247: [bookingCare247Obj._id],
                    latestPartner: bookingCare247Obj.partner,
                    latestPartnerTime: bookingCare247Obj.date
                });
                await bookingCare247Constraint.save();
            } catch (error) {
                console.log('error', error);
                try {
                    const existConstraint = await this.bookingCare247ConstraintUserModel
                            .findOne({ userId: bookingCare247.userId })
                            .populate({ path: 'care247', select: { date: true, partner: true } })
                            .exec();
                    const existConstraintrObj = existConstraint.toObject();
                    const care247List = [...existConstraintrObj.care247, { date: bookingCare247Obj.date, partner: bookingCare247Obj.partner }];
                    const bookingDates = care247List.map(c => c.date);
                    const latestBookingDate = this.utilService.getLatestDate(bookingDates);
                    const groupCare247ByDatePartner = reduce(care247List, (acc, item) => {
                        acc[item.date] = item.partner;
                        return acc;
                    }, {});
                    const transactions = [...existConstraintrObj.transactionIds, transactionId];
                    await this.bookingCare247ConstraintUserModel.findByIdAndUpdate(
                        { _id: existConstraint._id }, 
                        {   
                            transactionIds: [...new Set(transactions)],
                            times: transactions.length,
                            care247: [...new Set([...existConstraintrObj.care247, bookingCare247Obj._id])],
                            multiple: 2,
                            latestPartner: groupCare247ByDatePartner[latestBookingDate],
                            latestPartnerTime: latestBookingDate
                        },
                    ).read('primary').exec();
                } catch (error) {
                    console.log('error catch', error);
                                        
                }
            }

            //Update data kpi cskh
            try {
                await this.kpiCskhModel.findOneAndUpdate({ bookingId: bookingObj._id }, { mode: 'care247' }).exec();
            } catch (error) {
                console.log('error', error);
            }

            const patient = bookingObj.patient as unknown as IPatient;

            const userById = await this.userModel.findById({ _id: bookingById.userId }).exec();
            const partner = bookingObj.partner as unknown as IHospital

            let status: string;
            switch (bookingObj.status) {
                case -2:
                    status = 'Đã hủy';
                    break;
                case 0:
                    status = 'Chưa thanh toán';
                    break;
                case 1:
                    status = 'Đã thanh toán';
                    break;
                case 6:
                    status = 'Thanh toán hộ';
                    break;
                case 2:
                    status = 'Đã khám';
                    break;
            }

            let patientFullAddress = '';
            try {
                /* tìm lại booking theo _id */
                const findBookingAddress = await this.bookingModel.findById({ _id: bookingById._id }, { patientVersionId: true, patientId: true }).exec();

                let patients = [];
                if (findBookingAddress && typeof findBookingAddress.patientVersion !== typeof undefined) {
                    patients = await this.patientVersionModel
                        .find({ id: findBookingAddress.patientVersionId })
                        // .populate('profession')
                        // .populate('country')
                        // .populate('nation')
                        .populate('city')
                        .populate('district')
                        .populate('ward')
                        .exec();
                } else {
                    patients = await this.patientModel
                        .find({ id: findBookingAddress.patientId })
                        // .populate('profession')
                        // .populate('country')
                        // .populate('nation')
                        .populate('city')
                        .populate('district')
                        .populate('ward')
                        .exec();
                }

                if (patients.length > 0) {
                    const firstPatient = first(patients);
                    patientFullAddress = this.patientMongoService.getTinhThanh(firstPatient);
                }

            } catch (error) {

            }
            let maphieus: string
            if (bookingCare247?.bookingsRelation) {
                const relationsBookingCode = bookingCare247?.bookingsRelation?.map(item => {
                    return item.bookingCodeV1
                })
                maphieus = [bookingCare247.bookingCode, ...relationsBookingCode].join()
            }
            let partnerName = ''
            switch (bookingObj.partnerId) {
                case 'umc':
                    partnerName = `${partner.name} - Cơ sở chính`
                    break;
                case 'umc2':
                    partnerName = `${partner.name} - Cơ sở 2`
                    break;
                default:
                    partnerName = partner.name
                    break;
            }

            let getCSKHUser = null;
            if(bookingObj?.cskhUserId || ''){
                getCSKHUser = await this.userModel.findById({_id: bookingObj.cskhUserId}).exec();
            }


            const objMedproCare = {
                medproId: `${userById.username} - ${userById.fullname}`,
                hoso: `${patient.surname} ${patient.name} (${patient.mobile})`,
                thoigian: moment(bookingObj.date).utc().add(7, 'hours').format('HH:mm DD-MM-YYYY'),
                benhvien: partnerName || '',
                maphieu: !!maphieus ? maphieus : bookingCare247.bookingCode,
                trangthai: `${status} (${payment?.medproCareFee})`,
                address: patientFullAddress,
                medproCare: bookingCare247,
                ...(getCSKHUser && { cskhUser: `${getCSKHUser.username} - ${getCSKHUser.fullname}`})
            }

            console.log('findMedproCareBookingDataByBookingIdAndSendNotiToLark', JSON.stringify(objMedproCare, null, 2))

            const csGuide = bookingCare247?.instructor?.username + ' - ' + bookingCare247?.instructor?.fullname
            const { isOK } = await this.sendLarkNotiOnUserBookingMedproCare(objMedproCare, csGuide)

            try {
                const { medproCare } = await this.getMedproCareTransaction(bookingObj.partnerId)
                let locale: string
                switch (true) {
                    case (bookingCare247.addonServices[0].id === medproCare.addonServices[0].id):
                        locale = 'vi'
                        break;
                    case (bookingCare247.addonServices[0].id === medproCare.addonServices[1].id):
                        locale = 'en'
                        break;
                    case (bookingCare247.addonServices[0].id === medproCare.addonServices[2].id):
                        locale = 'km'
                        break;
                    default:
                        locale = 'vi'
                        break;
                }
                this.eventEmmiter.emit(SEND_SMS_CARE247_AFTER_SUCCESS, {
                    locale,
                    mobile: bookingCare247.get('user.username'),
                    service: bookingCare247.addonServices[0].name,
                    user: bookingCare247.userId, 
                    booking: bookingCare247.bookingId,
                    bookingCare247: bookingCare247._id
                });
            } catch (error) {
                console.log('error sms', error);
            }

            return {
                success: isOK
            }
        } catch (error) {
            console.log(`${error?.message || 'Error: CSKH điều phối người hướng dẫn'}`)
            return {
                success: false,
                message: error?.message || 'Error: CSKH điều phối người hướng dẫn'
            }
        }

    }

    private async pushNotiToUserBookingMedproCare ({
        bookingId,
        message
    }: {
        bookingId: string,
        message: string,
    }) {
        return this.pushNotifService.pushNotifyToPatient({
            bookingId,
            message
        })
    }

    public async sendLarkNotiOnUserBookingMedproCare(payload: SendNotiOnMedproCareBookDto , csGuide?: string) {
        try {
            const {
                medproId,
                hoso,
                thoigian,
                benhvien,
                maphieu,
                trangthai,
                medproCare,
                address,
                cskhUser
            } = payload;

            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            const mentionId = 'ou_25713be9f3f98398b8863313740fc367';
            const mentionName = 'Phạm Lê Diệu Hương';
            const now = moment().utc().add(7, 'hours').format('HH:mm DD-MM-YYYY');
            const tagnameTop = `[PKH-MEDPRO]-Đặt khám-${payload.benhvien}-${now} [MAIL]`
            const tagnameBottom = this.pickAddonServiceAndBuildMessageContentFromMedpoCare(medproCare)

            const objLarkGroup = {
                larkEnv: larkEnv,
                memtionId: mentionId,
                memtionName: mentionName,
                messageRemiderNotPamentTop: tagnameTop,
                messageRemiderNotPamentBottom: tagnameBottom,
                creator: medproId,
                action: hoso,
                time: thoigian,
                hospitalName: benhvien,
                bookingCode: maphieu,
                bookingStatus: trangthai,
                address,
                ...(csGuide && { csGuide: csGuide }),
                ...(cskhUser && { cskhUser: cskhUser }),
            };
            console.log(`objLarkGroup:`, JSON.stringify(objLarkGroup, null, 2))

            const msgContent = larkMsgOnMedproCareBookingTemplate(objLarkGroup)

            this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                webHub: this.urlConfigService.getLarkNotiOnMedproCareBookingUrl,
                data: {
                    msg_type: 'interactive',
                    card: msgContent,
                }
            });

            return {
                isOK: true
            }
        } catch (error) {
            console.log(`sendLarkNotiOnUserBookingMedproCare: ${error?.message || ''}`)
            return {
                isOK: false
            }
        }

    }

    public async sendLarkNotiOnUserCancelBookingMedproCare(body: { id: string }) {
        try {
            const { id } = body
            const bookingCare247 = await this.bookingCare247Model.findById({ _id: id }).read('primary').exec();
            const bookingById = await this.bookingModel.findById({ _id: bookingCare247.bookingId })
                .populate({
                    path: 'partner', select: {
                        name: true
                    },
                })
                .populate({
                    path: 'patient', select: {
                        name: true, surname: true, sex: true,
                        birthdate: true, birthyear: true, code: true,
                        mobile: true
                    },
                })
                .read('primary')
                .exec()
            const bookingObj = bookingById.toObject() as IBooking;


            const patient = bookingObj.patient as unknown as IPatient;

            const userById = await this.userModel.findById({ _id: bookingById.userId }).exec();
            const partner = bookingObj.partner as unknown as IHospital

            let status: string;
            switch (bookingObj.status) {
                case -2:
                    status = 'Đã hủy';
                    break;
                case 0:
                    status = 'Chưa thanh toán';
                    break;
                case 1:
                    status = 'Đã thanh toán';
                    break;
                case 6:
                    status = 'Thanh toán hộ';
                    break;
                case 2:
                    status = 'Đã khám';
                    break;
            }

            let patientFullAddress = '';
            try {
                /* tìm lại booking theo _id */
                const findBookingAddress = await this.bookingModel.findById({ _id: bookingById._id }, { patientVersionId: true, patientId: true }).exec();

                let patients = [];
                if (findBookingAddress && typeof findBookingAddress.patientVersion !== typeof undefined) {
                    patients = await this.patientVersionModel
                        .find({ id: findBookingAddress.patientVersionId })
                        // .populate('profession')
                        // .populate('country')
                        // .populate('nation')
                        .populate('city')
                        .populate('district')
                        .populate('ward')
                        .exec();
                } else {
                    patients = await this.patientModel
                        .find({ id: findBookingAddress.patientId })
                        // .populate('profession')
                        // .populate('country')
                        // .populate('nation')
                        .populate('city')
                        .populate('district')
                        .populate('ward')
                        .exec();
                }

                if (patients.length > 0) {
                    const firstPatient = first(patients);
                    patientFullAddress = this.patientMongoService.getTinhThanh(firstPatient);
                }

            } catch (error) {

            }
            let maphieus: string
            if (bookingCare247?.bookingsRelation) {
                const relationsBookingCode = bookingCare247?.bookingsRelation?.map(item => {
                    return item.bookingCodeV1
                })
                maphieus = [bookingCare247.bookingCode, ...relationsBookingCode].join()
            }
            let partnerName = ''
            switch (bookingObj.partnerId) {
                case 'umc':
                    partnerName = `${partner.name} - Cơ sở chính`
                    break;
                case 'umc2':
                    partnerName = `${partner.name} - Cơ sở 2`
                    break;
                default:
                    partnerName = partner.name
                    break;
            }

            const now = moment().utc().add(7, 'hours').format('HH:mm DD-MM-YYYY');
            let tagnameTop = `[CANCEL] Dịch vụ Care247 của phiếu khám ${partnerName} bị hủy. - ${now}`

            let cancelReason = bookingCare247.cancelInfo.cancelReason

            let cancelUser: string
            switch (true) {
                case bookingObj.cancelledBy === 'PORTAL':
                    cancelUser = bookingObj.userPortalCancelBooking
                    tagnameTop = `Portal thao tác hủy phiếu khám ${bookingCare247.bookingCode} lúc ${now}. Dịch vụ care247 đi kèm theo phiếu khám, hiện tại vẫn đang sử dụng (chưa hủy)` 
                    cancelReason = 'Portal gửi thao tác Hủy phiếu khám bệnh'
                    break;
                case bookingObj.cancelledBy === 'USER':
                case bookingObj.cancelledBy === 'CSKH':
                    const userAction = await this.userModel.findById({ _id: bookingObj.cancelledInfo.userActionId }).exec();
                    cancelUser = `${userAction.username} - ${userAction.fullname}`
                    break;
                default:
                    cancelUser = `${bookingCare247.cancelInfo.mobile} - ${bookingCare247.cancelInfo.fullname}`
                    break;
            }
            // const cancelReason = bookingCare247.cancelInfo.cancelReason

            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            const mentionId = 'ou_25713be9f3f98398b8863313740fc367';
            const mentionName = 'Phạm Lê Diệu Hương';
            // const now = moment().utc().add(7, 'hours').format('HH:mm DD-MM-YYYY');
            // const tagnameTop = `[CANCEL] Dịch vụ Care247 của phiếu khám ${partnerName} bị hủy. - ${now}`
            const tagnameBottom = this.pickAddonServiceAndBuildMessageContentFromMedpoCare(bookingCare247)
            const csGuide = bookingCare247?.instructor?.username + ' - ' + bookingCare247?.instructor?.fullname
            let getCSKHUser = null;
            if(bookingObj?.cskhUserId || ''){
                getCSKHUser = await this.userModel.findById({_id: bookingObj.cskhUserId}).exec();
            }

            const objLarkGroup = {
                larkEnv: larkEnv,
                memtionId: mentionId,
                memtionName: mentionName,
                messageRemiderNotPamentTop: tagnameTop,
                messageRemiderNotPamentBottom: tagnameBottom,
                cancelUser,
                cancelReason,
                creator: `${userById.username} - ${userById.fullname}`,
                action: `${patient.surname} ${patient.name} (${patient.mobile})`,
                time: moment(bookingObj.date).utc().add(7, 'hours').format('HH:mm DD-MM-YYYY'),
                hospitalName: partnerName || '',
                bookingCode: !!maphieus ? maphieus : bookingCare247.bookingCode,
                bookingStatus: status,
                address: patientFullAddress,
                ...(csGuide && { csGuide: csGuide }),
                ...(getCSKHUser && { getCSKHUser: `${getCSKHUser.username} - ${getCSKHUser.fullname}`})
            };
            console.log(`objLarkGroup:`, JSON.stringify(objLarkGroup, null, 2))

            const msgContent = larkMsgOnMedproCareCancelTemplate(objLarkGroup)

            this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                webHub: this.urlConfigService.getLarkNotiCancelBookingMedproCareUrl,
                data: {
                    msg_type: 'interactive',
                    card: msgContent,
                }
            });

            return {
                isOK: true
            }
        } catch (error) {
            console.log(`sendLarkNotiOnUserCancelBookingMedproCare: ${error?.message || ''}`)
            return {
                isOK: false
            }
        }

    }

    public async sendLarkNotiOnUserCancelBookingTelemed(body: { id: string }) {
        try {
            const { id } = body
            const bookingById = await this.bookingModel.findById({ _id: id })
                .populate({
                    path: 'partner', select: {
                        name: true
                    },
                })
                .populate({
                    path: 'patient', select: {
                        name: true, surname: true, sex: true,
                        birthdate: true, birthyear: true, code: true,
                        mobile: true
                    },
                })
                .populate({ path: 'subject', select: { name: true } })
                .populate({ path: 'doctor', select: { name: true, phone: true } })
                .read('primary')
                .exec()
            const bookingObj = bookingById.toObject();


            const patient = bookingObj.patient as unknown as IPatient;

            const userById = await this.userModel.findById({ _id: bookingById.userId }).exec();

            let status: string;
            switch (bookingObj.status) {
                case -2:
                    status = 'Đã hủy';
                    break;
                case 0:
                    status = 'Chưa thanh toán';
                    break;
                case 1:
                    status = 'Đã thanh toán';
                    break;
                case 6:
                    status = 'Thanh toán hộ';
                    break;
                case 2:
                    status = 'Đã khám';
                    break;
            }

            let patientFullAddress = '';
            try {
                /* tìm lại booking theo _id */
                const findBookingAddress = await this.bookingModel.findById({ _id: bookingById._id }, { patientVersionId: true, patientId: true }).exec();

                let patients = [];
                if (findBookingAddress && typeof findBookingAddress.patientVersion !== typeof undefined) {
                    patients = await this.patientVersionModel
                        .find({ id: findBookingAddress.patientVersionId })
                        // .populate('profession')
                        // .populate('country')
                        // .populate('nation')
                        .populate('city')
                        .populate('district')
                        .populate('ward')
                        .exec();
                } else {
                    patients = await this.patientModel
                        .find({ id: findBookingAddress.patientId })
                        // .populate('profession')
                        // .populate('country')
                        // .populate('nation')
                        .populate('city')
                        .populate('district')
                        .populate('ward')
                        .exec();
                }

                if (patients.length > 0) {
                    const firstPatient = first(patients);
                    patientFullAddress = this.patientMongoService.getTinhThanh(firstPatient);
                }

            } catch (error) {

            }

            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            const mentionId = 'ou_5fe4fb7b52445dab4fe2e7b3b9b3c704';
            const mentionName = 'Nguyễn Thị Linh Chi';
            const now = moment(bookingObj.canceledDate).utc().add(7, 'hours').format('HH:mm DD-MM-YYYY');
            const tagnameTop = `[PKH-MEDPRO] - Hủy lịch hẹn tư vấn - Bác sĩ Chuyên Khoa - ${now} [MAIL].`

            const objLarkGroup = {
                larkEnv: larkEnv,
                messageRemiderNotPamentTop: tagnameTop,
                doctor: `${bookingObj.doctor.name} (${bookingObj.doctor.phone})`,
                medproId: `${userById.username} - ${userById.fullname}`,
                patient: `${patient.surname} ${patient.name} (${patient.mobile})`,
                time: moment(bookingObj.date).utc().add(7, 'hours').format('HH:mm DD-MM-YYYY'),
                memtionId: mentionId,
                memtionName: mentionName,
                bookingCode: bookingObj.bookingCode,
                subject: `${bookingObj.subject.name}`,
                bookingStatus: status,
                address: patientFullAddress,
            };
            console.log(`objLarkGroup:`, JSON.stringify(objLarkGroup, null, 2))

            const msgContent = larkMsgOnBookingTelemedCancelTemplate(objLarkGroup)

            this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                webHub: this.urlConfigService.getLarkNotiCancelBookingTelemedUrl,
                data: {
                    msg_type: 'interactive',
                    card: msgContent,
                }
            });

            return {
                isOK: true
            }
        } catch (error) {
            console.log(`sendLarkNotiOnUserCancelBookingTelemed: ${error?.message || ''}`)
            return {
                isOK: false
            }
        }

    }

    public async sendLarkNotiAssignInstructorMedproCare(payload: any, csGuide?: string, csAdmin?: string) {
        try {
            const {
                medproId,
                hoso,
                thoigian,
                benhvien,
                maphieu,
                trangthai,
                medproCare,
            } = payload;

            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            const mentionId = 'ou_25713be9f3f98398b8863313740fc367';
            const mentionName = 'Phạm Lê Diệu Hương';
            const now = moment().utc().add(7, 'hours').format('HH:mm DD-MM-YYYY');
            const tagnameTop = `Hệ thống ghi nhận thao tác điều phối nhân viên care247 vào lúc ${now}`
            const tagnameBottom = `${medproCare.name} - ${medproCare.addonServices[0].name}`

            const objLarkGroup = {
                larkEnv: larkEnv,
                memtionId: mentionId,
                memtionName: mentionName,
                messageRemiderNotPamentTop: tagnameTop,
                messageRemiderNotPamentBottom: tagnameBottom,
                creator: medproId,
                action: hoso,
                time: thoigian,
                hospitalName: benhvien,
                bookingCode: maphieu,
                bookingStatus: trangthai,
                csAdmin: csAdmin,
                csGuide: csGuide
            };
            console.log(`objLarkGroup:`, JSON.stringify(objLarkGroup, null, 2))

            const msgContent = larkMsgAssignInstructorCare247Template(objLarkGroup)

            this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                webHub: this.urlConfigService.getLarkNotiAssignInstructorUrl,
                data: {
                    msg_type: 'interactive',
                    card: msgContent,
                }
            });

            return {
                isOK: true
            }
        } catch (error) {
            console.log(`sendLarkNotiOnUserBookingMedproCare: ${error?.message || ''}`)
            return {
                isOK: false
            }
        }

    }

    @OnEvent(MESSAGE_EVENT_MESSAGE_CTA, { async: true })
    async createMessageCTAEvent(payload: {
        topic: string,
        appId: string,
        userId: string,
        title: string,
        content: string,
        type: number,
        partnerId: string,
        eventData: object,
        isPushNotif: boolean,
        isNotif: boolean,
        isSendMail: boolean,
        devices: [{
            id: string;
            clientId: string;
            clientToken: string;
        }]
    }, repoName = ''): Promise<any> {
        const transporter = TransporterEvent.PUSH;
        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId: payload.appId }, { partnerId: true, notifAppId: true, notifApiKey: true }).exec();
        const clientIds = map(payload.devices, 'clientId');
        if (clientIds) {
            const { notifApiKey, notifAppId } = partnerConfig;
            const { isPushNotif, isNotif, isSendMail, topic: topicId, type, userId, eventData, ...data } = payload;
            const document = new this.messageEventModel({
                ...data,
                title: data.title,
                appId: payload.appId,
                topic: topicId,
                type,
                userId,
                notifAppId,
                notifApiKey,
                clientIds:this.uniqueClientIds(clientIds),
                eventData: {
                    ...eventData,
                    ...(data.content && { content: data.content }),
                },
                transporter,
                repoName: 'MessageCTA',
            });
            await document.save();
        }
        return 'OK'
    }


    @OnEvent(MESSAGE_EVENT_MEDPRO_DOCTOR, { async: true })
    async createMessageEventMedproDoctor(payload: CreateMessageEventMepdroDoctorDto, repoName = ''):Promise<any> {
        // kiểm tra các trường hợp sẽ có event data riêng
        let transporter: string;
        const appId = 'medprodoctor';

        const { isPushNotif, isNotif, isSendMail, topic: topicId, topicBooking, type, userId, eventData, ...data } = payload;
        if (isPushNotif) {
            transporter = TransporterEvent.PUSH;

            const [partnerConfig, deviceInfos] = await Promise.all([
                this.partnerConfigModel.findOne({ partnerId: appId }, { partnerId: true, notifAppId: true, notifApiKey: true }).exec(),
                this.pushDeviceModel.find({ userId }).exec(),
            ]);

            const defaultRepo = repoName || "MedproDoctor";

            const clientIds = map(deviceInfos, 'clientId');

            if (clientIds) {
                const { notifApiKey, notifAppId } = partnerConfig;
                const document = new this.messageEventModel({
                    ...data,
                    title: data.title,
                    appId: 'medprodoctor',
                    topic: topicId,
                    topicBooking,
                    type,
                    userId,
                    notifAppId,
                    notifApiKey,
                    clientIds:this.uniqueClientIds(clientIds),
                    eventData: {
                        ...eventData,
                        ...(data.content && { content: data.content }),
                    },
                    transporter,
                    repoName: defaultRepo,
                });
                await document.save();
            }
        } else if (isSendMail) {
            let allowSaveMessage = false;

            let mailTemplate: MailTemplateEnum;
            // if (topicId === 'clinic.register' || topicId === 'sync-booking.failed' || topicId === 'tranform-data-schedule-v1.failed' || topicId === 'send.mail.payment.umc') {
            //     allowSaveMessage = true;
            //     transporter = TransporterEvent.MAIL;
            // }
            const topicSet = new Set([
                'clinic.register',
                'sync-booking.failed',
                'tranform-data-schedule-v1.failed',
                'send.mail.payment.umc',
                'invoice.confirm',
                'bookings.confirm',
                'bookings.reExam',
                'update.booking.failed',
                'booking.update',
                'booking-new-notice',
            ]);
            if (topicSet.has(topicId)) {
                if (topicId === 'invoice.confirm') {
                    mailTemplate = MailTemplateEnum.INVOICE_CONFIRM;
                }
                if (topicId === 'booking.update') {
                    mailTemplate = MailTemplateEnum.BOOKING_SUCCESS;
                }
                if ([1, 2].includes(type)) {
                    mailTemplate = MailTemplateEnum.BOOKING_SUCCESS;
                }
                allowSaveMessage = true;
                transporter = TransporterEvent.MAIL;
            }

            if (allowSaveMessage) {
                let defaultRepo = this.repoService.getRepoName();
                switch (topicId) {
                    case 'booking-new-notice':
                        defaultRepo = 'BookingNewNotice';
                        break;
                    default:
                        defaultRepo = this.repoService.getRepoName();
                        break;
                }

                if (repoName) {
                    defaultRepo = repoName;
                }

                this.logger.warn(`Tiến thành lưu event gửi mail vào message event`);
                const document = await this.messageEventModel.create({
                    ...data,
                    appId,
                    topic: topicId,
                    topicBooking,
                    userId,
                    notifAppId: ``,
                    notifApiKey: this.sendgridConfigService.getApiKey(),
                    eventData,
                    transporter,
                    mailTemplate,
                    repoName: defaultRepo,
                });
                await document.save();
            }
        }

        if (isNotif) {
            this.createNotifMedproDoctor({ topicId, userId, eventData, type, ...data});
        }
        return 'OK';
    }

    @OnEvent(PUSH_NOTIF_BOOKING_NEW_NOTICE_LARK, { async: true })
    async onPushNotifBookingNewNoticeLark(payload: { webHub: string, data: any, transactionId: string }) {
        const { webHub, data, transactionId } = payload;
        try {
            const result = (await this.http.post(webHub, {
                ...data,
            }).toPromise()).data;
            return result;
        } catch (error) {
            this.logger.warn(`LARK_NOTIF_BOOKING_NEW_NOTICE ${moment().toISOString()}`);
            console.log('LARK_NOTIF_BOOKING_NEW_NOTICE', error);
            const msg = {
                // to: '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>',
                to: '<EMAIL>',
                from: '<EMAIL>',
                subject: 'Lỗi khi push lark notif - Group Giám sát sự kiện gửi mail!',
                html: `<p>Có lỗi khi push lark notif. kiểm tra VM 2.10  - containerName api-notif-v2-111-live port 14001. Cần thiết thì restart lại <b>docker restart api-notif-v2-111-live</b></p>
                        <i>Chạy lại lệnh sau khi restart docker: <code>curl --location --request PATCH 'http://172.168.2.10:14001/message-event/mail-booking-success-notice?transactionid=${transactionId}</code>'</i>
                    `,
            };
            await this.sendgrid.send(msg);
            return true;
        }
    }

    @OnEvent(PUSH_NOTIF_LARK, { async: true })
    async onPushNotifLark(payload: { webHub: string, data: any }) {
        const { webHub, data } = payload;
        try {
            const result = (await this.http.post(webHub, {
                ...data,
            }).toPromise()).data;
            return result;
        } catch (error) {
            this.logger.warn(`LARK_NOTIF_CALLOUT_CSKH ${moment().toISOString()}`);
            console.log('LARK_NOTIF_CALLOUT_CSKH', error);
            const msg = {
                // to: '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>',
                to: '<EMAIL>',
                from: '<EMAIL>',
                subject: 'Lỗi khi push lark notif - Group CallOUT CSKH!',
                html: `<p>Có lỗi khi push lark notif. kiểm tra VM 2.10  - containerName api-notif-v2-111-live port 14001. Cần thiết thì restart lại <b>docker restart api-notif-v2-111-live</b></p>`,
            };
            await this.sendgrid.send(msg);
            return true;
        }
    }

    @OnEvent(SEND_ZNS_PORTAL)
    async sendZnsPortal(payload: any) {
        const url = await this.globalSettingService.findByKeyAndRepoName('PORTAL_NOTIFICATION_API_URL');
        const znsLog: any = {
            ...payload,
            url,
        }

        try {
            console.log(`${url}/notification/zalo/sending`)
            console.log(`payload: ${JSON.stringify(payload, null, 2)}`)
            const { data } = await this.http.post(`${url}/notification/zalo/sending`, payload).toPromise();
            znsLog.response = data;
            znsLog.status = 1;
        } catch (err) {
            console.error('error sendZnsPortal: ', err);
            znsLog.error = this.utilService.errorHandler(err);
            znsLog.status = 0;
        } finally {
            this.zndLogModel.create(znsLog)
                .catch(err => console.error('error save zndLogModel', err));
        }
    }

    // else if (
    //     isPushNotif && topicId === 'messages.push-notif' && type === 3
    // ) {
    //     allowSaveMessage = true;
    //     transporter = TransporterEvent.PUSH;
    // } else if (
    //     isPushNotif && topicId === 'messages.push-inform-notif' && type === 100
    // ) {
    //     allowSaveMessage = true;
    //     transporter = TransporterEvent.PUSH;
    // } else if (
    //     isPushNotif && topicId === 'his.confirm' && type === 5
    // ) {
    //     allowSaveMessage = true;
    //     transporter = TransporterEvent.PUSH;
    // } else if (isPushNotif === true && type === 99) {
    //     allowSaveMessage = true;
    //     transporter = TransporterEvent.PUSH;
    // }
    // } else if (
    //     isSendMail && topicId === 'bookings.reExam' && type === 8
    // ) {
    //     // to do
    //     // allowSaveMessage = true;
    //     // transporter = TransporterEvent.MAIL;
    // } else if (
    //     isSendMail && topicId === 'invoice.confirm'
    // ) {
    //     // to do
    //     // allowSaveMessage = true;
    //     // transporter = TransporterEvent.MAIL;
    // } else if (
    //     isSendMail && topicId === 'bookings.confirm' && [1, 2].includes(type)
    // ) {
    //     // to do
    //     // allowSaveMessage = true;
    //     // transporter = TransporterEvent.MAIL;
    // }

    @OnEvent(SYNC_V1_EVENT)
    async eventSynV1(bookingObj: any) {
        const { transactionId, actionSyncV1 = 2 } = bookingObj;
        const [findBooking, payment, user] = await Promise.all([
            this.bookingModel
                .findOne({ id: bookingObj.id })
                .populate('partner')
                .populate('patient'),
            this.paymentModel.findOne({ transactionId }).exec(),
            this.userModel.findById(bookingObj.userId).exec(),
        ]);
        const findBookingObj = findBooking.toObject();
        const overrideBookingCode = findBookingObj?.bookingCodeV1 || findBookingObj.bookingCode;
        const { partner, patient } = findBookingObj;
        const topic = 'sync-booking.failed';
        let html = '';
        let title = '';
        let subject = '';
        switch (actionSyncV1) {
            case 1:
                title = `Hủy phiếu ${overrideBookingCode} không thành công! Vui lòng liên hệ tổng đài 1900 2115 để được hổ trợ!`;
                subject = `[${findBookingObj.partnerId.toUpperCase()} - Hủy phiếu] Mã phiếu ${overrideBookingCode} chưa hủy được. CSKH sẽ gọi lại cho khách sau.`;
                html = await this.globalSettingService.findByKeyAndRepoName('HTML_SYNC_V1_FAIL');
                break;
            case 2:
                title = `Mã giao dịch ${transactionId} đã thanh toán thành công, chúng tôi đang xử lý và xác nhận phiếu khám!`;
                subject = `[${findBookingObj.partnerId.toUpperCase()}] Phiếu khám ${overrideBookingCode} đã thanh toán nhưng không đồng bộ được xuống V1`;
                html = await this.globalSettingService.findByKeyAndRepoName('HTML_SYNC_V1_FAIL');
                break;
            case 3: // Hỗ trợ booking bị hủy nếu bên cổng gọi api update trễ
                const mailCskhBookingCancelPaymentLate = await this.globalSettingService.findByKeyAndRepoName(
                    'MAIL_CSKH_BOOKING_CANCEL_PAYMENT_LATE',
                );
                const mailCskhBookingCancelPaymentLateObj = JSON.parse(mailCskhBookingCancelPaymentLate);
                title = mailCskhBookingCancelPaymentLateObj?.title.replace('{transactionId}', transactionId);
                subject = mailCskhBookingCancelPaymentLateObj?.subject.replace('{transactionId}', transactionId);
                html = mailCskhBookingCancelPaymentLateObj?.html;
                break;
        }

        html = html
            .replace('{booking.bookingCode}', overrideBookingCode)
            .replace(
                '{booking.date}',
                moment(findBookingObj.date)
                    .add(7, 'hours')
                    .format('DD-MM-yyyy'),
            )
            .replace('{hospitalName}', partner.name)
            .replace('{payment.transactionId}', payment.transactionId)
            .replace('{payment.subTotal}', `${payment.subTotal}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.') + ' VND')
            .replace('{username}', user.username)
            .replace('{patients.name}', patient.name)
            .replace('{patients.surname}', patient.surname)
            .replace('{patients.sex}', patient.sex === 1 ? 'NỮ' : 'NAM')
            .replace('{patients.phone}', patient.mobile);

        await this.sendEventByTransportMail(topic, {
            from: {
                name: 'Medpro - Giải pháp tiếp cận y tế thông minh',
                email: '<EMAIL>',
            },
            subject,
            html,
        });
    }

    @OnEvent(V1_TRANSFORM_DATA_SCHEDULE_FAIL)
    async eventV1TransformDataScheduleFail(payload: any) {
        const topic = 'tranform-data-schedule-v1.failed';
        const { urlTransform, params, dataKeys } = payload;
        const html =
            `<p>url: ${urlTransform}</p>` +
            `<p>params: ${JSON.stringify(params, null, 2)}</p>` +
            `<p> response : ${JSON.stringify(dataKeys, null, 2)}</p>`;
        await this.sendEventByTransportMail(topic, {
            from: {
                name: 'Medpro - Giải pháp tiếp cận y tế thông minh',
                email: '<EMAIL>',
            },
            subject: `Không tồn tại lịch khám v1`,
            html,
        });
    }

    @OnEvent(PUSH_NOTIF_BOOKING_COMPLETE_INFO, { async: true })
    async onPushNotifBookingCompleteInfo(payload: { webHub: string, data: any, transactionId: string }) {
        const { webHub, data, transactionId } = payload;
        try {
            const result = (await this.http.post(webHub, {
                ...data,
            }).toPromise()).data;
            return result;
        } catch (error) {
            this.logger.warn(`LARK_NOTIF_BOOKING_COMPLETE_INFO ${moment().toISOString()}`);
            console.log('LARK_NOTIF_BOOKING_COMPLETE_INFO', error);
            const msg = {
                to: '<EMAIL>',
                from: '<EMAIL>',
                subject: 'Hồ sơ bệnh nhân chưa hoàn thiện thông tin.',
                html: `<p>Có lỗi khi push lark notif. kiểm tra VM 2.10  - containerName api-notif-v2-111-live port 14001. Cần thiết thì restart lại <b>docker restart api-notif-v2-111-live</b></p>
                        <i>Chạy lại lệnh sau khi restart docker: <code>curl --location --request PATCH 'http://172.168.2.10:14001/message-event/mail-booking-success-notice?transactionid=${transactionId}</code>'</i>
                    `,
            };
            await this.sendgrid.send(msg);
            return true;
        }
    }

    @OnEvent(PUSH_NOTIF_TRACKING_MEDPRO_CARE, { async: true })
    async onPushNotifTrackingMedproCare(payload: { userId: string, patientId: string, partnerId: string }) {
        try {
            console.log('payload', payload);
            
            const { userId, patientId, partnerId } = payload;
            const date = moment().utc().add(7, 'hours').format('YYYYMMDD')
            const medproCareTrackingId = `${userId}_${date}`
            const medproCareTracking = await this.medproCareTrackingModel.findOne({ medproCareTrackingId }).exec();
            const patient = await this.patientModel
                .findById(patientId)
                .populate('city')
                .populate('district')
                .populate('ward')
                .exec();

            if (medproCareTracking) {
                if (medproCareTracking.patientId.includes(patientId)) {
                    const patientTracking = medproCareTracking.patient.map(p => {
                        if (p._id.toString() === patientId) {
                            return {...p, times: ( p.times || 1 ) + 1}
                        } else {
                            return {...p, times: p.times || 1}
                        }
                    })
                    await this.medproCareTrackingModel.updateOne({ _id: medproCareTracking._id }, 
                        { patient: patientTracking, trackingTimes: medproCareTracking.trackingTimes + 1 }).exec();
                } else {
                    const patientIds = [...medproCareTracking.patientId, patientId]
                    const patientTracking = [...medproCareTracking.patient, {...patient.toObject(), times: 1}]
                    await this.medproCareTrackingModel.updateOne({ _id: medproCareTracking._id }, 
                        { patientId: patientIds, patient: patientTracking, trackingTimes: medproCareTracking.trackingTimes + 1 }).exec();
                }
            } else {
                const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
                const user = (await this.userModel.findById(userId, { username: true, fullname: true, email: true }).exec());
                const currentDate = moment().utc().add(7, 'hours').format('HH:mm - DD/MM/YYYY');
                const hospital = await this.hospitalModel.findOne({ partnerId }, { name: true }).exec();

                const patientFullAddress = this.patientMongoService.getFullAddress(patient);
                
                const msgContent = larkTrackingMedproCare({
                    larkEnv,
                    message: `Hệ thống ghi nhận người dùng có thao tác chọn dịch vụ Medpro Care vào lúc ${currentDate} khi đặt khám tại ${hospital.name}.`,
                    user: `${user.username} - ${user?.fullname}`,
                    patient: `${patient?.surname} ${patient?.name} - ${patient?.mobile}`,
                    address: patientFullAddress
                });

                const newTracking = new this.medproCareTrackingModel({
                    medproCareTrackingId,
                    userId,
                    patientId,
                    user,
                    patient,
                    trackingTimes: 1,
                });
                await newTracking.save();

                const result = (await this.http.post(this.larkConfig.getLarkNotiOnTrackingMedproCare, {
                    msg_type: 'interactive',
                    card: msgContent,
                }).toPromise()).data;
                return result;
            }
        } catch (error) {
            console.log('PUSH_NOTIF_TRACKING_MEDPRO_CARE payload', payload);
            this.logger.warn(`PUSH_NOTIF_TRACKING_MEDPRO_CARE ${moment().toISOString()}`);
            console.log('PUSH_NOTIF_TRACKING_MEDPRO_CARE', error);
            const msg = {
                to: '<EMAIL>',
                from: '<EMAIL>',
                subject: 'Thông báo tracking Medpro Care.',
                html: `<p>Có lỗi khi push lark notif. kiểm tra VM 2.10  - containerName api-notif-v2-111-live port 14001. Cần thiết thì restart lại <b>docker restart api-notif-v2-111-live</b></p>`,
            };
            await this.sendgrid.send(msg);
            return true;
        }
    }
    
    @OnEvent(LARK_NOTIF_PAYMENT_ERROR, { async: true })
    async onPushLarkNotifPaymentError(payload: any) {
        try {
            console.log('payload', payload);
            
            const { paymentHubData, errorBody, message } = payload;
            
            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            const user = (await this.userModel.findById(paymentHubData.userId, { username: true, fullname: true, email: true }).exec());
            const bookingDateString = errorBody?.data?.timestamp ? moment(errorBody?.data?.timestamp).utc().format('HH:mm DD-MM-YYYY') : ''
            const hospital = await this.hospitalModel.findOne({ partnerId: paymentHubData.partnerId }, { name: true }).exec();
            
            const msgContent = larkPaymentError({
                larkEnv,
                message: `PaymentHub Issue - Có lỗi xảy ra khi người dùng thanh toán phiếu khám. ( ${message} )`,
                user: `${user.username} - ${user?.fullname}`,
                patient: `${paymentHubData?.patient?.surname} ${paymentHubData?.patient?.name} - ${paymentHubData?.patient?.mobile}`,
                email: paymentHubData?.email,
                address: paymentHubData?.address,
                city: paymentHubData?.city,
                country: paymentHubData?.country,
                platform: paymentHubData?.platform,
                hospitalName: hospital.name,
                methodName: paymentHubData?.feeInfo?.methodName,
                code: paymentHubData?.feeInfo?.code,
                gatewayId: paymentHubData?.feeInfo?.gatewayId,
                time: bookingDateString,
            });
            const result = (await this.http.post(this.larkConfig.getLarkNotifPaymentError, {
                msg_type: 'interactive',
                card: msgContent,
            }).toPromise()).data;
            return result;
        } catch (error) {
            this.logger.warn(`LARK_NOTIF_PAYMENT_ERROR ${moment().toISOString()}`);
            console.log('LARK_NOTIF_PAYMENT_ERROR', error);
            const msg = {
                to: '<EMAIL>',
                from: '<EMAIL>',
                subject: 'Thông báo LARK_NOTIF_PAYMENT_ERROR.',
                html: `<p>Có lỗi khi push lark notif. kiểm tra VM 2.10  - containerName api-notif-v2-111-live port 14001. Cần thiết thì restart lại <b>docker restart api-notif-v2-111-live</b></p>`,
            };
            await this.sendgrid.send(msg);
            return true;
        }
    }

    @OnEvent(LARK_NOTIF_COOPERATE_MEDPRO, { async: true })
    async onPushLarkNotifCooperateMedpro(payload: any) {
        try {
            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            const time = moment().utc().add(7, 'hours').format('DD-MM-YYYY HH:mm')

            let notifLarkGroup: string;

            
            let msgContent = larkSubmitCooperateMedpro(
                larkEnv,
                time,
                payload
            );

            switch (payload.type) {
                case 1:
                    notifLarkGroup = this.larkConfig.getLarkCooperateMedpro;
                    break;
                case 3:
                    notifLarkGroup = this.larkConfig.getLarkCooperateClinicMedpro;
                    msgContent = larkSubmitCooperateClinicMedpro(
                        larkEnv,
                        time,
                        payload
                    );
                    break;
                default:
                    break;
            }

            if (REPO_NAME_BETA.includes(payload.repoName)) {
                notifLarkGroup = this.larkConfig.getLarkNotifTest;
            }
            const result = (await this.http.post(notifLarkGroup, {
                msg_type: 'interactive',
                card: msgContent,
            }).toPromise()).data;
            return result;
        } catch (error) {
            this.logger.warn(`LARK_NOTIF_COOPERATE_MEDPRO ${moment().toISOString()}`);
            console.log('LARK_NOTIF_COOPERATE_MEDPRO', error);
            const msg = {
                to: '<EMAIL>',
                from: '<EMAIL>',
                subject: 'Thông báo LARK_NOTIF_COOPERATE_MEDPRO.',
                html: `<p>Có lỗi khi push lark notif. kiểm tra VM 2.10  - containerName api-notif-v2-111-live port 14001. Cần thiết thì restart lại <b>docker restart api-notif-v2-111-live</b></p>`,
            };
            await this.sendgrid.send(msg);
            return true;
        }
    }


    @OnEvent(LARK_NOTIF_RECRUITMENT, { async: true })
    async onPushLarkNotifRecruitment(payload: any) {
        try {
            // Lấy cấu hình Lark từ ConfigService
            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            const time = moment().utc().add(7, 'hours').format('DD-MM-YYYY HH:mm');

            // Tạo nội dung thông báo
            const msgContent = larkSubmitApplyJob(larkEnv, time, payload);

            // Gửi thông báo qua Lark
            const result = (await this.http.post(this.larkConfig.getLarkRecruitment, {
                msg_type: 'interactive',
                card: msgContent,
            }).toPromise()).data;

            return result;
        } catch (error) {
            // Ghi log lỗi
            this.logger.warn(`LARK_NOTIF_RECRUITMENT ${moment().toISOString()}`);
            console.log('LARK_NOTIF_RECRUITMENT', error);

            // Gửi email thông báo lỗi qua SendGrid
            const msg = {
                to: '<EMAIL>',
                from: '<EMAIL>',
                subject: 'Thông báo LARK_NOTIF_RECRUITMENT.',
                html: `<p>Có lỗi khi push lark notif cho đăng ký khám doanh nghiệp. Kiểm tra VM 2.10 - containerName api-notif-v2-111-live port 14001. Cần thiết thì restart lại <b>docker restart api-notif-v2-111-live</b></p>`,
            };
            await this.sendgrid.send(msg);

            return true;
        }
    }

    @OnEvent(LARK_NOTIF_ENTERPRISE_REGISTRATION, { async: true })
    async onPushLarkNotifEnterpriseRegistration(payload: any) {
        try {
            // Lấy cấu hình Lark từ ConfigService
            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            const time = moment().utc().add(7, 'hours').format('DD-MM-YYYY HH:mm');

            // Tạo nội dung thông báo
            const msgContent = larkSubmitEnterpriseRegistration(larkEnv, time, payload);

            // Gửi thông báo qua Lark
            const result = (await this.http.post(this.larkConfig.getLarkEnterpriseRegistration, {
                msg_type: 'interactive',
                card: msgContent,
            }).toPromise()).data;

            return result;
        } catch (error) {
            // Ghi log lỗi
            this.logger.warn(`LARK_NOTIF_ENTERPRISE_REGISTRATION ${moment().toISOString()}`);
            console.log('LARK_NOTIF_ENTERPRISE_REGISTRATION', error);

            // Gửi email thông báo lỗi qua SendGrid
            const msg = {
                to: '<EMAIL>',
                from: '<EMAIL>',
                subject: 'Thông báo LARK_NOTIF_ENTERPRISE_REGISTRATION.',
                html: `<p>Có lỗi khi push lark notif cho đăng ký khám doanh nghiệp. Kiểm tra VM 2.10 - containerName api-notif-v2-111-live port 14001. Cần thiết thì restart lại <b>docker restart api-notif-v2-111-live</b></p>`,
            };
            await this.sendgrid.send(msg);

            return true;
        }
    }

    @OnEvent(LARK_NOTIF_SYNC_BOOKING_FAIL, { async: true })
    async onPushNotifSyncBookingFail(payload: { bookingId: string, message?: string }) {
        const { bookingId } = payload;
        try {
            const booking = await this.bookingModel.findById({ _id: bookingId })
                .populate({ path: 'patient', select: { name: true, surname: true }}).exec()
            const bookingObj = booking.toObject()    
            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            const user = await this.userModel.findById(bookingObj.userId).exec();
            const bookingDateString = moment(bookingObj?.date ? bookingObj.date : bookingObj.createdAt).utc().add(7, 'hours').format('HH:mm DD-MM-YYYY')
            const hospital = await this.hospitalModel.findOne({ partnerId: bookingObj.partnerId }, { name: true, short_name: true, features: true, isContractSigned: true, status: true }).exec();
            const msgContent = larkBookingNotifSyncFail({
                larkEnv,
                message: `Fail khi đồng bộ phiếu khám bệnh. ${payload?.message}`,
                user: `${user.username} - ${user?.fullname}`,
                patient: `${bookingObj?.patient?.surname} ${bookingObj?.patient?.name}`,
                bookingCode: booking.bookingCode,
                status: this.utilService.getBookingText(booking),
                time: bookingDateString,
                hospital,
            });

            const newDocument = new this.syncBookingFailConstraintModel({
                bookingId,
            });
            await newDocument.save();

            const result = (await this.http.post(this.larkConfig.getLarkNotifSyncBookingFail, {
                msg_type: 'interactive',
                card: msgContent,
            }).toPromise()).data;
            return result;
        } catch (error) {
            this.logger.warn(`LARK_NOTIF_SYNC_BOOKING_FAIL ${moment().toISOString()}`);
            console.log('LARK_NOTIF_SYNC_BOOKING_FAIL', error);
            const msg = {
                to: '<EMAIL>',
                from: '<EMAIL>',
                subject: 'Fail khi đồng bộ phiếu khám bệnh.',
                html: `<p>Có lỗi khi push lark notif. kiểm tra VM 2.10  - containerName api-notif-v2-111-live port 14001. Cần thiết thì restart lại <b>docker restart api-notif-v2-111-live</b></p>
                        <i>Chạy lại lệnh sau khi restart docker: <code>curl --location --request PATCH 'http://172.168.2.10:14001/message-event/notification/lark-sync-booking-error</code>'</i>
                    `,
            };
            await this.sendgrid.send(msg);
            return true;
        }
    }

    @OnEvent(LARK_NOTIF_CARE247_AFTER_SUCCESS, { async: true })
    async onPushNotifCare247AfterSuccess(payload: any) {
        try {
            await this.manualFindMedproCareBookingDataByBookingIdAndSendNotiToLark(payload)
        } catch (error) {
            this.logger.warn(`LARK_NOTIF_CARE247_AFTER_SUCCESS ${moment().toISOString()}`);
        }
    }
    
    @OnEvent(SEND_SMS_CARE247_AFTER_SUCCESS, { async: true })
    async onSendSmsCare247AfterSuccess(payload: any) {
        try {
            // await this.sendSMSCare247(payload)
            return true;
        } catch (error) {
            this.logger.warn(`SEND_SMS_CARE247_AFTER_SUCCESS ${moment().toISOString()}`);
        }
    }

    async getMessageEventSuccess(fromData: MessageEventDTO): Promise<BaseResponse<any>> {
        const { pageIndex, pageSize = 10 } = fromData;
        try {
            const [rows, totalRows] = await Promise.all([
                this.messageEventSuccessModel
                    .find({}, { notifAppId: false, notifApiKey: false })
                    .sort({ createdAt: 'desc' })
                    .skip(pageIndex * pageSize)
                    .limit(pageSize)
                    .exec(),
                this.messageEventSuccessModel.countDocuments(),
            ]);
            return {
                pageIndex,
                pageSize,
                rows,
                totalRows,
            };
        } catch (error) {
            this.logger.error(`Error when exec getMessageEventSuccess()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async getMessageEventProcess(fromData: MessageEventDTO): Promise<BaseResponse<any>> {
        const { pageIndex, pageSize = 10 } = fromData;
        try {
            const [rows, totalRows] = await Promise.all([
                this.messageEventProcessModel
                    .find({}, { notifAppId: false, notifApiKey: false })
                    .sort({ createdAt: 'desc' })
                    .skip(pageIndex * pageSize)
                    .limit(pageSize)
                    .exec(),
                this.messageEventProcessModel.countDocuments(),
            ]);
            return {
                pageIndex,
                pageSize,
                rows,
                totalRows,
            };
        } catch (error) {
            this.logger.error(`Error when exec getMessageEventProcess()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async getMessageEventProcessFailed(fromData: MessageEventDTO): Promise<BaseResponse<any>> {
        const { pageIndex, pageSize = 10 } = fromData;
        try {
            const [rows, totalRows] = await Promise.all([
                this.messageEventProcessFailModel
                    .find({}, { notifAppId: false, notifApiKey: false })
                    .sort({ createdAt: 'desc' })
                    .skip(pageIndex * pageSize)
                    .limit(pageSize)
                    .exec(),
                this.messageEventProcessFailModel.countDocuments(),
            ]);
            return {
                pageIndex,
                pageSize,
                rows,
                totalRows,
            };
        } catch (error) {
            this.logger.error(`Error when exec getMessageEventProcessFailed()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async rollBackMessageEventFailService(processId: string): Promise<any> {
        try {
            const messageEventFail = await this.messageEventProcessFailModel
                .findOne({ processId })
                .lean()
                .exec();
            if (!messageEventFail) {
                throw new HttpException(`Không tìm thấy dữ liệu với processId là ${processId} `, 404);
            }
            const { processId: aaaa, synStatus, ...data } = messageEventFail;
            await this.messageEventModel.create({ ...data });
            await this.messageEventProcessFailModel.deleteOne({ processId }).exec();
        } catch (error) {
            this.logger.error(`Error when exec rollBackMessageEventFailService()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async getMessageEvents(fromData: MessageEventDTO): Promise<BaseResponse<any>> {
        const { pageIndex, pageSize = 10 } = fromData;
        try {
            const [rows, totalRows] = await Promise.all([
                this.messageEventModel
                    .find({}, { notifAppId: false, notifApiKey: false })
                    .sort({ createdAt: 'desc' })
                    .skip(pageIndex * pageSize)
                    .limit(pageSize)
                    .exec(),
                this.messageEventModel.countDocuments(),
            ]);
            return {
                pageIndex,
                pageSize,
                rows,
                totalRows,
            };
        } catch (error) {
            this.logger.error(`Error when exec getMessageEventSuccess()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async sendEventByTransportMail(topic: string, eventData: CreateMessageEventDataMailDto): Promise<any> {
        const groupMail = await this.globalSettingService.findByKeyAndRepoName('MAIL_SUPPORT_PAYMENT_GROUP');
        if (groupMail) {
            eventData.to = groupMail.split(',');
        }
        this.logger.debug(`Email Event Send To: ${eventData.to}`);
        const payload = {
            topic,
            type: -1,
            isPushNotif: false,
            isSendMail: true,
            eventData,
        };
        this.eventEmmiter.emit(MESSAGE_EVENT, payload);
    }

    sendEventByTransportPush(data: CreateMessageEventPushDto): void {
        const payload = {
            ...data,
            isPushNotif: true,
            isSendMail: false,
        };
        this.eventEmmiter.emit(MESSAGE_EVENT, payload);
    }

    @OnEvent(REMOVE_PATIENT_V1)
    async onRemoveV1Patient(payload: any): Promise<void> {
        const urlV1 = this.restApiOldHospital.NhiDong1RestfulAPI();
        const { patientObj, userMongo, partnerId } = payload;
        if (patientObj.patientIdV1UMC && userMongo.userIdV1) {
            const { patientIdV1UMC } = patientObj;
            const { userIdV1 } = userMongo;
            const session = await this.pkhPatientKnex('session')
                .where('user_id', userIdV1)
                .first();
            const res = await this.http
                .post(
                    `${urlV1}/patient/delete`,
                    { session, patientIdV1UMC, userIdV1 },
                    {
                        headers: {
                            partnerid: partnerId,
                        },
                    },
                )
                .toPromise();
            if (res.status < 200 && res.status >= 400) {
                this.eventEmmiter.emit(SERVICE_LOG_NAME, {
                    name: 'onRemoveV1Patient',
                    summary: 'Xóa v2 => xóa luôn v1',
                    nameParent: 'onRemoveV1Patient',
                    params: { session, patientIdV1UMC, userIdV1 },
                    message: 'Không thể xóa v1 sau khi xóa v2',
                });
            }
        }
    }

    @OnEvent(SEND_MAIL_UMC_AFTER_PAYMENT)
    async onSendMailUmcAfterPayment(transactionId: string): Promise<any> {
        try {
            const env = this.urlConfigService.getEnv();
            const payment = await this.paymentModel.findOne({ transactionId }).exec();
            const keyGlobalDynamicPartner = `HTLM_MAIL_PAYMENT_${payment.partnerId}`.toUpperCase();

            const [hospitalFee, partner, htmlMail] = await Promise.all([
                this.hospitalFeeModel.findOne({ fee_code: payment.feeCode, transactionId }).exec(),
                this.hospitalModel.findOne({ partnerId: payment.partnerId }).exec(),
                this.globalSettingService.findByKeyAndRepoName(keyGlobalDynamicPartner),
            ]);

            if (hospitalFee && partner) {
                const htmlMailHandler = htmlMail
                    .replace(
                        '{imageUrl}',
                        `${this.urlConfigService.getBoUrl()}/static/images/${payment.partnerId}/app/image/logo_phieu_kham.png?t=1111`,
                    )
                    .replace('{hospital.name}', partner.name)
                    .replace('{data.fullname}', hospitalFee.fullname)
                    .replace('{data.bv_id}', `(${hospitalFee?.bv_id})` || '')
                    .replace('{data.fee_code}', hospitalFee.fee_code)
                    .replace('{data.transactionId}', transactionId)
                    .replace('{data.subject_name}', hospitalFee.subject_name)
                    .replace('{data.content}', hospitalFee.content)
                    .replace(/{tienThanhToan}/g, `${hospitalFee.amount}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.'));
                let display = '';
                if (env !== 'PRODUCTION') {
                    display = `[${env}]`;
                }
                const eventData = {
                    to: hospitalFee.email,
                    // to: ['<EMAIL>', '<EMAIL>'],
                    from: {
                        name: 'Medpro - Giải pháp tiếp cận y tế thông minh',
                        email: '<EMAIL>',
                    },
                    // bcc: '<EMAIL>',
                    subject: `${display}${partner.name} - Thanh toán Phiếu tạm ứng ${hospitalFee.fee_code}`,
                    html: htmlMailHandler,
                };
                const payload = {
                    topic: 'send.mail.payment.umc',
                    type: -1,
                    isPushNotif: false,
                    isSendMail: true,
                    eventData,
                };
                this.eventEmmiter.emit(MESSAGE_EVENT, payload);
            }
        } catch (error) {
            throw error;
        }
    }

    // async testEvent(transactionId: string) {
    //     this.eventEmmiter.emit(SEND_MAIL_UMC_AFTER_PAYMENT,transactionId )
    // }

    @OnEvent(INSERT_PATIENT_V1)
    async eventInsertToV1(payload: EventInsertPatientDto): Promise<any> {
        const { partnerId, appId, userV1, patientId } = payload;
        this.logger.debug(`Log params event Insert Patient to V1: ${JSON.stringify(payload, null, 2)}`, 'Event - InsertPatientV1');
        let session: any;
        switch (partnerId) {
            case 'nhidong1':
                session = await this.sessionService.checkExistsNhiDong1SessionByUserID(userV1);
                break;
            case 'dalieuhcm':
                session = await this.sessionService.checkExistsSkinSessionByUserID(userV1);
                break;
            case 'ctchhcm':
                session = await this.sessionService.checkExistsCTCHSessionByUserID(userV1);
                break;
            case 'thuduc':
                session = await this.sessionService.checkExistsThuDucSessionByUserID(userV1);
                break;
            case 'umc':
                session = await this.sessionService.checkExistsUMCSessionByUserID(userV1);
                break;
            default:
                session = {
                    user_id: 0,
                    access_token: '',
                };
                break;
        }
        const params = { user_id: userV1, patient_id: patientId, access_token: session.access_token };
        const url = this.restApiOldHospital.umcMappingUserPatientUrl();
        try {
            const res = await this.http
                .post(url, params, {
                    headers: {
                        partnerid: partnerId,
                    },
                })
                .toPromise();
            const status = res.data?.status;
            if (!status) {
                throw Error(`Không mapping được patient: ${patientId} vào user: ${userV1}`);
            }
        } catch (error) {
            this.logger.error(`Error when exec event eventInsertToV1. Cause: ${error.message}`);
        }
    }

    // async testEvent(transactionId: string) {
    //     const booking = await this.bookingModel.findOne({ transactionId }).populate('patient').exec();
    //     const payment: IPayment = await this.paymentModel.findOne({ transactionId }).exec();
    //     const bookingObj = booking.toObject();
    //     const titleMesasge = `Bạn đã đăng ký khám bệnh thành công. Mã phiếu: ${bookingObj.bookingCode}`;
    //     const statusObj: any = {
    //         status: 2,
    //     };
    //     const dataMessageEvent = {
    //         topic: 'bookings.confirm',
    //         userId: bookingObj.userId,
    //         partnerId: bookingObj.partnerId,
    //         appId: bookingObj.appId,
    //         title: titleMesasge,
    //         eventData: {
    //             ...bookingObj,
    //             paymentStatus: payment.status,
    //             paymentMessage: 'Test Message',
    //             ...statusObj,
    //             transactionId,
    //             type: 1,
    //             emailPatient: bookingObj.patient.email,
    //         },
    //         type: 1,
    //     };
    //     const payload = {
    //         ...dataMessageEvent,
    //         isPushNotif: false,
    //         isSendMail: true,
    //     };
    //     this.eventEmmiter.emit(MESSAGE_EVENT, payload);
    // }

    @OnEvent(CRON_REMIDER_BOOKING_NOT_PAYMENT)
    async remindBookingNotPaymentYet(bookingBefore: IBooking) {
        const url = `${this.urlConfigService.getUrlPushRemindTelemed}/message-event/remind/booking-not-payment`;
        return this.http.patch(url, { ...bookingBefore }).toPromise();
    }

    async handleRemindBookingNotPaymentYet(bookingBefore: any, defaultMinutes: number = 15): Promise<any> {
        let notPaymentYetReminder = defaultMinutes;
        const [getPartnerConfig, messageRemiderNotPamentGlobal] = await Promise.all([
            this.partnerConfigModel.findOne({ partnerId: bookingBefore.partnerId }, { notPaymentYetReminder: true, notPaymentYetReminderSameDay: true, countdown: true, countdownSameDay: true }).exec(),
            bookingBefore.appId === 'medpro' && !['umc', 'umc2', 'umc3'].includes(bookingBefore.partnerId) ?
                this.globalSettingService.findByKeyAndRepoName('TITLE_NOT_PAYMENT_YET_REMINDER_MEDPRO') :
                this.globalSettingService.findByKeyAndRepoName('TITLE_NOT_PAYMENT_YET_REMINDER'),
        ]);

        const checkTrongNgay = moment(bookingBefore.date).isSame(moment(), 'day');

        if (checkTrongNgay && getPartnerConfig.notPaymentYetReminderSameDay) {
            notPaymentYetReminder = getPartnerConfig.notPaymentYetReminderSameDay;
        } else if (!getPartnerConfig.notPaymentYetReminder) {
            notPaymentYetReminder = Number(await this.globalSettingService.findByKeyAndRepoName('NOT_PAYMENT_YET_REMINDER'));
        } else {
            notPaymentYetReminder = getPartnerConfig.notPaymentYetReminder;
        }
        console.log('notPaymentYetReminder', notPaymentYetReminder)
        const remindTime = moment()
            .add(notPaymentYetReminder, 'minutes')
            .toDate();
        const name = `reminder_booking_not_payment_yet_${bookingBefore.bookingCode}_${remindTime.valueOf()}`;
        const job = new CronJob(
            remindTime,
            async () => {
                /* Tìm lại thông tin booking */
                const booking = await this.bookingModel.findById({ _id: bookingBefore._id }).populate('partner').exec();
                const bookingObj = booking.toObject();
                const checkTrongNgay = moment(bookingObj.date).isSame(moment(), 'day');
                const countdown = checkTrongNgay && getPartnerConfig.countdownSameDay ? getPartnerConfig.countdownSameDay : getPartnerConfig?.countdown;

                /* tiến hành kiểm tra xem trạng thái phiếu khám thế nào */
                const setSatusBooking = new Set([0, 6]);
                if (setSatusBooking.has(booking.status)) {
                    const messageRemiderNotPament = messageRemiderNotPamentGlobal.replace(
                        '{boookingCode}',
                        `${booking?.bookingCodeV1 || booking.bookingCode}`,
                    ).replace('{countdown}', `${countdown || 60}`);
                    /* tiến hành bắn event */
                    await this.createEvent(
                        {
                            topicId: 'bookings.not.payment.yet.reminder',
                            createTime: moment().toISOString(),
                            userId: booking.userId,
                            appId: booking.appId,
                            title: `${messageRemiderNotPament}`,
                            partnerId: booking.partnerId,
                            eventData: {
                                ...bookingObj,
                                type: 1,
                            },
                            type: 1,
                        },
                        true,
                        false,
                        false,
                    );

                    const messageInformCSKH = `Phiếu khám ${booking?.bookingCodeV1 || booking.bookingCode} chưa được thanh toán ${notPaymentYetReminder}/${getPartnerConfig?.countdown || 60} phút, kể từ lúc người dùng tạo. Vui lòng kiểm tra và hỗ trợ khách. Xin cảm ơn! `;

                    this.eventEmmiter.emit(MESSAGE_EVENT, {
                        topic: 'bookings.not.payment.yet.reminder',
                        userId: booking.userId,
                        appId: booking.appId,
                        title: `${messageRemiderNotPament}`,
                        partnerId: booking.partnerId,
                        eventData: {
                            ...bookingObj,
                            type: 1,
                        },
                        type: 1,
                        isPushNotif: true,
                        isSendMail: false,
                    });

                    let patients: IPatient[];
                    if (typeof booking.patientVersion !== typeof undefined) {
                        patients = await this.patientVersionModel.find({ id: booking.patientVersionId }).exec();
                    } else {
                        patients = await this.patientModel.find({ id: booking.patientId }).exec();
                    }
                    const firstPatient = first(patients);
                    const patientObj = firstPatient.toObject();
                    const bookingDateString = moment(booking.createdAt)
                        .utc().add(7, 'hours').format('HH:mm DD-MM-YYYY');
                    const thisUser = await this.userModel.findById(booking.userId);
                    const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
                    const msgContent = larkMsgTemplate1(larkEnv, messageInformCSKH, `${thisUser.username} - ${thisUser?.fullname}`, `${patientObj.surname} ${patientObj.name} (${patientObj?.mobile})`, bookingDateString, (bookingObj?.partner?.name || booking.partnerId));

                    this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                        webHub: this.larkConfig.larkBookingNotPaymentYet,
                        data: {
                            msg_type: 'interactive',
                            card: msgContent,
                        },
                    });

                    this.scheduler.deleteCronJob(name);

                } else {
                    this.scheduler.deleteCronJob(name);
                }
            },
            () => {
                this.logger.log(`Complete CronJob push notify reminder for Booking: ${bookingBefore.bookingCode}`);
            },
        );
        try {
            this.scheduler.addCronJob(name, job);
            this.logger.log(`CronJob reminder for Booking: ${bookingBefore.bookingCode} is registered`);
            job.start();
        } catch (error) {
            // this.clientSentry.instance().captureException(error);
        }
    }

    async createEvent(data: CreateEventDTO, isNotif?: boolean, isPushNotif?: boolean, isSendMail?: boolean): Promise<any> {
        const id = uuid.v4().replace(/-/g, '');
        const obj: any = {};
        if (data.userId) {
            obj.userId = data.userId;
        }
        const { type = 0 } = data;
        /* lấy thông tin bookingDate */
        const { eventData }: any = data;
        const objRecommend: any = {};
        if (eventData) {
            if (typeof eventData.type !== typeof undefined && eventData.type === 1) {
                objRecommend.bookingDate = eventData.date;
                objRecommend.type = eventData.type;
                objRecommend.isRecommended = true;
            }
        }
        let eventInfo = data;
        switch (data.topicId) {
            case 'returnResult.telemedPromotion':
                eventInfo = {
                    id,
                    createTime: moment(data.createTime).toDate(),
                    topicId: data.topicId,
                    ...obj,
                    ...objRecommend,
                    appId: data.appId,
                    partnerId: data.appId,
                    title: data.title,
                    isNotif: isNotif ? true : false,
                    isPushNotif: isPushNotif ? true : false,
                    isSendMail: isSendMail ? true : false,
                    content: data?.content,
                    eventData,
                    type,
                };
                break;
            default:
                eventInfo = {
                    id,
                    createTime: moment(data.createTime).toDate(),
                    topicId: data.topicId,
                    ...obj,
                    ...objRecommend,
                    appId: data.appId,
                    partnerId: data.appId,
                    title: data.title,
                    isNotif: isNotif ? true : false,
                    isPushNotif: isPushNotif ? true : false,
                    isSendMail: isSendMail ? true : false,
                    content: (data.eventData as { content?: string })?.content || '',
                    eventData: {
                        ...data.eventData,
                        content: (data.eventData as { content?: string })?.content || '',
                    },
                    type,
                };
                break;
        }
        const event = new this.eventModel(eventInfo);
        // For notification
        const notification = new this.notificationModel(eventInfo);

        this.cacheService.delByPattern(`event:events-unread-by-user.*userMongoId=${data.userId}.*`);
        this.cacheService.delByPattern(`event:events-by-user.*userMongoId=${data.userId}.*`);

        event.save().catch(error => Logger.log(error));
        const listTopicId = new Set([
            'invoice.confirm',
            'his.confirm',
            'bookings.confirm',
            'bookings.cancel',
            'deposit.confirm',
            'orders.confirm',
            'orders.cancel',
            'bookings.reminder',
            'bookings.reExam',
            'bookings.telemed.reminder',
            'bookings.not.payment.yet.reminder',
            'sync-booking.failed',
            'booking.survey.form',
            'bookings.remind.exam',
            'booking-new-notice',
        ]);
        const isTopicIdNotif = listTopicId.has(eventInfo.topicId);
        if (isTopicIdNotif) {
            // đẩy sang chỗ MESAGE EVENT xử lý riêng
            // this.eventEmitter.emit(MESSAGE_EVENT, {
            //     eventId: event._id,
            //     appId: data.appId,
            //     topic: data.topicId,
            //     partnerId: data.partnerId,
            //     type,
            //     title: data.title,
            //     transactionId: eventInfo.eventData?.transactionId,
            //     userId: data.userId,
            //     isPushNotif: isPushNotif ? true : false,
            //     isSendMail: isSendMail ? true : false,
            //     eventData: eventInfo.eventData,
            // });
            notification.save().catch(error => Logger.error(error));
        }
        return true;
    }

    async createNotifMedproDoctor(data: CreateNotifMedproDoctorDto): Promise<any> {
        const id = uuid.v4().replace(/-/g, '');

        // For notification
        return this.medproDoctoNotifModel.create({
            ...data,
            id,
            appId: 'medprodoctor',
        });
    }

    @OnEvent(UPDATE_BOOKING_V1_EVENT)
    async updateBooking(paramRequestV1: any) {
        const { bookingTimeIdv1, buoiv1, scheduleIdv1, subjectIdV1, roomIdV1, doctorIdV1, serviceIdV1, bookingId, error_message } = paramRequestV1;
        const [htmlMail, findBooking] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('HTML_UPDATE_BOOKING_FAIL'),
            this.bookingModel
                .findOne({ id: bookingId })
                .populate('partner')
                .populate('patient')
                .exec(),
        ]);
        const findBookingObj = findBooking.toObject();
        const overrideBookingCode = findBookingObj?.bookingCodeV1 || findBookingObj.bookingCode;
        const { partner, patient } = findBookingObj;
        const empty = 'Empty';
        const html = htmlMail
            .replace('{hospitalName}', partner.name)
            .replace('{bookingCode}', overrideBookingCode)
            .replace('{fullName}', `${patient.surname} ${patient.name}`)
            .replace(`{bookingTimeIdv1}`, bookingTimeIdv1 || empty)
            .replace(`{buoiv1}`, buoiv1 || empty)
            .replace(`{scheduleIdv1}`, scheduleIdv1 || empty)
            .replace(`{subjectIdV1}`, subjectIdV1 || empty)
            .replace(`{roomIdV1}`, roomIdV1 || empty)
            .replace(`{doctorIdV1}`, doctorIdV1 || empty)
            .replace(`{serviceIdV1}`, serviceIdV1 || empty)
            .replace(`{error_message}`, error_message);
        const topic = 'update.booking.failed';
        await this.sendEventByTransportMail(topic, {
            from: {
                name: 'Medpro - Giải pháp tiếp cận y tế thông minh',
                email: '<EMAIL>',
            },
            subject: `[${findBookingObj.partnerId.toUpperCase()}] Cập nhật phiếu khám ${overrideBookingCode} không thành công xuống Api V1`,
            html,
        });
    }

    @OnEvent(HANDLE_UPDATE_STATUS_BOOKING_V1_FAIL)
    async handleUpdateStatusBookingFailV1(payload: RetryUpdateStatusDto): Promise<any> {
        try {
            const { bookingObjId, ...data } = payload;
            const retryTransactionNew = new this.retryTransactionModel({
                dataPaymentUpdate: {
                    ...data,
                },
                booking: bookingObjId,
                constraint: `constraint_${data.transactionId}_${bookingObjId}`,
            });
            await retryTransactionNew.save();
        } catch (error) {
            this.logger.log(error.message);
        }
    }

    @OnEvent(SYNC_USER_INFO, { async: true })
    async syncUserInfo(data: any): Promise<any> {
        const url = `${this.urlConfigService.getUrlAPI119}/user/sync-user-by-phone`;
        return this.http.patch(url, { phone: data?.phone }).toPromise();
    }

    @OnEvent(HANDLE_PATIENT_SORT)
    async handlePatientSort(payload: PatientSortFormDto): Promise<void> {
        const { patientId } = payload;
        const findPatient = await this.patientModel.findOne({ id: patientId }).exec();
        if (findPatient) {
            const findPatientSort = await this.patientSortModel.findOne({ id: patientId });
            try {
                if (findPatientSort) {
                    await this.patientSortModel.findByIdAndUpdate(findPatientSort._id, {
                        ...payload,
                    });
                } else {
                    const patientSortNew = new this.patientSortModel({
                        ...payload,
                    });
                    const resPatientSort = await patientSortNew.save();
                    await this.patientModel.findByIdAndUpdate(findPatient._id, {
                        patientSort: resPatientSort._id,
                    });
                }
            } catch (error) {
                this.logger.error(error?.message);
            }
        }
    }

    @OnEvent(LOG_BOOKING_TREE_TOKEN_USER)
    async logBookingTreeUser(payload: PushDeviceErrorDto): Promise<void> {
        this.eventEmmiter.emit(SERVICE_LOG_NAME, {
            name: 'logBookingTreeUser',
            summary: 'Đăng ký device bị miss booking tree',
            nameParent: 'logBookingTreeUser',
            params: { payload },
            message: 'Check device ở booking tree',
        });
        if (payload?.token) {
            const { token, ...data } = payload;
            const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.createJwtOptions();
            try {
                let user: any;
                const jwtVerify: any = jwt.verify(last(token.split(' ')), jwtOptions.secret);
                /* Kiểm tra lại thông tin cskhInfo */
                const { userMongoId = '' } = jwtVerify;
                const getType = 'dkkb';
                if (isEmpty(payload?.osid) || isEmpty(payload?.ostoken)) {
                    const newPushDeviceError = new this.pushDeviceErrorModel({
                        ...payload,
                        clientId: payload?.osid,
                        clientToken: payload?.ostoken,
                        userId: userMongoId,
                    });
                    await newPushDeviceError.save();
                } else {
                    user = await this.userModel.findById({
                        _id: userMongoId,
                    }, { patients: true }).exec();
                    if (user) {
                        const pushDevice = await this.pushDeviceModel.findOne({
                            clientId: payload?.osid,
                            clientToken: payload?.ostoken,
                            userId: user._id,
                            platform: payload?.platform,
                            type: getType,
                        }).exec();
                        if (!pushDevice) {
                            const id = uuid.v4().replace(/-/g, '').replace(/-/g, '');
                            if (payload?.platform) {
                                const pushDeviceInfo = new this.pushDeviceModel({
                                    id,
                                    clientToken: payload?.ostoken,
                                    clientId: payload?.osid,
                                    platform: payload?.platform,
                                    type: getType,
                                    appId: payload?.appId,
                                    partnerId: payload?.partnerId,
                                    userId: user._id,
                                });
                                await pushDeviceInfo.save();
                            }
                            const newPushDeviceError = new this.pushDeviceErrorModel({
                                ...data,
                                clientId: pushDevice?.clientId,
                                clientToken: pushDevice?.clientToken,
                                userId: user._id,
                            });
                            await newPushDeviceError.save();
                        } else {
                            this.eventEmmiter.emit(SERVICE_LOG_NAME, {
                                name: 'logBookingTreeUser',
                                summary: 'Đăng ký device bị miss booking tree',
                                nameParent: 'logBookingTreeUser',
                                params: { payload, userId: user._id },
                                message: 'Tạo device không thành công!',
                            });
                        }
                    }
                }
            } catch (error) {
                this.logger.error(error?.message);
            }
        }
    }

    @OnEvent(TRACKING_CANCEL_RESERVATIONS)
    async trackingCancelReservation(payload: TrackingCancelReserveDto): Promise<void> {
        const { bookingId, userActionId, cancelledBy } = payload;
        const [booking, userAction] = await Promise.all([
            this.bookingModel.findOne({ id: bookingId }, {
                userId: true,
                transactionId: true,
                partnerId: true,
                appId: true,
                cskhUserId: true,
                date: true,
                smsCode: true,
                id: true,
                treeId: true,
                serviceType: true,
                care247: true,
                patient: true,
                serviceInfo: true
            }).exec(),
            this.userModel.findById(userActionId).exec(),
        ]);
        if (booking) {
            const userBooking = await this.userModel.findOne({ _id: booking.userId }, { username: true }).exec();
            const bookingObj = booking.toObject();
            const userActionObj = userAction?.toObject();
            const userBookingObj = userBooking.toObject();

            const tracking = new this.cancelReservationModel({
                partnerId: bookingObj.partnerId,
                appId: bookingObj.appId,
                userId: bookingObj.userId,
                username: userBookingObj.username,
                bookingDate: bookingObj.date,
                transactionId: bookingObj.transactionId,
                cskhUserId: bookingObj.cskhUserId,
                ...(userActionObj && { userAction: userActionObj._id }),
                booking: bookingObj._id,
                smsCode: bookingObj?.smsCode,
                id: bookingObj.id,
                cancelledBy,
            });

            //Update data kpi cskh 
            try {
                let statusKpi = -2
                if (cancelledBy === 'SYSTEM') {
                    statusKpi = -3
                }
                if (bookingObj?.care247) {
                    await this.kpiCskhModel.findOneAndUpdate({ bookingId: bookingObj._id }, { status: statusKpi, mode: 'care247' }).exec();
                } else {
                    await this.kpiCskhModel.findOneAndUpdate({ bookingId: bookingObj._id }, { status: statusKpi }).exec();
                }
            } catch (error) {
                
            }

            try {
                if ((bookingObj.treeId === 'TELEMED' || bookingObj.treeId === 'TELEMEDNOW') && bookingObj.serviceType === 'ADVISORY') {
                    await this.sendLarkNotiOnUserCancelBookingTelemed({ id: bookingObj._id })
                }
            } catch (error) {
                this.logger.error(error);
            }

            //update constraint user booking one day
            try {
                if (bookingObj.partnerId === 'bvmathcm' || bookingObj.partnerId === 'dkdongnai' ||
                (bookingObj.partnerId === 'bvndgiadinh' && bookingObj?.serviceInfo?.serviceType === 'INSURANCE_ONLY')) {
                    const date = moment(bookingObj.date).utc().format('DDMMYYYY');
                    const userPatientDate = `${bookingObj.userId}_${bookingObj.patient}_${date}`
                    const constraint = await this.constraintUserPatientBookingOneDayModel.findOne({ constraintValue: userPatientDate }).exec();
                    if (constraint && constraint.status === 1 && `${constraint.booking}` === `${bookingObj._id}`) {
                        await this.constraintUserPatientBookingOneDayModel.findByIdAndUpdate(
                            { _id: constraint._id },
                            { status: 0 },
                        ).exec();
                    }
                }
            } catch (error) {
            }

            try {

                await this.bookingModel.updateOne({ _id: bookingObj._id }, {
                    cancelledInfo: {
                        ...payload,
                        cancelledDate: new Date(),
                    },
                    cancelledBy,
                });
                await tracking.save();

                //Update bookingStatus of bookingCare247
                const cancelInfo = {
                    ...payload,
                    fullname: userActionObj?.fullname,
                    mobile: userActionObj?.username,
                    cancelledDate: new Date(),
                    cancelReason: 'DV Care247 huỷ do phiếu khám hủy.',
                };
                if (bookingObj.partnerId !== 'umc') {
                    const bookingsCare247 = await this.bookingCare247Model.find({ bookingId: bookingObj._id, status: 1 }).exec();
                    if (bookingsCare247.length > 0) {
                        if (cancelledBy !== 'PORTAL') {
                            for await (const care247 of bookingsCare247) {
                                await this.bookingCare247Model.findByIdAndUpdate({ _id: care247._id }, {
                                    bookingStatus: -2, status: -2, cancelInfo
                                });
                            }
                        }
                        await this.sendLarkNotiOnUserCancelBookingMedproCare({ id: bookingsCare247[0]._id })
                    }
                } else {
                    const payment = await this.paymentModel.findOne({ transactionId: bookingObj.transactionId }, { type: true }).exec();
                    if (payment.type === 1) {
                        const bookingsCare247 = await this.bookingCare247Model.find({ bookingId: bookingObj._id, status: 1 }).exec();
                        if (bookingsCare247.length > 0) {
                            if (cancelledBy !== 'PORTAL') {
                                for await (const care247 of bookingsCare247) {
                                    await this.bookingCare247Model.findByIdAndUpdate({ _id: care247._id }, {
                                        bookingStatus: -2, status: -2, cancelInfo
                                    });
                                }
                            }
                            await this.sendLarkNotiOnUserCancelBookingMedproCare({ id: bookingsCare247[0]._id })
                        }
                    } else if (payment.type === 5) {
                        const bookings = await this.bookingModel.find({ transactionId: bookingObj.transactionId }).exec();
                        let firstBooking = first(bookings).toObject();
                        try {
                            const earliestDateBooking = bookings.reduce((earliest, current) => {
                                return moment(current.date).isBefore(moment(earliest.date)) ? current : earliest;
                            });
                            firstBooking = earliestDateBooking.toObject()
                        } catch (error) {
                            console.log('error', error);
                        }
                        const bookingsCare247 = await this.bookingCare247Model.find({ bookingId: firstBooking._id, status: 1 }).exec();
                        if (bookingsCare247.length > 0) {
                            if (cancelledBy !== 'PORTAL') {
                                for await (const care247 of bookingsCare247) {
                                    await this.bookingCare247Model.findByIdAndUpdate({ _id: care247._id }, {
                                        bookingStatus: -2, status: -2, cancelInfo
                                    });
                                }
                            }
                            await this.sendLarkNotiOnUserCancelBookingMedproCare({ id: bookingsCare247[0]._id })
                        }
                    }
                }
            } catch (error) {
                this.logger.error(error);
            }
        }
    }

    @OnEvent(HANDLE_BOOKING_CANCLE_PAYMENT_LATE)
    async handleBookingCancelPaymentLate(payload: BookingCancelPaymentLateDto): Promise<void> {
        const { bookingId: id } = payload;
        const bookingCancelTracking = await this.cancelReservationModel
            .findOne({ id })
            .populate({
                path: 'booking',
                select: {
                    userId: true,
                    id: true,
                    transactionId: true,
                },
            })
            .exec();
        if (!bookingCancelTracking) {
            const bookingCancelTrackingObj = bookingCancelTracking.toObject();
            this.eventEmmiter.emit(SYNC_V1_EVENT, { ...bookingCancelTrackingObj.booking, actionSyncV1: 3 });
        }
    }

    // async handleEvenSurveyFormNoty(): Promise<void> {
    //     const envConfigDaLieuSurvay = await this.globalSettingService.findByKeyAndRepoName('CONFIG_FORM_SURVEY_NOTY');
    //     const envConfigDaLieuSurvayObj = JSON.parse(envConfigDaLieuSurvay);
    //     const currentDate = moment().clone();
    //     const configDate = moment(
    //         `${envConfigDaLieuSurvayObj?.time}`.replace('{day}', currentDate.format('YYYY-MM-DD')),
    //     ).toDate();
    //     const gteDate = moment(currentDate).set({ hours: 0, minutes: 0, seconds: 0 }).toDate();
    //     const lteDate = currentDate.set({
    //         hours: configDate.getUTCHours(),
    //         minutes: configDate.getUTCMinutes(),
    //         seconds: configDate.getUTCSeconds(),
    //     }).toDate();
    //     console.log("time: ", {
    //         gteDate,
    //         lteDate
    //     });
    //     const bookings = await this.bookingModel.find(
    //         {
    //             partnerId: { $in: envConfigDaLieuSurvayObj?.partners },
    //             status: { $in: [1, 2] },
    //             bookingCode: { $in: ['T220624EYJNAL', 'T220725LZXJTA'] },
    //             // date: {
    //             //     $gte: gteDate,
    //             //     $lte: lteDate,
    //             // },
    //         },
    //         {
    //             service: true, serviceId: true, subject: true, subjectId: true, doctor: true, sectionId: true, section: true,
    //             room: true, date: true, patient: true, partnerId: true, appId: true, roomId: true, insuranceType: true,
    //             transactionId: true, insuranceCode: true, sequenceNumber: true, checkInRoom: true, bookingNote: true,
    //             insuranceChoice: true, patientVersionId: true, patientId: true, status: true, noPayment: true,
    //             bookingCodeV1: true, syncStatus: true, bookingCode: true, serviceInfo: true, bookingInternalId: true,
    //             addonServices: true, id: true, treeId: true, implementAgent: true, implementLocation: true, userId: true,
    //         },
    //     )
    //         .populate('service')
    //         .populate({ path: 'subject', select: 'name' })
    //         .populate({ path: 'room', select: 'name' })
    //         .populate({ path: 'doctor', select: 'name' })
    //         .populate({ path: 'section', select: 'name' })
    //         .populate({
    //             path: 'patient', select: {
    //                 name: true, surname: true, sex: true,
    //                 birthdate: true, birthyear: true, code: true,
    //             },
    //         }).exec();

    //     if (bookings.length === 0) {
    //         return;
    //     }
    //     console.log('booking', bookings)
    //     const groupBookingPatient = groupBy(bookings, 'patientId');
    //     await Promise.all(Object.entries(groupBookingPatient).map(async ([key, value]) => {
    //         const bookingGourp = value;
    //         const patient = await this.patientModel
    //             .findOne({ id: key }, {
    //                 name: true,
    //                 surname: true,
    //                 mobile: true,
    //             })
    //             .exec();
    //         if (patient && bookingGourp.length) {
    //             const patientObj = patient.toObject();
    //             const bookingObj = first(value).toObject();

    //             const url = queryString.stringifyUrl({
    //                 url: envConfigDaLieuSurvayObj?.url,
    //                 query: {
    //                     name: `${patientObj.surname} ${patientObj.name}`,
    //                     phone: patientObj.mobile,
    //                     service: bookingObj?.service?.name || ''
    //                 },
    //             });

    //             /* tiến hành bắn event */
    //             await this.createEvent(
    //                 {
    //                     topicId: 'booking.survey.form',
    //                     createTime: moment().toISOString(),
    //                     userId: bookingObj.userId,
    //                     appId: bookingObj.appId,
    //                     title: envConfigDaLieuSurvayObj?.title,
    //                     partnerId: bookingObj.partnerId,
    //                     eventData: {
    //                         ...bookingObj,
    //                         content: envConfigDaLieuSurvayObj?.content,
    //                         type: 100,
    //                         url,
    //                     },
    //                     type: 100,
    //                 },
    //                 true,
    //                 true,
    //                 false,
    //             );

    //             this.eventEmmiter.emit(MESSAGE_EVENT, {
    //                 topic: 'booking.survey.form',
    //                 userId: bookingObj.userId,
    //                 appId: bookingObj.appId,
    //                 title: envConfigDaLieuSurvayObj?.title,
    //                 partnerId: bookingObj.partnerId,
    //                 eventData: {
    //                     ...bookingObj,
    //                     content: envConfigDaLieuSurvayObj?.content,
    //                     type: 100,
    //                     url,
    //                 },
    //                 type: 100,
    //                 isPushNotif: true,
    //                 isSendMail: false,
    //             });
    //         }
    //     }));
    // }

    async handleEvenSurveyFormNoty(idMongoBooking: string): Promise<void> {
        const envConfigDaLieuSurvay = await this.globalSettingService.findByKeyAndRepoName('CONFIG_FORM_SURVEY_NOTY');
        const envConfigDaLieuSurvayObj = JSON.parse(envConfigDaLieuSurvay);
        const booking = await this.bookingModel.findById(
            idMongoBooking,
            {
                service: true, serviceId: true, subject: true, subjectId: true, doctor: true, sectionId: true, section: true,
                room: true, date: true, patient: true, partnerId: true, appId: true, roomId: true, insuranceType: true,
                transactionId: true, insuranceCode: true, sequenceNumber: true, checkInRoom: true, bookingNote: true,
                insuranceChoice: true, patientVersionId: true, patientId: true, status: true, noPayment: true,
                bookingCodeV1: true, syncStatus: true, bookingCode: true, serviceInfo: true, bookingInternalId: true,
                addonServices: true, id: true, treeId: true, implementAgent: true, implementLocation: true, userId: true,
            },
        )
            .populate('service')
            .populate({ path: 'subject', select: 'name' })
            .populate({ path: 'room', select: 'name' })
            .populate({ path: 'doctor', select: 'name' })
            .populate({ path: 'section', select: 'name' })
            .populate({
                path: 'patient', select: {
                    name: true, surname: true, sex: true,
                    birthdate: true, birthyear: true, code: true,
                    mobile: true,
                },
            }).exec();

        if (!booking) {
            return;
        }

        const bookingObj = booking.toObject();
        const url = queryString.stringifyUrl({
            url: envConfigDaLieuSurvayObj?.url,
            query: {
                name: `${bookingObj?.patient?.surname} ${bookingObj?.patient?.name}`,
                phone: bookingObj?.patient?.mobile,
                service: bookingObj?.service?.name || '',
            },
        });

        /* tiến hành bắn event */
        await this.createEvent(
            {
                topicId: 'booking.survey.form',
                createTime: moment().toISOString(),
                userId: bookingObj.userId,
                appId: bookingObj.appId,
                title: envConfigDaLieuSurvayObj?.title,
                partnerId: bookingObj.partnerId,
                eventData: {
                    ...bookingObj,
                    content: envConfigDaLieuSurvayObj?.content,
                    type: 100,
                    url,
                },
                type: 100,
            },
            true,
            false,
            false,
        );

        this.eventEmmiter.emit(MESSAGE_EVENT, {
            topic: 'booking.survey.form',
            userId: bookingObj.userId,
            appId: bookingObj.appId,
            title: envConfigDaLieuSurvayObj?.title,
            partnerId: bookingObj.partnerId,
            eventData: {
                ...bookingObj,
                content: envConfigDaLieuSurvayObj?.content,
                type: 100,
                url,
            },
            type: 100,
            isPushNotif: true,
            isSendMail: false,
        });
    }

    async pushNotifSurveyFormPartner(param: { bookingCode?: string }) {
        const { bookingCode } = param;

        const date = moment().subtract(1, 'day');
        const fromDate = moment(date).startOf('day').utc().subtract(7, 'hours').toDate();
        const toDate = moment(date).endOf('day').utc().subtract(7, 'hours').toDate();

        console.log('fromDate: ', fromDate);
        console.log('toDate: ', fromDate);

        const configString = await this.globalSettingService.findByKeyAndRepoName('CONFIG_FORM_SURVEY_NOTY_PARTNERS')
        const envConfigServey = JSON.parse(configString);

        let query : any = {  };
        if (bookingCode) {
            query = { $or: [{ bookingCode }, { bookingCodeV1: bookingCode }] };
        } else {
            query = { appId: { $in: [...envConfigServey.partners, 'medpro'] }, status: 1, date: { $gte: fromDate, $lte: toDate } };
        }

        const bookings = await this.bookingModel.find(query, {
            service: true, serviceId: true, subject: true, subjectId: true, doctor: true, sectionId: true, section: true,
            room: true, date: true, patient: true, partnerId: true, appId: true, roomId: true, insuranceType: true,
            transactionId: true, insuranceCode: true, sequenceNumber: true, checkInRoom: true, bookingNote: true,
            insuranceChoice: true, patientVersionId: true, patientId: true, status: true, noPayment: true,
            bookingCodeV1: true, syncStatus: true, bookingCode: true, serviceInfo: true, bookingInternalId: true,
            addonServices: true, id: true, treeId: true, implementAgent: true, implementLocation: true, userId: true, bookingId: true,
        })
        .populate({
            path: 'partner',
            select: {
                name: 1,
                partnerId: 1,
            },
        })
        .exec();

        console.log(`push notif survery form for partners: ${bookings.length}`);

        const hospitals: any = {}
        const partnerConfigs: any = {}
        const medproConfig = await this.partnerConfigModel.findOne({ partnerId: 'medpro' }, { configSurveyForm: true }).exec();

        for (let booking of bookings) {
            const bookingObj = booking.toObject();
            const payment = await this.paymentModel.findOne({ transactionId: bookingObj.transactionId }).exec();

            let hospital = hospitals[booking.partnerId];
            let partnerConfig = partnerConfigs[booking.partnerId];
            if (!hospital) {
                [hospital, partnerConfig] = await Promise.all([
                    this.hospitalModel.findOne({ partnerId: booking.partnerId }, { name: true }).exec(),
                    this.partnerConfigModel.findOne({ partnerId: booking.partnerId }, { configSurveyForm: true }).exec(),
                ]);

                hospitals[booking.partnerId] = hospital;
                partnerConfigs[booking.partnerId] = partnerConfig;
            }

            const contentReplace = envConfigServey?.content.replace('{{hospitalName}}', hospital.name);

            const user = await this.userModel.findById(booking.userId, { username: true }).exec()
            
            // let urlSwitchAppMedpro = bookingObj.appId === 'medpro' ? medproConfig.configSurveyForm.url : (partnerConfig.configSurveyForm?.url || envConfigServey?.url)

            let larkFormUrlOverride = '';
            const medproUserURl = "https://pkh-medpro.larksuite.com/share/base/form/shrusZmzxgubbJyhFadBH9mQpTb";
            const umcUrl = "https://pkh-medpro.larksuite.com/share/base/form/shrusCVYbYlaXVqXTHJW8YQCyIe";
            const medproAppUrl = "https://pkh-medpro.larksuite.com/share/base/form/shrus0RsVVecPCk7lFerHGolgiP";

            if(bookingObj.appId === 'medpro'){
                larkFormUrlOverride = medproAppUrl
            } else if ( partnerConfig.configSurveyForm.url ) {
                larkFormUrlOverride = umcUrl
            } else {
                larkFormUrlOverride = medproUserURl
            }

            const overrideUrl = queryString.stringifyUrl({
                url: larkFormUrlOverride,
                query: {
                    prefill_partnerId: bookingObj.partnerId,
                    partnerfullname: hospital.name,
                    prefill_bookingid: bookingObj.bookingCodeV1 || bookingObj.bookingCode,
                    prefill_userid: user.username,
                    prefill_pttt: payment?.paymentMethod || 'NO_CONFIG',
                    prefill_ctt: payment?.gatewayId || 'NO_CONFIG',
                    prefill_appId: bookingObj.appId,
                    prefill_partnerName: bookingObj?.partner?.name || "",
                    hide_partnerName: 1,
                    hide_appId: 1,
                    hide_ctt: 1,
                    hide_bookingid: 1,
                    hide_partnerId: 1,
                    hide_pttt: 1,
                    hide_userid: 1,
                },
            });

            const finalTitle = partnerConfig.configSurveyForm?.title || envConfigServey?.title;

            /* tiến hành bắn event */
            await this.createEvent(
                {
                    topicId: 'booking.survey.form',
                    createTime: moment().toISOString(),
                    userId: bookingObj.userId,
                    appId: bookingObj.appId,
                    title: finalTitle,
                    partnerId: bookingObj.partnerId,
                    eventData: {
                        ...bookingObj,
                        content: contentReplace,
                        type: 100,
                        url: overrideUrl,
                    },
                    type: 100,
                },
                true,
                false,
                false,
            );

            this.eventEmmiter.emit(MESSAGE_EVENT, {
                topic: 'booking.survey.form',
                userId: bookingObj.userId,
                appId: bookingObj.appId,
                title: finalTitle,
                partnerId: bookingObj.partnerId,
                eventData: {
                    ...bookingObj,
                    content: contentReplace,
                    type: 100,
                    url: overrideUrl,
                },
                type: 100,
                isPushNotif: true,
                isSendMail: false,
                repoName: 'SurveyForm',
            });
        }

        const data = {
            isOk: true,
            query: { ...query, date: { $gte: fromDate, $lte: toDate } },
            booking: {
                count: bookings.length,
            }
        };
        console.log('pushNotifSurveyFormNhidong1: ',data)
        return data;
    }

    public async handleSendSurveyFormMedproUser ({
        bookingCode
    }: {
        bookingCode: string
    }) {
        const configString = await this.globalSettingService.findByKeyAndRepoName('CONFIG_FORM_SURVEY_NOTY_PARTNERS')
        let envConfigServey: {
            env: string;
            partners: string[];
            url: string;
            title: string;
            content: string;
        } = JSON.parse(configString);  
        
        let findBookingCondition: unknown
        const date = moment().subtract(1, 'day');
        const fromDate = moment(date).startOf('day').utc().subtract(7, 'hours').toDate();
        const toDate = moment(date).endOf('day').utc().subtract(7, 'hours').toDate();
        
        if(!!bookingCode){
            findBookingCondition = { $or: [{ bookingCode }, { bookingCodeV1: bookingCode }] };
        }else{
            findBookingCondition = { appId: { $in: [...envConfigServey.partners, 'medpro'] }, status: 1, date: { $gte: fromDate, $lte: toDate } }
        }
        
        const foundBooking = await this.bookingModel.findOne(findBookingCondition).exec();
        const bookingObj: IBooking = foundBooking.toObject();
        const partner = bookingObj.partner as unknown as IHospital
        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId: bookingObj.partnerId }, { configSurveyForm: true }).exec()
        const medproConfig = await this.partnerConfigModel.findOne({ partnerId: 'medpro' }, { configSurveyForm: true }).exec();
        const [user, payment] = await Promise.all([
            this.userModel
              .findOne({ _id: bookingObj.userId }, { username: true, fullname: true })
              .exec(),
            this.paymentModel
              .findOne(
                { transactionId: bookingObj.transactionId },
                { paymentMethod: true, gatewayId: true }
              )
              .exec(),
        ]);

        // ? ************ FAKE URL SURVEY FORM ************ // => DEVELOP ONLY
        let overrideUrlBaseOnAppId = '';
        const medproUserURl = "https://pkh-medpro.larksuite.com/share/base/form/shrusZmzxgubbJyhFadBH9mQpTb?hide_bookingid=1&hide_ctt=1&hide_partnerId=1&hide_pttt=1&hide_userid=1&prefill_bookingid=T202412345679&prefill_ctt=VNPAY&prefill_partnerId=umc&prefill_pttt=visa_vnpay&prefill_userid=0912345679"
        const umcUrl = "https://pkh-medpro.larksuite.com/share/base/form/shrusCVYbYlaXVqXTHJW8YQCyIe?hide_bookingid=1&hide_ctt=1&hide_partnerId=1&hide_pttt=1&hide_userid=1&prefill_bookingid=T202412345679&prefill_ctt=VNPAY&prefill_partnerId=umc&prefill_pttt=visa_vnpay&prefill_userid=0912345679"
        const medproAppUrl = "https://pkh-medpro.larksuite.com/share/base/form/shrus0RsVVecPCk7lFerHGolgiP?hide_bookingid=1&hide_ctt=1&hide_partnerId=1&hide_pttt=1&hide_userid=1&prefill_bookingid=T202412345679&prefill_ctt=VNPAY&prefill_partnerId=umc&prefill_pttt=visa_vnpay&prefill_userid=0912345679"

        if(bookingObj.appId === 'medpro'){
            overrideUrlBaseOnAppId = medproAppUrl
        } else if ( partnerConfig.configSurveyForm.url ) {
            overrideUrlBaseOnAppId = umcUrl
        } else {
            overrideUrlBaseOnAppId = medproUserURl
        }

        const finalTitle = "TEST TITLE"
        // ? ************ FAKE URL SURVEY FORM ************ //
        
        const overrideUrl = queryString.stringifyUrl({
            url: overrideUrlBaseOnAppId,
            query: {
                hide_ctt: 1,
                hide_bookingid: 1,
                hide_partnerId: 1,
                hide_pttt: 1,
                hide_userid: 1,
                hide_appId: 1,
                hide_partnerName: 1,
                prefill_partnerName: partner?.name || "",
                prefill_appId: bookingObj.appId,
                prefill_bookingid: bookingObj.bookingCodeV1 || bookingObj.bookingCode,
                prefill_ctt: payment?.gatewayId || 'NO_CONFIG',
                prefill_partnerId: bookingObj.partnerId,
                prefill_pttt: payment?.paymentMethod || 'NO_CONFIG',
                prefill_userid: user?.username || 'NO_CONFIG',
            },
        });    

        // ? ******************************************************************************************************************************
        // ? PUSH NOTIFICATION
         /* tiến hành bắn event */
         await this.createEvent(
            {
                topicId: 'booking.survey.form',
                createTime: moment().toISOString(),
                userId: bookingObj.userId,
                appId: bookingObj.appId,
                title: finalTitle,
                partnerId: bookingObj.partnerId,
                eventData: {
                    ...bookingObj,
                    content: envConfigServey.content,
                    type: 100,
                    url: overrideUrl,
                },
                type: 100,
            },
            true,
            false,
            false,
        );

        this.eventEmmiter.emit(MESSAGE_EVENT, {
            topic: 'booking.survey.form',
            userId: bookingObj.userId,
            appId: bookingObj.appId,
            title: finalTitle,
            partnerId: bookingObj.partnerId,
            eventData: {
                ...bookingObj,
                content: envConfigServey.content,
                type: 100,
                url: overrideUrl,
            },
            type: 100,
            isPushNotif: true,
            isSendMail: false,
            repoName: 'SurveyForm',
        });
        // ? ******************************************************************************************************************************

        return {
            url: overrideUrl,
            query: queryString.parseUrl(overrideUrl).query,
        }
    }

    async pushDynamicRemindExamBooking(bookingObjectId: string): Promise<void> {
        const booking = await this.bookingModel.findById(bookingObjectId);
        if (!booking) {
            return;
        }
        const bookingObj = booking.toObject();
        let [config, content] = await Promise.all(
            ['NOTY_REMIND_EXAM_BOOKING', `NOTY_CONTENT_REMIND_EXAM_BOOKING_${bookingObj.partnerId}`]
                .map(async key => {
                    return this.globalSettingService.findByKeyAndRepoName(key);
                }));
        if (!content) {
            content = await this.globalSettingService.findByKeyAndRepoName('NOTY_CONTENT_REMIND_EXAM_BOOKING');
        }
        let configObjPartner: any
        try {
            const configObj = JSON.parse(config)
            configObjPartner = get(configObj, bookingObj.partnerId);
        } catch (error) {
            return;
        }
        if (false === configObjPartner?.env) {
            return;
        }
        content = content.replace('{{dateBeforeExam}}', configObjPartner?.timeBeforeExam);
        const remindTime = moment(bookingObj?.date).utc()
            .subtract(configObjPartner?.timeBeforeExam, 'minutes')
            .toDate();
        const name = `reminder_booking_exam_${bookingObj.bookingCode}_${remindTime.valueOf()}`;
        const job = new CronJob(
            remindTime,
            async () => {
                await this.handlePushRemindExamBooking(bookingObj, content)
            },
            () => {
                this.logger.log(`Complete CronJob push notify reminder for Booking: ${bookingObj.bookingCode}`);
            },
        );
        try {
            this.scheduler.addCronJob(name, job);
            this.logger.log(`CronJob reminder for Booking: ${bookingObj.bookingCode} is registered`);
            job.start();
        } catch (error) {
            return;
        }
    }

    async pushBackgroundRemindExamBooking(): Promise<void> {
        const [config, defaultContent] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('NOTY_REMIND_EXAM_BOOKING'),
            this.globalSettingService.findByKeyAndRepoName('NOTY_CONTENT_REMIND_EXAM_BOOKING'),
        ]);
        let configObj: any
        try {
            configObj = JSON.parse(config)
        } catch (error) {
            return;
        }
        const currentDate = moment().utc().add(7, 'hours').clone()
        const startDate = moment(currentDate).set({ hours: 0, minutes: 0, seconds: 0 }).subtract(7, 'hours');
        const endDate = moment(currentDate).set({ hours: 23, minutes: 59, seconds: 59 }).subtract(7, 'hours');
        const bookings = await this.bookingModel.find(
            {
                status: 1,
                partnerId: { $in: Object.keys(configObj) },
                date: {
                    $gte: startDate,
                    $lte: endDate
                },
            }
        ).exec();
        if (bookings.length <= 0) {
            return;
        }

        await Promise.all(bookings.map(async booking => {
            const bookingObj = booking.toObject();
            let content = await this.globalSettingService.findByKeyAndRepoName(`NOTY_CONTENT_REMIND_EXAM_BOOKING_${bookingObj.partnerId}`);
            if (!content) {
                content = defaultContent;
            }
            const configObjPartner = get(configObj, bookingObj.partnerId);
            if (false === configObjPartner?.env) {
                return;
            }
            content = content.replace('{{dateBeforeExam}}', configObjPartner?.timeBeforeExam);
            await Promise.all([
                this.handlePushRemindExamBooking(bookingObj, content),
                this.pushDynamicRemindExamBooking(bookingObj._id),
            ])
        }))
    }

    async handlePushRemindExamBooking(bookingObj: any, content: string): Promise<void> {
        if (bookingObj.status !== 1) {
            return;
        }
        const hospital = await this.hospitalModel.findOne({ partnerId: bookingObj?.partnerId }, { name: true });
        let overrideContent = content;
        overrideContent = overrideContent.replace('{{hospital}}', hospital?.name);
        overrideContent = overrideContent.replace('{{date}}', moment(bookingObj?.date).utc().add(7, 'hours').format('HH:mm DD/MM/YYYY'));
        /* tiến hành bắn event */
        await this.createEvent(
            {
                topicId: 'bookings.remind.exam',
                createTime: moment().toISOString(),
                userId: bookingObj.userId,
                appId: bookingObj.appId,
                title: `${overrideContent}`,
                partnerId: bookingObj.partnerId,
                eventData: {
                    ...bookingObj,
                    type: 1,
                },
                type: 1,
            },
            true,
            false,
            false,
        );

        this.eventEmmiter.emit(MESSAGE_EVENT, {
            topic: 'bookings.remind.exam',
            userId: bookingObj.userId,
            appId: bookingObj.appId,
            title: `${overrideContent}`,
            partnerId: bookingObj.partnerId,
            eventData: {
                ...bookingObj,
                type: 1,
            },
            type: 1,
            isPushNotif: true,
            isSendMail: false,
        });
    }

    async handleBookingTelemedNewNotice(bookingObj: any , patientObj: any) {
        const bookingCode = bookingObj.bookingCodeV1 || bookingObj.bookingCode;

        const doctor = bookingObj.doctor;
        const userDoctor = await this.userDoctorModel.findOne({ doctor: doctor._id, partnerId: bookingObj.partnerId }).exec();
        const doctorMedproId = await this.userModel.findOne({ _id: userDoctor.userId }).exec();

        const doctorPhone = doctorMedproId.username || doctor.phone;

        const bookingDate = moment(bookingObj?.date ? bookingObj.date : bookingObj.createdAt).utc().add(7, 'hours');
        const patientName = `${patientObj.surname} ${patientObj.name}`;
        const patientBirthDay = moment(patientObj.birthdate).isValid() ? moment(patientObj.birthdate).format('DD/MM/YYYY') : `01/01/${patientObj.birthyear}`

        let notifConfig;
        if (bookingObj.treeId === 'TELEMEDNOW') {
            notifConfig = await this.globalSettingService.findByKeyAndRepoNameJSON('NOTIF_BOOKING_TELEMED_NEW_NOTICE_CONFIG_TELEMEDNOW');
        } else {
            notifConfig = await this.globalSettingService.findByKeyAndRepoNameJSON('NOTIF_BOOKING_TELEMED_NEW_NOTICE_CONFIG');
        }

        let { content, title } = notifConfig;

        // const prefixTitle = bookingObj.treeId === 'TELEMEDNOW' ? 'Bạn có phiếu đăng ký tư vấn ngay được đặt vào lúc' : 'Bạn có phiếu đăng ký tư vấn khám bệnh từ xa vào lúc'
        // const content = `${prefixTitle} ${bookingDate.format('HH:mm')} ngày ${bookingDate.format('DD/MM/YYYY')}. ` +
        //     `Bệnh nhân ${patientName}, sinh ngày ${patientBirthDay}. Mã phiếu: ${bookingCode}`

        content = content.replace('{time}', bookingDate.format('HH:mm'))
            .replace('{date}', bookingDate.format('DD/MM/YYYY'))
            .replace('{bookingCode}', bookingCode)
            .replace('{birthDay}', patientBirthDay)
            .replace('{patientName}', patientName)

        const topic = 'bookings.new-notice-notif';

        const payload : CreateMessageEventMepdroDoctorDto = {
            topic,
            userId: bookingObj.doctor?._id,
            title,
            content,
            partnerId: bookingObj.partnerId,
            eventData: {
                bookingCode,
                transactionId: bookingObj.transactionId,
                type: 1,
            },
            type: 1,
            isPushNotif: true,
            isNotif: true,
            isSendMail: false,
        };

        this.eventEmmiter.emit(MESSAGE_EVENT_MEDPRO_DOCTOR, payload);

        const envZns = await this.globalSettingService.findByKeyAndRepoName('ENV_SEND_NZS_BOOKING_TELEMED_NEW_NOTICE')

        if (envZns === 'ON') {
            if (doctorPhone && envZns === 'ON') {
                this.eventEmmiter.emit(SEND_ZNS_PORTAL, {
                    zaloTemplateId: '286801',
                    params: {
                        ten_bs: doctor.name,
                        ngay_kham: bookingDate.format('HH:mm DD/MM/YYYY'),
                        ten_bn: patientName,
                        nam_sinh: patientBirthDay,
                        maphieu: bookingCode,
                        ma_giaodich: bookingObj.transactionId,
                    },
                    phone: doctorPhone.replace(/^(\+84|0)/, '84'),
                });
            } else {
                console.log('handleBookingTelemedNewNotice no doctor phone: ', bookingCode);
            }
        }

    }

    async sendMailBookingSuccesssNewNotice(transactionId: string): Promise<void> {

        this.logger.warn(`PARAMS_BOOKING_NEW_NOTICE transactionId: ${transactionId}`);

        const [booking, payment, configSubject, htmlConfig, htmlDoctorConfig, ignoreUsers] = await Promise.all([
            this.bookingModel.findOne({ transactionId }, {
                service: true, serviceId: true, subject: true, subjectId: true, doctor: true, sectionId: true, section: true,
                room: true, date: true, patient: true, partnerId: true, appId: true, roomId: true, insuranceType: true,
                transactionId: true, insuranceCode: true, sequenceNumber: true, checkInRoom: true, bookingNote: true,
                insuranceChoice: true, patientVersionId: true, patientId: true, status: true, noPayment: true,
                bookingCodeV1: true, syncStatus: true, bookingCode: true, serviceInfo: true, bookingInternalId: true, serviceType: true,
                addonServices: true, id: true, treeId: true, implementAgent: true, implementLocation: true, userId: true, createdAt: true,
                care247: true, cskhUserId: true
            })
                .populate('service')
                .populate({ path: 'subject', select: 'name' })
                .populate({ path: 'room', select: 'name' })
                .populate({ path: 'doctor', select: 'name phone' })
                .populate({ path: 'section', select: 'name' })
                .populate({
                    path: 'patient', select: {
                        name: true, surname: true, sex: true,
                        birthdate: true, birthyear: true, code: true,
                    },
                })
                .exec(),
            this.paymentModel.findOne({ transactionId }, { chargeFeeInfo: true }).exec(),
            // this.mailReceiveModel.findOne({ type: 'booking-new-notice' }).exec(),
            this.globalSettingService.findByKeyAndRepoName('CONFIG_SUBJECT_BOOKING_NEW_NOTICE'),
            this.globalSettingService.findByKeyAndRepoName('HTML_TEMPLATE_MAIL_BOOKING_NOTICE'),
            this.globalSettingService.findByKeyAndRepoName('HTML_TEMPLATE_MAIL_BOOKING_NOTICE_DOCTOR'),
            this.globalSettingService.findByKeyAndRepoName('IGNORE_LARK_NOTIF_BOOKING_NEW_NOTICE'),
        ]);
        if (!booking || booking.status !== 1) {
            return;
        }

        let patients = [];
        if (typeof booking.patientVersion !== typeof undefined) {
            patients = await this.patientVersionModel
                .find({ id: booking.patientVersionId })
                // .populate('profession')
                // .populate('country')
                // .populate('nation')
                .populate('city')
                .populate('district')
                .populate('ward')
                .exec();
        } else {
            patients = await this.patientModel
                .find({ id: booking.patientId })
                // .populate('profession')
                // .populate('country')
                // .populate('nation')
                .populate('city')
                .populate('district')
                .populate('ward')
                .exec();
        }

        if (patients.length === 0) {
            return;
        }

        const firstPatient = first(patients);
        const patientFullAddress = this.patientMongoService.getFullAddress(firstPatient);
        const patientObj = firstPatient.toObject();
        const bookingObj = booking.toObject();

        this.logBookingConstraintPatient({
            booking: bookingObj,
            patient: patientObj,
        })

        const waitingTimeSyncBooking = await this.globalSettingService.findByKeyAndRepoName('WAITING_TIME_SYNC_BOOKING') || '300'
        const jobTime = moment().add(+waitingTimeSyncBooking, 'seconds')
        await this.addCronJobSyncBookingFail({
            booking: bookingObj,
            jobTime,
        })

        const user = await this.userModel.findById(bookingObj.userId).exec();

        const mailReceiver = await this.mailReceiveModel.findOne({ type: 'booking-new-notice', partnerId: bookingObj.partnerId }).exec();

        const mails = (mailReceiver?.emails.split(',') || []).map(item => `${item}`.trim()).filter(m => !!m);

        this.logger.warn(`partner: ${bookingObj.partnerId} mails: ${mails.length} ${JSON.stringify(mails)}`);

        if (mails.length === 0) {
            this.logger.warn(`NOT_FOUND_RECEIVER_TO Partner: ${bookingObj.partnerId} chưa có thông tin người nhận. mã phiếu: ${bookingObj.bookingCode}, giao dịch ${transactionId}`);
            return;
        }

        let html = htmlConfig;
        if (bookingObj?.doctor?.name || '') {
            html = htmlDoctorConfig;
        }
        let statusColor: string;
        let status: string;
        switch (bookingObj.status) {
            case -2:
                statusColor = 'gray';
                status = 'Đã hủy';
                break;
            case 0:
                statusColor = 'red';
                status = 'Chưa thanh toán';
                break;
            case 1:
                statusColor = 'green';
                status = 'Đã thanh toán';
                break;
            case 6:
                statusColor = 'green';
                status = 'Thanh toán hộ';
                break;
            case 2:
                statusColor = 'blue';
                status = 'Đã khám';
                break;
        }

        const hospital = await this.hospitalModel.findOne({ partnerId: booking.partnerId }, { name: true, short_name: true, features: true, isContractSigned: true, status: true, partnerId: true }).exec();
        const feature = hospital?.features.find(featureItem => featureItem.type === `booking.${booking.treeId}`.toLocaleLowerCase());

        const bookingDateString = moment(booking?.date ? booking.date : booking.createdAt).utc().add(7, 'hours').format('HH:mm DD-MM-YYYY')
        const bookingCode = booking?.bookingCodeV1 || booking.bookingCode;

        let doctorPhone = '';
        if (bookingObj.serviceType === 'ADVISORY') {
            const userDoctor = await this.userDoctorModel.findOne({doctor: bookingObj?.doctor?._id}).exec();
            if (userDoctor) {
                const user = await this.userModel.findOne({ _id: userDoctor.userId }).exec();
                if (user) {
                    doctorPhone = `${user.username}`;
                }
            }
        }

        html = html.replace('{fullnameMedproId}', `${user?.fullname !== user?.username ? user.fullname : ''}`)
            .replace('{phoneMedpro}', this.patientMongoService.secretMobile(user?.username))
            .replace('{fullname}', `${patientObj.surname} ${patientObj.name}`)
            .replace('{phone}', this.patientMongoService.secretMobile(patientObj?.mobile))
            .replace('{bookingCode}', bookingCode)
            .replace('{date}', bookingDateString)
            .replace('{subject}', bookingObj?.subject?.name || bookingObj?.service.name)
            .replace('{service}', bookingObj?.service.name)
            .replace('{doctor}', `${bookingObj?.doctor?.name} ( ${doctorPhone} )`)
            .replace('{paymentMedthod}', payment?.chargeFeeInfo.name)
            .replace('{status}', this.utilService.getBookingText(bookingObj))
            .replace('{color}', statusColor)
            .replace('{birthdate}', moment(patientObj?.birthdate).isValid() ? moment(patientObj?.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : `${patientObj.birthyear}`);

        const defaultSubject = `${configSubject}`.replace('{featureName}', feature?.name)
            .replace('{hospitalName}', hospital?.short_name || hospital.name)
            .replace('{date}', bookingDateString);

        const topic = 'booking-new-notice';
        const payload = {
            topic,
            topicBooking: topic + '-' + bookingCode,
            type: -1,
            isPushNotif: false,
            isSendMail: true,
            eventData: {
                from: {
                    name: 'Medpro - Giải pháp tiếp cận y tế thông minh',
                    email: '<EMAIL>',
                },
                subject: defaultSubject,
                html,
                to: mails,
                cc: ['<EMAIL>', '<EMAIL>', '<EMAIL>']
            },
        };

        let tagname = defaultSubject;
        if (mails.length > 0) {
            tagname = `${tagname} [MAIL]`;
        }


        const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;

        let memtionId = '';
        let memtionName = '';
        let larkWebHook = 'https://open.larksuite.com/open-apis/bot/v2/hook/7e57fbb4-78d2-4a9d-ad91-216b0a004304'; // group partner thường
        let msgContent: any;
        if (bookingObj.serviceType === 'ADVISORY') {
            memtionId = 'ou_5fe4fb7b52445dab4fe2e7b3b9b3c704';
            memtionName = 'Nguyễn Thị Linh Chi';
            tagname = `${tagname}. \nBác sĩ: ${bookingObj?.doctor?.name || 'Chưa cấu hình'} ( ${doctorPhone} )`;
            larkWebHook = 'https://open.larksuite.com/open-apis/bot/v2/hook/36ce2675-0837-4ab8-9ead-2d144ec114e4'; // group telemed

            msgContent = larkMsgBookingNewNoticeTemplate2(larkEnv, tagname, `${user.username} - ${user?.fullname}`, `${patientObj.surname} ${patientObj.name} (${patientObj?.mobile})`, bookingDateString, hospital, (booking?.bookingCodeV1 || booking.bookingCode), this.utilService.getBookingText(bookingObj), memtionId, memtionName,
                bookingObj?.subject?.name, patientFullAddress);
        } else {
            memtionId = 'ou_25713be9f3f98398b8863313740fc367';
            memtionName = 'Phạm Lê Diệu Hương';
            msgContent = larkMsgBookingNewNoticeTemplate(larkEnv, tagname, `${user.username} - ${user?.fullname}`, `${patientObj.surname} ${patientObj.name} (${patientObj?.mobile})`, bookingDateString, hospital, (booking?.bookingCodeV1 || booking.bookingCode), this.utilService.getBookingText(bookingObj), memtionId, memtionName);
        }

        const ignoreSet = new Set(ignoreUsers.split(','));

        let sentPartnerConfig = false;
        if (this.urlConfigService.getEnvironment() !== 'PRODUCTION' || ignoreSet.has(booking.userId)) {
            this.eventEmmiter.emit(PUSH_NOTIF_BOOKING_NEW_NOTICE_LARK, {
                webHub: 'https://open.larksuite.com/open-apis/bot/v2/hook/96c3ce6b-2f8b-4a34-8c9c-425a7b6a6f13',
                data: {
                    msg_type: 'interactive',
                    card: msgContent,
                },
                transactionId: booking.transactionId,
            });
        } else {
            const listHospitalIgnoreString = await this.globalSettingService.findByKeyAndRepoName("PARTNER_BOOKING_NEW_NO_LARK_GROUP")
            const listHospitalIgnoreSet = new Set(listHospitalIgnoreString.split(','));
            if (hospital.isContractSigned && !listHospitalIgnoreSet.has(hospital.partnerId)) {
 
                const canthoBookingOnSuccessConfig = await this.globalSettingService.findByKeyAndRepoName("CT_BOOKING_PARTNER");
                const { larkWebHook: canthoLarkWebHook , partnerIds } = JSON.parse(canthoBookingOnSuccessConfig);

                this.eventEmmiter.emit(PUSH_NOTIF_BOOKING_NEW_NOTICE_LARK, {
                    webHub: partnerIds.includes(booking.partnerId) ? canthoLarkWebHook : larkWebHook, // 'https://open.larksuite.com/open-apis/bot/v2/hook/7e57fbb4-78d2-4a9d-ad91-216b0a004304',
                    data: {
                        msg_type: 'interactive',
                        card: msgContent,
                    },
                    transactionId: booking.transactionId,
                });
            }

            // Push lark notification external Medpro
            const thisPartnerConfig = await this.partnerConfigModel.findOne({ partnerId: booking.partnerId }).exec();
            const { notifLarkGroup, isNotifLarkGroup } = thisPartnerConfig;
            if (isNotifLarkGroup) {
                sentPartnerConfig = true;
                this.eventEmmiter.emit(PUSH_NOTIF_BOOKING_NEW_NOTICE_LARK, {
                    webHub: notifLarkGroup,
                    data: {
                        msg_type: 'interactive',
                        card: msgContent,
                    },
                    transactionId: booking.transactionId,
                });
            }
        }

        this.eventEmmiter.emit(MESSAGE_EVENT, payload);

        const treeIdsTelemed = await this.globalSettingService.findByKeyAndRepoName('TELEMED_TREE_ID_RELATED');
        if (treeIdsTelemed.includes(bookingObj.treeId) && bookingObj.serviceType === 'ADVISORY') {
            this.handleBookingTelemedNewNotice(bookingObj, patientObj);
        }

        if (!sentPartnerConfig) {
            this.handleBookingPartnerNotYetSignContract(hospital, msgContent);
        }
    }

    async addCronJobCompleteInfo({booking, patient, time, jobTime, type, data} : any) {
        const jobName = `job_complete_info_lark_notif ${jobTime.toISOString()}_${patient.id}`

        const job = new CronJob(jobTime.toDate(), async () => {
            try {
                this.logger.log(`${jobName} started`);

                const baseUrl = this.urlConfigService.getUrlCheckBookingRules();

                const { data } = await this.http.post(`${baseUrl}/booking-rules/patient/detail`,
                    {
                        patientIds: [patient.id],
                    }, {
                        headers: {
                            partnerid: booking.partnerId,
                            locale: booking.locale || '',
                            appid: booking.appId,
                        }
                    }
                ).toPromise();

                const firstPatient = first<any>(data);
                const missingInfo = firstPatient?.constraintInfo?.errors?.map(e => this.utilService.displayError(e.key)).filter(Boolean).join(', ')

                if (get(firstPatient, 'constraintInfo.isValid') === false) {
                    //Push Lark Group Notification
                    const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
                    const user = await this.userModel.findById(booking.userId).exec();
                    const bookingDateString = moment(booking?.date ? booking.date : booking.createdAt).utc().add(7, 'hours').format('HH:mm DD-MM-YYYY')
                    const hospital = await this.hospitalModel.findOne({ partnerId: booking.partnerId }, { name: true, short_name: true, features: true, isContractSigned: true, status: true }).exec();
                    const msgContent = larkBookingNotifCompleteInfo({
                        larkEnv,
                        message: `Hồ sơ bệnh nhân chưa hoàn tất thông tin (${missingInfo}). [sau ${time/60000} phút] [${type}]`,
                        user: `${user.username} - ${user?.fullname}`,
                        patient: `${patient.surname} ${patient.name}`,
                        bookingCode: booking.bookingCode,
                        status: this.utilService.getBookingText(booking),
                        time: bookingDateString,
                        hospital,
                        type
                    });

                    this.eventEmmiter.emit(PUSH_NOTIF_BOOKING_COMPLETE_INFO, {
                        webHub: this.larkConfig.larkBookingCompleteInfo,
                        data: {
                            msg_type: 'interactive',
                            card: msgContent,
                        },
                    });
                } else {
                    await this.bookingPatientConstraintModel.updateOne({ _id: data._id }, { deleted: true }).exec();
                }
            } catch (err) {
                console.error(`${jobName} err`, err);
            } finally {
                this.scheduler.deleteCronJob(jobName);
            }
        })

        try {
            this.scheduler.addCronJob(jobName, job);
            this.logger.log(`${jobName} registered`);
            job.start();
        } catch (error) {
            return;
        }
    }

    async addCronJobSyncBookingFail({ booking, jobTime } : any) {
        const jobName = `job_sync_booking_fail_lark_notif ${jobTime.toISOString()}_${booking._id}`
        console.log('cronjob sync booking fail start.');
        
        const job = new CronJob(jobTime.toDate(), async () => {
            try {
                this.logger.log(`${jobName} started`);
                const waitingPartner = await this.globalSettingService.findByKeyAndRepoName('WAITING_BOOKING_PARTNER')
                const waitingPartnerListArr = new Set(waitingPartner.split(','));
                if (waitingPartnerListArr.has(booking.partnerId)) {
                    if (booking?.syncStatus === 'success' && !! booking?.bookingInternalId) {
                        return { isOk: true }
                    } else {
                        await this.larkNotifSyncBookingFail({ bookingId: booking._id })
                    }
                }
            } catch (err) {
                console.error(`${jobName} err`, err);
            } finally {
                this.scheduler.deleteCronJob(jobName);
            }
        })

        try {
            this.scheduler.addCronJob(jobName, job);
            this.logger.log(`${jobName} registered`);
            job.start();
        } catch (error) {
            console.log('error', error);
            
            return;
        }
    }

    async registerExistCompleInfo(id?: string) {
        try {
            const currentDate = new Date();
            const tomorrow = currentDate.setDate(currentDate.getDate() + 1);
            const condition = id ? { _id: id } : { deleted: { $ne: true }, bookingDate: { $gte: new Date(tomorrow).toISOString().split('T')[0] + 'T00:00:00Z' } }
            const data = await this.bookingPatientConstraintModel.find(condition).exec();

            for (const item of data) {
                const { bookingId, patient, createdAt } = item
                const booking = await this.bookingModel.findById(bookingId, {
                    service: true, serviceId: true, subject: true, subjectId: true, doctor: true, sectionId: true, section: true,
                    room: true, date: true, patient: true, partnerId: true, appId: true, roomId: true, insuranceType: true,
                    transactionId: true, insuranceCode: true, sequenceNumber: true, checkInRoom: true, bookingNote: true,
                    insuranceChoice: true, patientVersionId: true, patientId: true, status: true, noPayment: true,
                    bookingCodeV1: true, syncStatus: true, bookingCode: true, serviceInfo: true, bookingInternalId: true,
                    addonServices: true, id: true, treeId: true, implementAgent: true, implementLocation: true, userId: true
                }).exec()
                //addCronJob
                let jobTime : any
                //Medium
                // const { millisecondsMedium = 3000 } = await this.globalSettingService.findByKeyAndRepoNameJSON('COMPLETE_INFO_LARK_NOTIF_CONFIG_MEDIUM')
                // jobTime = moment(createdAt).add(millisecondsMedium, 'milliseconds')
                // await this.addCronJobCompleteInfo({
                //     booking: booking.toObject(),
                //     patient,
                //     time: millisecondsMedium,
                //     jobTime,
                //     type: 'MEDIUM',
                //     data: item.toObject()
                // })
                //Highest
                const { millisecondsHighest = 6000 } = await this.globalSettingService.findByKeyAndRepoNameJSON('COMPLETE_INFO_LARK_NOTIF_CONFIG_HIGHEST')
                jobTime = moment(createdAt).add(millisecondsHighest, 'milliseconds')
                await this.addCronJobCompleteInfo({
                    booking: booking.toObject(),
                    patient,
                    time: millisecondsHighest,
                    jobTime,
                    type: 'HIGHEST',
                    data: item.toObject()
                })
            }
        } catch (err) {
            console.error('err registerExistCompleInfo', err);
        }
    }

    async sendLarkNotifMultipleBookings(data: [{ _id: string }]): Promise<void> {

        for await (const item of data) {
            const booking = await this.bookingModel.findById({ _id: item._id }, {
                service: true, serviceId: true, subject: true, subjectId: true, doctor: true, sectionId: true, section: true,
                room: true, date: true, patient: true, partnerId: true, appId: true, roomId: true, insuranceType: true,
                transactionId: true, insuranceCode: true, sequenceNumber: true, checkInRoom: true, bookingNote: true,
                insuranceChoice: true, patientVersionId: true, patientId: true, status: true, noPayment: true,
                bookingCodeV1: true, syncStatus: true, bookingCode: true, serviceInfo: true, bookingInternalId: true,
                addonServices: true, id: true, treeId: true, implementAgent: true, implementLocation: true, userId: true,
                medproCare: true, paymentStatus: true, platform: true
            })
                .populate({ path: 'partner', select: 'name' })
                .populate('service')
                .populate({ path: 'subject', select: 'name' })
                .populate({ path: 'room', select: 'name' })
                .populate({ path: 'doctor', select: 'name' })
                .populate({ path: 'section', select: 'name' })
                .populate({
                    path: 'patient', select: {
                        name: true, surname: true, sex: true,
                        birthdate: true, birthyear: true, code: true,
                    },
                })
                .read('primary')
                .exec();

            if (!booking) {
                return;
            }

            let patients = [];
            if (typeof booking.patientVersion !== typeof undefined) {
                patients = await this.patientVersionModel
                    .find({ id: booking.patientVersionId })
                    .populate('profession')
                    .populate('country')
                    .populate('nation')
                    .populate('city')
                    .populate('district')
                    .populate('ward')
                    .exec();
            } else {
                patients = await this.patientModel
                    .find({ id: booking.patientId })
                    .populate('profession')
                    .populate('country')
                    .populate('nation')
                    .populate('city')
                    .populate('district')
                    .populate('ward')
                    .exec();
            }

            console.log('patients.length: ', patients.length);
            if (patients.length === 0) {
                return;
            }

            const firstPatient = first(patients);
            const patientObj = firstPatient.toObject();
            const bookingObj = booking.toObject();
            const user = await this.userModel.findById(bookingObj.userId).exec();
            const hospital = await this.hospitalModel.findOne({ partnerId: booking.partnerId }, { name: true, short_name: true, features: true }).exec();
            const bookingCode = booking?.bookingCodeV1 || booking.bookingCode;

            try {
                if (bookingObj.appId === 'bvthongnhat' && bookingObj.platform === 'kiosk' && bookingObj.status === 1 && bookingObj.paymentStatus === 2) {
                    const objKioskPaymentLarkGroup = {
                        medproId: `${user.username} - ${user.fullname}`,
                        hoso: `${patientObj.surname} ${patientObj.name} (${patientObj.mobile})`,
                        maphieu: bookingCode,
                        thoigian: moment(bookingObj?.date).utc().add(7, 'hours').format('HH:mm DD-MM-YYYY'),
                        benhvien: hospital.name,
                        trangthai: 'Đã thanh toán',
                        larkTitle: `[Mã Giao dịch nhiều phiếu ${bookingObj.transactionId}] Hệ thống ghi nhận người dùng thanh toán qua kiosk tại ${hospital.name}`
                    };
                    console.log(JSON.stringify(objKioskPaymentLarkGroup, null, 2))
                    await this.sendNotiOnKioskPayment(objKioskPaymentLarkGroup)
                }
            } catch (error) {
    
            }
        }
    }

    async sendMailBookingSuccesssNewNoticeCAM(transactionId: string): Promise<void> {

        this.logger.warn(`PARAMS_BOOKING_NEW_NOTICE_CAMBODIA transactionId: ${transactionId}`);

        const [booking, payment, configSubject, htmlConfig, configBookingCamNotice] = await Promise.all([
            this.bookingModel.findOne({ transactionId }, {
                service: true, serviceId: true, subject: true, subjectId: true, doctor: true, sectionId: true, section: true,
                room: true, date: true, patient: true, partnerId: true, appId: true, roomId: true, insuranceType: true,
                transactionId: true, insuranceCode: true, sequenceNumber: true, checkInRoom: true, bookingNote: true,
                insuranceChoice: true, patientVersionId: true, patientId: true, status: true, noPayment: true,
                bookingCodeV1: true, syncStatus: true, bookingCode: true, serviceInfo: true, bookingInternalId: true,
                addonServices: true, id: true, treeId: true, implementAgent: true, implementLocation: true, userId: true,
                medproCare: true, paymentStatus: true, platform: true, cskhUserId: true, care247: true,
            })
                .populate({ path: 'partner', select: 'name' })
                .populate('service')
                .populate({ path: 'subject', select: 'name' })
                .populate({ path: 'room', select: 'name' })
                .populate({ path: 'doctor', select: 'name' })
                .populate({ path: 'section', select: 'name' })
                .populate({
                    path: 'patient', select: {
                        name: true, surname: true, sex: true,
                        birthdate: true, birthyear: true, code: true,
                    },
                })
                .read('primary')
                .exec(),
            this.paymentModel.findOne({ transactionId }, { chargeFeeInfo: true, type: true, medproCareFee: true }).exec(),
            this.globalSettingService.findByKeyAndRepoName('CONFIG_SUBJECT_BOOKING_NEW_NOTICE_CAM'),
            this.globalSettingService.findByKeyAndRepoName('HTML_TEMPLATE_MAIL_BOOKING_NOTICE_CAM'),
            this.globalSettingService.findByKeyAndRepoName('ENV_MAIL_BOOKING_NOTICE_CAM'),
        ]);
        if (!booking) {
            return;
        }

        let patients = [];
        if (typeof booking.patientVersion !== typeof undefined) {
            patients = await this.patientVersionModel
                .find({ id: booking.patientVersionId })
                // .populate('profession')
                // .populate('country')
                // .populate('nation')
                .populate('city')
                .populate('district')
                .populate('ward')
                .exec();
        } else {
            patients = await this.patientModel
                .find({ id: booking.patientId })
                // .populate('profession')
                // .populate('country')
                // .populate('nation')
                .populate('city')
                .populate('district')
                .populate('ward')
                .exec();
        }

        console.log('patients.length: ', patients.length);
        if (patients.length === 0) {
            return;
        }

        const firstPatient = first(patients);
        const patientFullAddress = this.patientMongoService.getTinhThanh(firstPatient);
        const patientObj = firstPatient.toObject();
        const bookingObj = booking.toObject();
        const user = await this.userModel.findById(bookingObj.userId).exec();
        const hospital = await this.hospitalModel.findOne({ partnerId: booking.partnerId }, { name: true, short_name: true, features: true }).exec();
        const bookingCode = booking?.bookingCodeV1 || booking.bookingCode;

        let statusColor: string;
        let status: string;
        switch (bookingObj.status) {
            case -2:
                statusColor = 'gray';
                status = 'Đã hủy';
                break;
            case 0:
                statusColor = 'red';
                status = 'Chưa thanh toán';
                break;
            case 1:
                statusColor = 'green';
                status = 'Đã thanh toán';
                break;
            case 6:
                statusColor = 'green';
                status = 'Thanh toán hộ';
                break;
            case 2:
                statusColor = 'blue';
                status = 'Đã khám';
                break;
        }

        this.logBookingConstraintPatient({
            booking: bookingObj,
            patient: patientObj,
        })

        const waitingTimeSyncBooking = await this.globalSettingService.findByKeyAndRepoName('WAITING_TIME_SYNC_BOOKING') || '300'
        const jobTime = moment().add(+waitingTimeSyncBooking, 'seconds')
        await this.addCronJobSyncBookingFail({
            booking: bookingObj,
            jobTime,
        })

        console.log('appId:', bookingObj.appId)
        console.log('this.newSetZNSPartnersV2:', this.newSetZNSPartnersV2.size)
        console.log('`${booking.userId}`', `${booking.userId}`)
        if (this.newSetZNSPartnersV2.has(bookingObj.appId)) {
            const objBody = { id: bookingObj._id }
            console.log(`Object Params ZNS ${bookingObj.appId}`, objBody)
            const envZnsV2 = await this.globalSettingService.findByKeyAndRepoName('ENV_SEND_NZS_BOOKING_V2_NEW_NOTICE')
            if (`${booking.userId}` === '6281c28cf054720019f0e9c1' || envZnsV2 === 'ON') {
                await this.pushZnsBookingSuccess({ id: bookingObj._id });
            }
        }

        //Update data kpi cskh
        try {
            await this.kpiCskhModel.findOneAndUpdate({ bookingId: bookingObj._id }, { status: 1 }).exec();
        } catch (error) {
            console.log('error', error);
        }

        const medproCarePartnerList = await this.globalSettingService.findByKeyAndRepoName('MEDPRO_CARE_PARTNER_LIST')
        let medproCarePartnerListArr = new Set(medproCarePartnerList.split(',')).add('umc').add('dalieuhcm')
        try {
            const onOffChoRay = await this.globalSettingService.findByKeyAndRepoName('ON_OFF_CHORAY_CARE247')
            if (onOffChoRay === 'ON') {
                medproCarePartnerListArr = medproCarePartnerListArr.add('choray');
            }
        } catch (error) {
        }
        const kenhPhanPhoi = new Set().add('medpro').add('viettelpay');
        if (bookingObj?.care247?.type === 'independent') {
        } else {
            if (kenhPhanPhoi.has(bookingObj.appId) && medproCarePartnerListArr.has(bookingObj.partnerId) && bookingObj?.medproCare && Object.keys(bookingObj.medproCare).length > 0) {
                
                let getCSKHUser = null;
                if(bookingObj?.cskhUserId || ''){
                    getCSKHUser = await this.userModel.findById({_id: bookingObj.cskhUserId}).exec();
                }
    
                let partnerName = ''
                switch (bookingObj.partnerId) {
                    case 'umc':
                        partnerName = `${hospital.name} - Cơ sở chính`
                        break;
                    case 'umc2':
                        partnerName = `${hospital.name} - Cơ sở 2`
                        break;
                    default:
                        partnerName = hospital.name
                        break;
                }
    
                const objMedproCareLarkGroup = {
                    medproId: `${user.username} - ${user.fullname}`,
                    hoso: `${patientObj.surname} ${patientObj.name} (${patientObj.mobile})`,
                    thoigian: moment(bookingObj?.date).utc().add(7, 'hours').format('HH:mm DD-MM-YYYY'),
                    benhvien: partnerName,
                    maphieu: bookingCode,
                    medproCare: bookingObj.medproCare,
                    trangthai: status,
                    address: patientFullAddress,
                    ...(getCSKHUser && { cskhUser: `${getCSKHUser.username} - ${getCSKHUser.fullname}`})
                };
                console.log(JSON.stringify(objMedproCareLarkGroup, null, 2))
                await this.sendLarkNotiOnUserBookingMedproCare(objMedproCareLarkGroup)
                // create care247 data
                try {
                    
                    const bookingCare247Constraint = new this.bookingCare247ConstraintModel({
                        transactionId,
                    });
                    await bookingCare247Constraint.save();
    
                    const { medproCare } = bookingObj
                    const bookingCode = bookingObj?.bookingCodeV1 || bookingObj?.bookingCode;
                    const newBookingCare247 = new this.bookingCare247Model({
                        bookingId: bookingObj._id,
                        booking: bookingObj._id,
                        bookingCode: bookingCode,
                        bookingStatus: bookingObj.status,
                        patient: bookingObj.patient,
                        patientId: bookingObj.patientId,
                        patientVersion: bookingObj.patientVersion,
                        patientVersionId: bookingObj.patientVersionId,
                        userId: bookingObj.userId,
                        user: bookingObj.userId,
                        appId: bookingObj.appId,
                        partnerId: bookingObj.partnerId,
                        partner: bookingObj.partner,
                        subject: bookingObj.subject,
                        service: bookingObj.service,
                        doctor: bookingObj.doctor,
                        room: bookingObj.room,
                        section: bookingObj.section,
                        date: bookingObj.date,
                        cskhUserId: bookingObj.cskhUserId,
                        cskh: bookingObj.cskhUserId,
                        platform: bookingObj.platform,
                        transactionId: bookingObj.transactionId,
                        name: medproCare.name,
                        id: uuid.v4().replace(/-/g, ''),
                        addonServices: medproCare.addonServices,
                        note: medproCare.note,
                        medproCareNote: medproCare.medproCareNote,
                        status: 1,
                        type: 'original',
                        provider: medproCare.provider,
                    });
                    await newBookingCare247.save();
                } catch (error) {
                    console.log('error', error);
                }
            } else {
                try {
                    const bookingCare247Constraint = new this.bookingCare247ConstraintModel({
                        transactionId,
                    });
                    await bookingCare247Constraint.save();
                    if (bookingObj.partnerId !== 'umc' && kenhPhanPhoi.has(bookingObj.appId) && medproCarePartnerListArr.has(bookingObj.partnerId)) {
                        //Tạo giao dịch care247 cho phiếu khám có cấu hình partner care247
                        try {
                            let medproCareTransaction: any
                            //Tạo data care247 khi booking thành công phiếu khám có đi kèm care247 cùng transactionId
                            if (bookingObj?.care247) {
                                medproCareTransaction = bookingObj?.care247
                                const bookingCode = bookingObj?.bookingCodeV1 || bookingObj?.bookingCode;
                                const care247Status = bookingObj.partnerId !== 'dalieuhcm' && payment.medproCareFee > 0 ? 1 : 0;
                                const newBookingCare247 = new this.bookingCare247Model({
                                    bookingId: bookingObj._id,
                                    booking: bookingObj._id,
                                    bookingCode: bookingCode,
                                    bookingStatus: bookingObj.status,
                                    patient: bookingObj.patient,
                                    patientId: bookingObj.patientId,
                                    patientVersion: bookingObj.patientVersion,
                                    patientVersionId: bookingObj.patientVersionId,
                                    userId: bookingObj.userId,
                                    user: bookingObj.userId,
                                    appId: bookingObj.appId,
                                    partnerId: bookingObj.partnerId,
                                    partner: bookingObj.partner,
                                    subject: bookingObj.subject,
                                    service: bookingObj.service,
                                    doctor: bookingObj.doctor,
                                    room: bookingObj.room,
                                    section: bookingObj.section,
                                    date: bookingObj.date,
                                    cskhUserId: bookingObj.cskhUserId,
                                    cskh: bookingObj.cskhUserId,
                                    platform: bookingObj.platform,
                                    transactionId: bookingObj.transactionId,
                                    name: medproCareTransaction.name,
                                    id: uuid.v4().replace(/-/g, ''),
                                    addonServices: medproCareTransaction.addonServices,
                                    note: medproCareTransaction.note,
                                    medproCareNote: medproCareTransaction.medproCareNote,
                                    status: care247Status,
                                    type: bookingObj.partnerId === 'dalieuhcm' ? 'primary' : 'original',
                                    provider: medproCareTransaction.provider,
                                });
                                await newBookingCare247.save();
                                if (bookingObj.partnerId !== 'dalieuhcm' && payment.medproCareFee > 0) {
                                    await this.manualFindMedproCareBookingDataByBookingIdAndSendNotiToLark({ bookingId: bookingObj.id, transactionId: bookingObj.transactionId })
                                }
                            }
                            
                            //Nếu thanh toán phiếu khám thành công chưa có care247 thì tạo giao dịch care247 chưa thanh toán
                            if (medproCarePartnerListArr.has(bookingObj.partnerId) && !bookingObj?.care247) {
                                const { medproCare } = await this.getMedproCareTransaction(bookingObj.partnerId)
                                medproCareTransaction = medproCare
                                
                                await this.bookingModel.findByIdAndUpdate({ _id: bookingObj._id }, 
                                    { care247: medproCareTransaction })
                                    .exec();
    
                                const bookingCode = bookingObj?.bookingCodeV1 || bookingObj?.bookingCode;
                                const newBookingCare247 = new this.bookingCare247Model({
                                    bookingId: bookingObj._id,
                                    booking: bookingObj._id,
                                    bookingCode: bookingCode,
                                    bookingStatus: bookingObj.status,
                                    patient: bookingObj.patient,
                                    patientId: bookingObj.patientId,
                                    patientVerSion: bookingObj.patientVerSion,
                                    patientVersionId: bookingObj.patientVersionId,
                                    userId: bookingObj.userId,
                                    user: bookingObj.userId,
                                    appId: bookingObj.appId,
                                    partnerId: bookingObj.partnerId,
                                    partner: bookingObj.partner,
                                    subject: bookingObj.subject,
                                    service: bookingObj.service,
                                    doctor: bookingObj.doctor,
                                    room: bookingObj.room,
                                    section: bookingObj.section,
                                    date: bookingObj.date,
                                    cskhUserId: bookingObj.cskhUserId,
                                    cskh: bookingObj.cskhUserId,
                                    platform: bookingObj.platform,
                                    name: medproCareTransaction.name,
                                    id: uuid.v4().replace(/-/g, ''),
                                    addonServices: medproCareTransaction.addonServices,
                                    note: medproCareTransaction.note,
                                    medproCareNote: medproCareTransaction.medproCareNote,
                                    status: medproCareTransaction.status,
                                    provider: medproCareTransaction.provider
                                });
                                await newBookingCare247.save();
                            }
                        } catch (error) {
                            console.log('error', error);
                        }
                    } else { // case booking partner umc
                        try {
                            let medproCareTransaction: any
                            const { medproCare } = await this.getMedproCareTransaction(bookingObj.partnerId)
                            if (bookingObj?.care247) {
                                medproCareTransaction = bookingObj?.care247
                            } else {
                                medproCareTransaction = medproCare
                            }
                            if (bookingObj.partnerId === 'umc' && payment.type === 1  && kenhPhanPhoi.has(bookingObj.appId) && medproCarePartnerListArr.has(bookingObj.partnerId)) { // booking 1 phiếu
                                await this.bookingModel.findByIdAndUpdate({ _id: bookingObj._id }, 
                                    { care247: medproCareTransaction })
                                    .exec();
                                const bookingCode = bookingObj?.bookingCodeV1 || bookingObj?.bookingCode;
                                const newBookingCare247 = new this.bookingCare247Model({
                                    bookingId: bookingObj._id,
                                    booking: bookingObj._id,
                                    bookingCode: bookingCode,
                                    bookingStatus: 1,
                                    patient: bookingObj.patient,
                                    patientId: bookingObj.patientId,
                                    patientVerSion: bookingObj.patientVerSion,
                                    patientVersionId: bookingObj.patientVersionId,
                                    userId: bookingObj.userId,
                                    user: bookingObj.userId,
                                    appId: bookingObj.appId,
                                    partnerId: bookingObj.partnerId,
                                    partner: bookingObj.partner,
                                    subject: bookingObj.subject,
                                    service: bookingObj.service,
                                    doctor: bookingObj.doctor,
                                    room: bookingObj.room,
                                    section: bookingObj.section,
                                    date: bookingObj.date,
                                    cskhUserId: bookingObj.cskhUserId,
                                    cskh: bookingObj.cskhUserId,
                                    platform: bookingObj.platform,
                                    name: medproCareTransaction.name,
                                    id: uuid.v4().replace(/-/g, ''),
                                    addonServices: medproCareTransaction.addonServices,
                                    note: medproCareTransaction.note,
                                    medproCareNote: medproCareTransaction.medproCareNote,
                                    status: 0, // tạo sẵn 1 cái booking care247 chưa thanh toán.
                                    provider: medproCareTransaction.provider
                                });
                                await newBookingCare247.save();
                            } else if (bookingObj.partnerId === 'umc' && payment.type === 5  && kenhPhanPhoi.has(bookingObj.appId) && medproCarePartnerListArr.has(bookingObj.partnerId)) { // booking nhiều phiếu
                                const bookings = await this.bookingModel.find({ transactionId }).exec()
                                let firstBooking = first(bookings).toObject();
                                try {
                                    const earliestDateBooking = bookings.reduce((earliest, current) => {
                                        return moment(current.date).isBefore(moment(earliest.date)) ? current : earliest;
                                    });
                                    firstBooking = earliestDateBooking.toObject()
                                } catch (error) {
                                    console.log('error umc care247', error);
                                }
    
                                if (firstBooking?.care247) {
                                    medproCareTransaction = firstBooking?.care247
                                }
    
                                await this.bookingModel.findByIdAndUpdate({ _id: firstBooking._id }, 
                                    { care247: medproCareTransaction })
                                    .exec();
                                const bookingCode = firstBooking?.bookingCodeV1 || firstBooking?.bookingCode;
                                const bookingsRelation = bookings.filter((b) => b.bookingCode !== firstBooking.bookingCode).map((item) => {
                                    return { _id: item._id, bookingCode: item.bookingCode, bookingCodeV1: item.bookingCodeV1}
                                })
                                const newBookingCare247 = new this.bookingCare247Model({
                                    bookingId: firstBooking._id,    
                                    booking: firstBooking._id,
                                    bookingCode: bookingCode,
                                    bookingStatus: 1,
                                    patient: firstBooking.patient,
                                    patientId: firstBooking.patientId,
                                    patientVerSion: firstBooking.patientVerSion,
                                    patientVersionId: firstBooking.patientVersionId,
                                    userId: firstBooking.userId,
                                    user: firstBooking.userId,
                                    appId: firstBooking.appId,
                                    partnerId: firstBooking.partnerId,
                                    partner: firstBooking.partner,
                                    subject: firstBooking.subject,
                                    service: firstBooking.service,
                                    doctor: firstBooking.doctor,
                                    room: firstBooking.room,
                                    section: firstBooking.section,
                                    date: firstBooking.date,
                                    cskhUserId: firstBooking.cskhUserId,
                                    cskh: bookingObj.cskhUserId,
                                    platform: firstBooking.platform,
                                    name: medproCareTransaction.name,
                                    id: uuid.v4().replace(/-/g, ''),
                                    addonServices: medproCareTransaction.addonServices,
                                    note: medproCareTransaction.note,
                                    medproCareNote: medproCareTransaction.medproCareNote,
                                    status: 0, // tạo sẵn 1 cái booking care247 chưa thanh toán.
                                    bookingsRelation: bookingsRelation,
                                    provider: medproCareTransaction.provider
                                });
                                await newBookingCare247.save();
                            }
                        } catch (error) {
                            console.log('error', error);
                        }
                    }
                } catch (error) {
                    console.log('error', error);
                }
            }
        }

        

        // ? Gửi thông báo lark form khi người dùng tại kiosk thanh toán tại BV Thống Nhất
        try {
            if (bookingObj.appId === 'bvthongnhat' && bookingObj.platform === 'kiosk' && bookingObj.status === 1 && bookingObj.paymentStatus === 2) {
                const objKioskPaymentLarkGroup = {
                    medproId: `${user.username} - ${user.fullname}`,
                    hoso: `${patientObj.surname} ${patientObj.name} (${patientObj.mobile})`,
                    maphieu: bookingCode,
                    thoigian: moment(bookingObj?.date).utc().add(7, 'hours').format('HH:mm DD-MM-YYYY'),
                    benhvien: hospital.name,
                    trangthai: 'Đã thanh toán',
                    larkTitle: `[Mã Giao dịch 1 phiếu ${bookingObj.transactionId}] Hệ thống ghi nhận người dùng thanh toán qua kiosk tại ${hospital.name}`
                };
                console.log(JSON.stringify(objKioskPaymentLarkGroup, null, 2))
                await this.sendNotiOnKioskPayment(objKioskPaymentLarkGroup)
            }
        } catch (error) {

        }
            

        let mailReceiver;
        try {
            const cfObject = JSON.parse(configBookingCamNotice);
            mailReceiver = get(cfObject, 'mails')
            const treeId = get(cfObject, 'treeIds', [])
            const setTreeId = new Set(treeId)
            if (!setTreeId.has(booking.treeId)) {
                return;
            }
        } catch (err) {
            console.error('cound not parse email config ENV_MAIL_BOOKING_NOTICE_CAM', err);
            return;
        }

        



        let html = htmlConfig
        
        console.log('mailReceiver: ', mailReceiver);
        html = html.replace('{fullnameMedproId}', `${user?.fullname !== user?.username ? user.fullname : ''}`)
            .replace('{phoneMedpro}', this.patientMongoService.secretMobile(user?.username))
            .replace('{fullname}', `${patientObj.surname} ${patientObj.name}`)
            .replace('{phone}', this.patientMongoService.secretMobile(patientObj?.mobile))
            .replace('{bookingCode}', bookingCode)
            .replace('{date}', moment(booking?.date).utc().add(7, 'hours').format('HH:mm DD-MM-YYYY'))
            .replace('{subject}', bookingObj?.subject?.name || bookingObj?.service.name)
            .replace('{service}', bookingObj?.service.name)
            .replace('{paymentMedthod}', payment?.chargeFeeInfo.name)
            .replace('{status}', this.utilService.getBookingText(bookingObj))
            .replace('{color}', statusColor)
            .replace('{birthdate}', moment(patientObj?.birthdate).isValid() ? moment(patientObj?.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : `${patientObj.birthyear}`);

        const defaultSubject = `${configSubject}`.replace('{hospitalName}', hospital.name)
            .replace('{bookingCode}', get(bookingObj, 'bookingCode', ''))
            .replace('{subjectName}', get(bookingObj, 'subject.name', ''))
            .replace('{date}', moment(booking?.date).utc().add(7, 'hours').format('HH:mm - DD-MM-YYYY'));

        const topic = 'booking-new-notice';
        const payload = {
            topic,
            topicBooking: topic + '-cam-' + bookingCode,
            type: -1,
            isPushNotif: false,
            isSendMail: true,
            eventData: {
                from: {
                    name: 'Medpro - Giải pháp tiếp cận y tế thông minh',
                    email: '<EMAIL>',
                },
                subject: defaultSubject,
                html,
                to: mailReceiver,
            },
        };
        this.eventEmmiter.emit(MESSAGE_EVENT, payload);

        this.logBookingConstraintPatient({
            booking: bookingObj,
            patient: patientObj,
        })
        
    }

    async userHaveBookingBVMat(body: { userId: string, partnerId: string, maxPatient: number }) {
        try {
            const { userId, partnerId, maxPatient } = body;
            const countBookingSucces = await this.bookingModel.find({ userId }, { partnerId: true, status: true }).exec();
            const checkSomes = countBookingSucces.some(item => item.partnerId === 'bvmathcm' && [1, 2].includes(item.status));
            if (checkSomes) {
                return this.checkMaxPatients({ userId, partnerId })
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    async isValidCreatePatient(
        body: {
            userId: string,
            partnerId: string,
            patientFormData: {
                name: string,
                mobile: string
            }
            city_id: string,
            sex: number, // ? 1: Nam, 0: Nữ,
            birthdate: string,
        }
    ) {

        try {
            const { userId, partnerId, patientFormData } = body;

            try {
                const checkCSUser = await this.userModel.findById({ _id: userId }, { isCS: true }).exec();
                if (checkCSUser && checkCSUser?.isCS === true) {
                    return {
                        isValid: false
                    };
                }
            } catch (error) {

            }
            

            const maxPatient = partnerId === 'bvmathcm' ? 3 : 10;
            const checkMaxPatients = await this.checkMaxPatients({ userId, partnerId });
            const userHaveBookingBVMat = await this.userHaveBookingBVMat({ userId, partnerId, maxPatient })
            if (checkMaxPatients || userHaveBookingBVMat) {

                const [
                    userById,
                    hospitalByPartnerId,
                    cityById
                ] = await Promise.all([
                    this.userModel.findById({ _id: userId }).exec(),
                    this.hospitalModel.findOne({ partnerId: partnerId }, { name: true }).exec(),
                    this.cityModel.findOne({ id: body.city_id }).exec()
                ]);

                // ? Gửi thông báo lark form
                await this.sendNotiWarningUserBehavior({
                    medproId: `${userById.username} - ${userById.fullname}`,
                    hoso: `${patientFormData.name} (${patientFormData.mobile})`,
                    thoigian: moment().utc().add(7, 'hours').format('HH:mm DD-MM-YYYY'), // ? 07:40 20-09-2021
                    benhvien: hospitalByPartnerId.name,
                    sex: body.sex === 1 ? 'Nam' : 'Nữ',
                    birthdate: moment(body.birthdate).utc().add(7, 'hours').format('DD-MM-YYYY'), // ? 20-09-2021
                    cityName: cityById.name
                })

                return {
                    isValid: true,
                    maxPatient
                };
            }

            return {
                isValid: false,
            };

        } catch (error) {
            return {
                isValid: false,
                message: error?.message || 'isValidCreatePatient'
            };
        }
    }

    async checkMaxPatients(body: { userId: string, partnerId: string }) {
        try {
            const { userId, partnerId } = body;
            const maxPatient = partnerId === 'bvmathcm' ? 3 : 10;
            const userWithPatients = await this.userModel.findById({ _id: userId }, { patients: true }).exec();

            if (userWithPatients.patients.length >= maxPatient) {
                return true;
            }
            return false;

        } catch (error) {
            return false;
        }
    }

    async pushZnsBookingSuccess(body: any) {
        const now = moment().add(7, 'hours');

        if (now.hour() >= 22 || now.hour() < 6) {
            return this.pushZnsBookingModel.create({
                bookingId: body.id,
            })
        }

        const booking = await this.bookingModel
            .findOne({ _id: body.id })
            .populate({ path: 'partner', select: 'name' })
            .populate({ path: 'subject', select: { name: true } })
            .populate({ path: 'room', select: { name: true } })
            .read('primary')
            .exec();

        let patients = [];
        if (typeof booking.patientVersion !== typeof undefined) {
            patients = await this.patientVersionModel
                .find({ id: booking.patientVersionId })
                .exec();
        } else {
            patients = await this.patientModel
                .find({ id: booking.patientId })
                .exec();
        }

        const patient = first(patients);
        const patientObj = patient.toObject();
        const bookingObj = booking.toObject();

        const user = await this.userModel.findOne({ _id: bookingObj.userId });

        if (bookingObj.appId === 'umc' &&  bookingObj.syncBookingType === 1) {
            this.eventEmmiter.emit(SEND_ZNS_PORTAL, {
                zaloTemplateId: '333381',
                params: {
                    ten_bn: `${patientObj.surname} ${patientObj.name}`,
                    ten_bv: `${bookingObj.partner.name}`,
                    ma_phieu: bookingObj.bookingCodeV1 || bookingObj.bookingCode,
                    ngay_kham: moment(bookingObj.date).add(7, 'hours').format('HH:mm DD/MM/YYYY'),
                    chuyen_khoa:  get(bookingObj, 'extraInfo.booking.subject.name'),
                    phong_kham:  get(bookingObj, 'extraInfo.booking.room.name'),
                    huong_dan:  'cach-dang-ky-kham-benh-dai-hoc-y-duoc-don-gian',
                },
                phone: user.username.replace(/^(\+84)/, '84'),
            });

            return {
                sent: true,
            }
        } else {
            // apply các partner trên V2
            console.log('apply các partner trên V2')
            console.log('bookingObj.appId', bookingObj.appId)
            if (this.newSetZNSPartnersV2.has(bookingObj.appId)) {
                let huong_dan = '';
                switch (bookingObj.appId) {
                    case 'bvmathcm':
                        huong_dan = 'huong-dan-di-kham-benh-vien-mat-tphcm';
                        break;
                    case 'choray':
                        huong_dan = 'cach-dat-lich-kham-benh-vien-cho-ray';
                        break;
                    case 'nhidong1':
                        huong_dan = 'huong-dan-dat-lich-kham-benh-vien-nhi-dong-1';
                        break;
                    case 'nhidonghcm':
                        huong_dan = 'huong-dan-dat-lich-kham-tai-benh-vien-nhi-dong-thanh-pho';
                        break;
                    case 'bvpsct':
                        huong_dan = 'benh-vien-phu-san-can-tho-quy-trinh-tham-kham-va-chi-phi';
                        break;
                    case 'umc2':
                        huong_dan = 'huong-dan-di-kham-benh-vien-dh-y-duoc-cs2';
                        break;
                    case 'umc3':
                        huong_dan = 'huong-dan-di-kham-benh-vien-dh-y-duoc-cs3';
                        break;
                    case 'ctchhcm':
                        huong_dan = 'huong-dan-di-kham-bv-chan-thuong-chinh-hinh';
                        break;
                    case 'dalieuhcm':
                        huong_dan = 'huong-dan-di-kham-benh-vien-da-lieu-tphcm';
                        break;
                    case 'binhthanhhcm':
                        huong_dan = 'huong-dan-di-kham-benh-vien-quan-binh-thanh';
                        break;
                    case 'leloi':
                        huong_dan = 'huong-dan-di-kham-benh-vien-vung-tau';
                        break;
                    case 'hoanmytd':
                        huong_dan = 'huong-dan-di-kham-benh-vien-hoan-my-thu-duc';
                        break;
                    default:
                        huong_dan = 'huong-dan-dat-lich-kham-benh-qua-tong-dai-19002115-nhanh-chong-va-tien-loi';
                        break;
                }
                // console.log('zns template:', {
                //     zaloTemplateId: '333381',
                //     params: {
                //         ten_bn: `${patientObj.surname} ${patientObj.name}`,
                //         ten_bv: `${bookingObj.partner.name}`,
                //         ma_phieu: bookingObj.bookingCodeV1 || bookingObj.bookingCode,
                //         ngay_kham: moment(bookingObj.date).add(7, 'hours').format('HH:mm DD/MM/YYYY'),
                //         chuyen_khoa: get(bookingObj, 'subject.name', 'Đang cập nhật'),
                //         phong_kham: get(bookingObj, 'room.name', 'Đang cập nhật'),
                //         huong_dan,
                //     },
                //     phone: user.username.replace(/^(\+84)/, '84'),
                // })
                this.eventEmmiter.emit(SEND_ZNS_PORTAL, {
                    zaloTemplateId: '333381',
                    params: {
                        ten_bn: `${patientObj.surname} ${patientObj.name}`,
                        ten_bv: `${bookingObj.partner.name}`,
                        ma_phieu: bookingObj.bookingCodeV1 || bookingObj.bookingCode,
                        ngay_kham: moment(bookingObj.date).add(7, 'hours').format('HH:mm DD/MM/YYYY'),
                        chuyen_khoa: get(bookingObj, 'subject.name', 'Đang cập nhật'),
                        phong_kham: get(bookingObj, 'room.name', 'Đang cập nhật'),
                        huong_dan,
                    },
                    phone: user.username.replace(/^(\+84)/, '84'),
                });

                return {
                    sent: true,
                }
            }
        }

        return {
            sent: false,
        }
    }

    async pushZnsBookingStored() {
        const data = await this.pushZnsBookingModel.find({ status: 0 }).exec();

        for (const item of data) {
            try {
                await this.pushZnsBookingSuccess({
                    id: item.bookingId
                })

                await this.pushZnsBookingModel.updateOne({ _id: item._id }, { status: 1 }).exec();
            } catch (err) {
                await this.pushZnsBookingModel.updateOne({ _id: item._id }, { status: -1, error: this.utilService.errorHandler(err) }).exec();
            }
        }
    }

    async logBookingConstraintPatient({ booking, patient }: any) {
        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId: booking.partnerId }, { da_tung_khamt_btn: true }).exec();
        if (booking.partnerId !== 'nhidong1' && !partnerConfig.da_tung_khamt_btn) {
            return {
                isValid: true,
            }
        }

        const baseUrl = this.urlConfigService.getUrlCheckBookingRules();

        const { data } = await this.http.post(`${baseUrl}/booking-rules/patient/detail`,
            {
                patientIds: [patient.id],
            }, {
                headers: {
                    partnerid: booking.partnerId,
                    locale: booking.locale || '',
                    appid: booking.appId,
                }
            }
        ).toPromise();
        const firstPatient = first<any>(data);
        if (get(firstPatient, 'constraintInfo.isValid') === false) {
            const user = await this.userModel.findOne({ _id: booking.userId }, { username: true, fullname: true }).exec()
            const saveData = {
                bookingId: booking._id,
                transactionId: booking.transactionId,
                patient: {
                    ...patient,
                    constraintInfo: firstPatient.constraintInfo,
                },
                user: user.toObject(),
                appId: booking.appId,
                partnerId: booking.partnerId,
                platform: booking.platform,
                bookingDate: booking.date,
            }

            const item = await this.bookingPatientConstraintModel.create(saveData);

            //addCronJob
            let jobTime : any
            //Medium
            // const { millisecondsMedium = 3000 } = await this.globalSettingService.findByKeyAndRepoNameJSON('COMPLETE_INFO_LARK_NOTIF_CONFIG_MEDIUM')
            // jobTime = moment().add(millisecondsMedium, 'milliseconds')
            // await this.addCronJobCompleteInfo({
            //     booking,
            //     patient,
            //     time: millisecondsMedium,
            //     jobTime,
            //     type: 'MEDIUM',
            //     data: item.toObject()
            // })
            //Highest
            const { millisecondsHighest = 6000 } = await this.globalSettingService.findByKeyAndRepoNameJSON('COMPLETE_INFO_LARK_NOTIF_CONFIG_HIGHEST')
            jobTime = moment().add(millisecondsHighest, 'milliseconds')
            await this.addCronJobCompleteInfo({
                booking,
                patient,
                time: millisecondsHighest,
                jobTime,
                type: 'HIGHEST',
                data: item.toObject()
            })

            return {
                isValid: false,
                saved: true,
            }
        } else {
            return {
                isValid: true,
            }
        }
    }

    async handleBookingPartnerNotYetSignContract(hospital: any, msgContent: string) {
        if (hospital.isContractSigned === false && hospital.status === 1) {
            this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                webHub: this.larkConfig.larkBookingSuccessPartnerNotYetSignedContract,
                data: {
                    msg_type: 'interactive',
                    card: msgContent,
                },
            })
        }
    }

    async pushNotifReExam(reExamId: string): Promise<any> {
        try {
            const reExam = await this.reExamVerifyModel.findOne({ reExamId }).exec();
            const { patientDetail: { patientPhoneNumber, patientLastName, patientFirstName }, scheduleDate, partnerId } = reExam;

            const [titleTemplatePartner, titleTemplateMedpro, partnerAllowPushReExam, hospital] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('REEXAM_NOTIF_TITLE_TEMPLATE'),
                this.globalSettingService.findByKeyAndRepoName('REEXAM_NOTIF_TITLE_TEMPLATE_MEDPRO'),
                this.globalSettingService.findByKeyAndRepoName('RE_EXAMS_PARTNER_CONFIG'),
                this.hospitalModel.findOne({ partnerId }, { name: true }).exec()
            ])

            const patientName = `${patientLastName} ${patientFirstName}`;
            const scheduleDateString = moment(scheduleDate).utc().add(7, 'hours').format('DD-MM-YYYY');

            const titleMedpro = titleTemplateMedpro.replace('{hospitalName}', hospital.name)
                .replace('{patientName}', patientName)
                .replace('{date}', scheduleDateString);

            const titlePartner = titleTemplatePartner.replace('{hospitalName}', hospital.name)
                .replace('{patientName}', patientName)
                .replace('{date}', scheduleDateString);
            console.log('patientPhoneNumber', patientPhoneNumber)
            const yourphone = `${patientPhoneNumber}`.replace(/^[+]84|^0/, '+84').replace(/^9/, '+849').replace(/^3/, '+843');
            console.log('yourphone', yourphone)
            const user = await this.userModel.findOne({ username: yourphone }).exec()
            console.log('user', user)
            const payload = {
                // title:'',
                content: "",
                requestId: "",
                clientViewId: "",
                subscribed: false,
                isNotif: true,
                isRead: false,
                isPushNotif: true,
                isSendMail: false,
                // id: this.utilService.generateUUID(),
                userId: user._id,
                // appId: "medpro",
                partnerId,
                eventData: {
                    urlId: reExam.urlId,
                },
                devices: [],
            }

            // push medpro
            const payloadMedpro = {
                ...payload,
                id: this.utilService.generateUUID(),
                appId: 'medpro',
                title: titleMedpro,
                type: 102,
                topicId: "bookings.tai-kham-nhanh",
                topic: "bookings.tai-kham-nhanh",
            }

            this.eventEmmiter.emit(MESSAGE_EVENT, payloadMedpro);
            this.createEvent({
                ...payloadMedpro,
                createTime: moment().toISOString(),
            }, true, false, false)

            if (partnerAllowPushReExam.includes(partnerId)) {
                // Push partner
                const payloadPartner = {
                    ...payload,
                    eventData: {
                        title: titlePartner,
                        content: titlePartner,
                        tel: '19002115',
                        type: 100,
                    },
                    id: this.utilService.generateUUID(),
                    appId: partnerId,
                    title: titlePartner,
                    type: 100,
                    topic: 'messages.call-19002115',
                    topicId: 'messages.call-19002115',
                }

                this.eventEmmiter.emit(MESSAGE_EVENT, payloadPartner);
                this.createEvent({
                    ...payloadPartner,
                    createTime: moment().toISOString(),
                }, true, false, false)
            }
        } catch (error) {
            console.log(`error pushNotifReExam reExamId = ${reExamId}`, error);
        }

    }

    async removeInvalidMessageEvent(): Promise<void> {
        return this.messageEventModel.find({
            repoName: {
                $in: [null, 'api-v2-120', 'api-v2-122']
            }
        }).remove().catch(e => {
            console.log(`${MessageEventService.name}: ${JSON.stringify(e)}`);
        });
    }

    async pushNotifBookingTelemedCompleted(body: any) {
        console.log('pushNotifBookingTelemedCompleted: ', body.id);
        const booking = await this.bookingModel
            .findOne( { _id: body.id })
            .populate('doctor')
            .populate({
                path: 'partner',
                select: {
                    partnerId: 1,
                    name: 1,
                },
            })
            .exec();
        if (!booking) {
            throw new HttpException("Không tìm thấy booking", 404);
        }

        const bookingObj = booking.toObject();
        const [configString, hospital, user, payment] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('CONFIG_FORM_SURVEY_BOOKING_TELEMED'),
            this.hospitalModel.findOne({ partnerId: bookingObj.partnerId }, { name: true }).exec(),
            this.userModel.findOne({ _id: bookingObj.userId }, { username: true, fullname: true }).exec(),
            this.paymentModel.findOne({ transactionId: bookingObj.transactionId }, { paymentMethod: true, gatewayId: true }).exec(),
        ]);

        const envConfigSurvey = JSON.parse(configString);

        const { content } = envConfigSurvey
        envConfigSurvey.url = "https://pkh-medpro.larksuite.com/share/base/form/shrusaIssyZUdlXQksCrDIrzZdf";
        const contentOverride = content.replace('{doctorName}', bookingObj.doctor?.name);

        const overrideUrl = queryString.stringifyUrl({
            url: envConfigSurvey.url,
            query: {
                hide_ctt: 1,
                hide_bookingid: 1,
                hide_partnerId: 1,
                hide_pttt: 1,
                hide_userid: 1,
                hide_appId: 1,
                hide_partnerName: 1,
                prefill_partnerName: bookingObj?.partner?.name || "",
                prefill_appId: bookingObj.appId,
                prefill_bookingid: bookingObj.bookingCodeV1 || bookingObj.bookingCode,
                prefill_ctt: payment?.gatewayId || 'NO_CONFIG',
                prefill_partnerId: bookingObj.doctor?.name,
                prefill_pttt: payment?.paymentMethod || 'NO_CONFIG',
                prefill_userid: user?.username || 'NO_CONFIG',
            },
        }); 

        this.eventEmmiter.emit(MESSAGE_EVENT, {
            topic: 'booking.survey.form',
            userId: bookingObj.userId,
            appId: bookingObj.appId,
            title: contentOverride,
            partnerId: bookingObj.partnerId,
            eventData: {
                ...bookingObj,
                content: contentOverride,
                type: 100,
                url: overrideUrl,
            },
            type: 100,
            isPushNotif: true,
            isSendMail: false,
            repoName: 'SurveyForm',
        });

        /* tiến hành bắn event */
        await this.createEvent(
            {
                topicId: 'booking.survey.form',
                createTime: moment().toISOString(),
                userId: bookingObj.userId,
                appId: bookingObj.appId,
                title: contentOverride,
                partnerId: bookingObj.partnerId,
                eventData: {
                    ...bookingObj,
                    type: 100,
                    url: overrideUrl,
                },
                type: 100,
            },
            true,
            false,
            false,
        );

        const msgContent = larkMsgEventCompleteBookingTelemed({
            larkEnv: this.LARK_ENV_TEXT,
            bookingCode: bookingObj.bookingCodeV1 || bookingObj.bookingCode,
            doctorName: bookingObj.doctor?.name,
            hospitalName: hospital.name,
            medproId: `${user.fullname? `${user.fullname} ` : ''}(${user.username})`,
            message: 'Xác nhận hoàn thành phiếu khám TELEMED',
            formUrl: overrideUrl,
        });

        this.eventEmmiter.emit(PUSH_NOTIF_BOOKING_NEW_NOTICE_LARK, {
            webHub: this.larkConfig.larkEnv.toUpperCase() === 'PRODUCTION' ?
                'https://open.larksuite.com/open-apis/bot/v2/hook/9bddc956-fda0-4f0e-82d6-d2afb8953d56' :
                'https://open.larksuite.com/open-apis/bot/v2/hook/96c3ce6b-2f8b-4a34-8c9c-425a7b6a6f13',
            data: {
                msg_type: 'interactive',
                card: msgContent,
            },
        });
    }

    public async handleSendUserNotiOnTelemedCompleted ({
        bookingId
    }:{
        bookingId: string
    }) {
        const booking = await this.bookingModel
            .findOne( { _id: bookingId })
            .populate('doctor' , { name: true })
            .populate({
                path: 'partner',
                select: {
                    partnerId: 1,
                    name: 1,
                },
            })
            .exec();

        const bookingObj = booking.toObject();

        const [configString, hospital, user, payment] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('CONFIG_FORM_SURVEY_BOOKING_TELEMED'),
            this.hospitalModel.findOne({ partnerId: bookingObj.partnerId }, { name: true }).exec(),
            this.userModel.findOne({ _id: bookingObj.userId }, { username: true, fullname: true }).exec(),
            this.paymentModel.findOne({ transactionId: bookingObj.transactionId }, { paymentMethod: true, gatewayId: true }).exec(),
        ])

        let envConfigSurvey: {
            env: string
            title: string
            content: string
            url: string
        } = JSON.parse(configString);
         
        // ? **********************************************************************************************************
        // ? DEVELOP ONLY
        const devOverrideUrl = "https://pkh-medpro.larksuite.com/share/base/form/shrusaIssyZUdlXQksCrDIrzZdf"
        envConfigSurvey.url = devOverrideUrl

        const contentOverride = envConfigSurvey.content.replace('{doctorName}', bookingObj.doctor?.name); 
        // ? **********************************************************************************************************

        const overrideUrl = queryString.stringifyUrl({
            url: envConfigSurvey.url,
            query: {
                hide_ctt: 1,
                hide_bookingid: 1,
                hide_partnerId: 1,
                hide_pttt: 1,
                hide_userid: 1,
                hide_appId: 1,
                hide_partnerName: 1,
                prefill_partnerName: bookingObj?.partner?.name || "",
                prefill_appId: bookingObj.appId,
                prefill_bookingid: bookingObj.bookingCodeV1 || bookingObj.bookingCode,
                prefill_ctt: payment?.gatewayId || 'NO_CONFIG',
                prefill_partnerId: bookingObj.doctor?.name,
                prefill_pttt: payment?.paymentMethod || 'NO_CONFIG',
                prefill_userid: user?.username || 'NO_CONFIG',
            },
        });   

        // ? **********************************************************************************************************
        // ? PUSH NOTIFICATION -> DEVELOP
        this.eventEmmiter.emit(MESSAGE_EVENT, {
            topic: 'booking.survey.form',
            userId: bookingObj.userId,
            appId: bookingObj.appId,
            title: contentOverride,
            partnerId: bookingObj.doctor?.name,
            eventData: {
                ...bookingObj,
                content: contentOverride,
                type: 100,
                url: overrideUrl,
            },
            type: 100,
            isPushNotif: true,
            isSendMail: false,
            repoName: 'SurveyForm',
        });    

        /* tiến hành bắn event */
        await this.createEvent(
            {
                topicId: 'booking.survey.form',
                createTime: moment().toISOString(),
                userId: bookingObj.userId,
                appId: bookingObj.appId,
                title: contentOverride,
                partnerId: bookingObj.doctor?.name,
                eventData: {
                    ...bookingObj,
                    type: 100,
                    url: overrideUrl,
                },
                type: 100,
            },
            true,
            false,
            false,
        );

        const msgContent = larkMsgEventCompleteBookingTelemed({
            larkEnv: this.LARK_ENV_TEXT,
            bookingCode: bookingObj.bookingCodeV1 || bookingObj.bookingCode,
            doctorName: bookingObj.doctor?.name,
            hospitalName: hospital.name,
            medproId: `${user.fullname? `${user.fullname} ` : ''}(${user.username})`,
            message: 'Xác nhận hoàn thành phiếu khám TELEMED',
            formUrl: overrideUrl,
        });

        this.eventEmmiter.emit(PUSH_NOTIF_BOOKING_NEW_NOTICE_LARK, {
            webHub: this.larkConfig.larkEnv.toUpperCase() === 'PRODUCTION' ?
                'https://open.larksuite.com/open-apis/bot/v2/hook/9bddc956-fda0-4f0e-82d6-d2afb8953d56' :
                'https://open.larksuite.com/open-apis/bot/v2/hook/96c3ce6b-2f8b-4a34-8c9c-425a7b6a6f13',
            data: {
                msg_type: 'interactive',
                card: msgContent,
            },
        });
        // ? **********************************************************************************************************

        return {
            url: overrideUrl,
            query: queryString.parseUrl(overrideUrl)
        }
    }

    async pushNotifRemindConfirmBookingTelemed(body: any) {
        let condition : any ;

        const [treeIdTelemed, config] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('TELEMED_TREE_ID_RELATED'),
            this.globalSettingService.findByKeyAndRepoNameJSON('CONFIG_NOTIF_REMIND_COMPLETE_BOOKING'),
        ]);

        if (body.bookingCode) {
           condition = {
               bookingCode: body.bookingCode
           }
        } else {
           condition = {
               date: {
                   $gte: moment().utc().startOf('day').utc().subtract(7, 'hours').toDate(),
                   $lte: moment().utc().endOf('day').utc().subtract(7, 'hours').toDate(),
               },
               status: 1,
               treeId: { $in: treeIdTelemed.split(',') },
               examStatus: { $ne: 'COMPLETED' },
               serviceType: 'ADVISORY',
           }
        }

        const bookings = await this.bookingModel.find(condition, { doctor: true, treeId: true, date: true, partnerId: true, bookingCode: true, transactionId: true })
            .populate('doctor')
            .populate('patient')
            .exec();

        this.logger.log('pushNotifRemindConfirmBookingTelemed: ', `${bookings.length}`);

        const { title } = config;
        for (const booking of bookings) {
            const bookingObj = booking.toObject();
            const { bookingCode, transactionId } = bookingObj
            const topic = 'bookings.remind-complete-booking';

            const content = title
                .replace('{date}', moment(bookingObj.date).utc().add(7, 'hours').format('DD/MM/YYYY'))
                .replace('{time}', moment(bookingObj.date).utc().add(7, 'hours').format('HH:mm'))
                .replace('{doctorName}', bookingObj.doctor?.name)
                .replace('{patientName}', `${bookingObj.patient?.surname} ${bookingObj.patient?.name}`);

            const payload : CreateMessageEventMepdroDoctorDto = {
                topic,
                userId: bookingObj.doctor?._id,
                title: 'Xác nhận phiếu khám',
                content,
                partnerId: bookingObj.partnerId,
                eventData: {
                    bookingCode,
                    transactionId,
                    type: 1,
                },
                type: 1,
                isPushNotif: true,
                isNotif: true,
                isSendMail: false,
            };

            this.eventEmmiter.emit(MESSAGE_EVENT_MEDPRO_DOCTOR, payload);
        }

        return {
            count: bookings.length,
            date: {
                $gte: moment().utc().startOf('day').utc().subtract(7, 'hours').toDate(),
                $lte: moment().utc().endOf('day').utc().subtract(7, 'hours').toDate(),
            },
        }
    }

    async pushNotifAllUsersOneSignal(data: PushNotifAllUsersDTO): Promise<any> {
        const { messageInfo: messageObj, partnerInfo: getPartner, segments, info, queryPushNotifDTO } = data;

        const client = new OneSignal.Client(getPartner.notifAppId, getPartner.notifApiKey,
            { apiRoot: 'https://onesignal.com/api/v1' });

        let url = messageObj.url;
        if (messageObj.url) {
            const parsedUrl = new URL(messageObj.url);
            parsedUrl.searchParams.append('inNotiApp', 'true');
            url = parsedUrl.toString();
        }

        // const messageObj = messageInfo.toObject();
        const defaultNotif = {
            contents: {
                en: messageObj.content,
                vi: messageObj.content,
            },
            headings: {
                en: messageObj.title,
                vi: messageObj.title,
            },
            data: {
                ...messageObj,
                ...(url && { url }),
                topicId: 'messages.cta.screen',
                type: 105,
                ...info,
                cta: queryPushNotifDTO.cta
            },
            included_segments: [segments],
        };


        try {
            console.log(`buildReceiverList ${queryPushNotifDTO.requestId}: before client.createNotification `);
            const response = await client.createNotification({ ...defaultNotif });
            const { body } = response;
            console.log(`buildReceiverList ${queryPushNotifDTO.requestId}: done client.createNotification, has body: ${!!body} `);
            if (body) {
                // const { id, recipients } = body;
                return {
                    body,
                    message: 'Gửi thông báo thành công!',
                    isOK: true,
                };
            } else {
                return {
                    isOK: false,
                    message: 'Gửi KHÔNG thành công. Vui lòng thử lại sau!',
                };
            }
        } catch (error) {
            // console.log('error push onsSignal: ', error);
            // this.eventEmmiter.emit(LOG_SERVICE_EVENT, {
            //     name: 'buildReceiverList',
            //     summary: 'Gửi oneSignal group all',
            //     nameParent: 'buildReceiverList',
            //     params: { ...queryPushNotifDTO, traceId },
            //     errorBody: { ...this.utilService.errorHandler(error) },
            //     message: error.message,
            // });
            return {
                body: Object.create(null),
                isOK: false,
                message: 'Gửi KHÔNG thành công. Vui lòng thử lại sau.',
            };
        }
    }

    async createMessageCTA(data: MessageCTADTO): Promise<any> {

        const { partnerId, type, title, content, eventData = {}, devices} = data;
        

        this.eventEmmiter.emit(MESSAGE_EVENT_MESSAGE_CTA, {
            topic: data.topicId,
            appId: data.appId,
            userId: data.userId,
            title: title,
            content,
            type,
            partnerId: partnerId,
            eventData: {
                ...eventData,
                content,
                type,
            },
            devices,
            isPushNotif: true,
            isNotif: false,
            isSendMail: false,
        })

        return { isOk: true, isOK: true };
    }

    async insertMedproDoctorNotif(body: CreateMedproDoctorNotifDto) {
        const { partnerId, type, doctorId, title, content, eventData = {}, isPushNotif = true, isNotif = true, isSendMail = false } = body;
        const doctor = await this.doctorModel.findOne({ id: doctorId }).exec();

        if (!doctor) {
            throw new HttpException(`Không tìm thấy bác sĩ ${doctorId}`, HttpStatus.NOT_FOUND);
        }

        this.eventEmmiter.emit(MESSAGE_EVENT_MEDPRO_DOCTOR, {
            topic: body.topicId,
            userId: doctor._id,
            title: title,
            content,
            type,
            partnerId: partnerId,
            eventData: {
                ...eventData,
                content,
                type,
            },
            isPushNotif,
            isNotif,
            isSendMail,
        })

        return { isOk: true, isOK: true };
    }

    async pushNotifRefund(body: PushNotifRefundDto) {
        const { title, content, bookingCode } = body;
        const booking = await this.bookingModel.findOne({ bookingCode }).exec();

        if (!booking) {
            throw new HttpException(`Không tìm phiếu khám ${bookingCode}`, HttpStatus.NOT_FOUND);
        }

        this.eventEmmiter.emit(MESSAGE_EVENT, {
            topic: 'bookings.refund',
            userId: booking.userId,
            title: content || title,
            type: 1,
            partnerId: booking.partnerId,
            appId: booking.appId,
            eventData: {
                transactionId: booking.transactionId,
                type: 1,
            },
            isPushNotif: true,
            isSendMail: false,
        });

        await this.createEvent(
            {
                topicId: 'bookings.refund',
                createTime: moment().toISOString(),
                userId: booking.userId,

                appId: booking.appId,
                title,
                partnerId: booking.partnerId,
                type: 1,
                eventData: {
                    transactionId: booking.transactionId,
                    content,
                },
            },
            true,
            false,
            false,
        );

        booking.set({
            refundData: {
                title,
                content,
                date: new Date(),
            },
        })
        await booking.save();

        return { isOk: true };
    }

    async insertUserNotif(body: InsertUserNotifDto) {
        this.validateUserNotifDto(body);

        const { receiver, topicId, appId, partnerId, title, content, type, eventData } = body;

        const username = receiver.replace(/^(84|0)/, '+84');
        const user = await this.userModel
            .findOne({
                username,
                medproId: `mp${username}`,
            })
            .exec();

        if (!user) {
            throw new HttpException(`Không tìm thấy user ${user}`, HttpStatus.NOT_FOUND);
        }
        switch (topicId) {
            case 'returnResult.telemedPromotion':
                this.eventEmmiter.emit(MESSAGE_EVENT, {
                    topic: topicId,
                    userId: user._id,
                    title,
                    content,
                    type,
                    partnerId,
                    appId,
                    eventData,
                    isPushNotif: true,
                    isSendMail: false,
                });
                break;
            default:
                this.eventEmmiter.emit(MESSAGE_EVENT, {
                    topic: topicId,
                    userId: user._id,
                    title: content || title,
                    type,
                    partnerId,
                    appId,
                    eventData: {
                        ...eventData,
                        content: content || title,
                        type,
                    },
                    isPushNotif: true,
                    isSendMail: false,
                });
                break;
        }
        await this.createEvent(
            {
                topicId,
                createTime: moment().toISOString(),
                userId: user._id,
                appId,
                title,
                partnerId,
                type,
                content,
                eventData: {
                    ...eventData,
                    type,
                    content,
                },
            },
            true,
            false,
            false,
        );

        if (appId === 'umc') {
            if (type === 1 && eventData.transactionId) {
                const bookingV2 = await this.bookingModel.findOne({ transactionId: eventData.transactionId }, { extraInfo: true }).exec();
                await this.pkhPatientKnex('notification').insert({
                    title,
                    content,
                    type,
                    booking_id: get(bookingV2, 'extraInfo.booking.id'),
                    user_id: get(bookingV2, 'extraInfo.booking.user_id'),
                });
            }
        }

        if (partnerId === 'nhidong1') {
            const msgContent = larkMessageNotifNhidong1({
                larkEnv: this.larkConfig.larkEnv,
                message: 'Gửi thông báo Nhi Đồng 1',
                username,
                fullname: user.fullname,
                title,
                content,
                topicId,
            });

            this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                webHub: this.larkConfig.larkNotifNhidong1,
                data: {
                    msg_type: 'interactive',
                    card: msgContent,
                },
            });
        }

        return { isOk: true };
    }

    validateUserNotifDto(dto: InsertUserNotifDto) {
        const { type, eventData } = dto;
        const messageValidate = [];
        if (type === 1 && !eventData.transactionId) {
            messageValidate.push({ property: 'eventData.transactionId', message: 'type = 1 require eventData.transactionId để mở phiếu khám'});
        }

        if (messageValidate.length > 0) {
            throw new HttpException({
                message: 'Thông tin không hợp lệ',
                errors: messageValidate,
            }, 400);
        }
    }

    async patientTracking(data: any) {

        const { bookingId, partnerId } = data.extraInfo || {};

        const [patient, userAction, userPatient] = await Promise.all([
            this.patientModel.findOne({ _id: data.patient }).exec(),
            this.userModel.findOne({ _id: data.userAction }).exec(),
            this.userModel.findOne({ _id: data.userPatient }).exec(),
        ]);

        let booking;
        let partner;
        if (bookingId) {
            booking = await this.bookingModel.findOne({ _id: bookingId }, { bookingCode: true }).exec();
        }

        if (partnerId) {
            partner = await this.hospitalModel.findOne({ partnerId }, { name: true }).exec();
        }

        this.patientTrackingModel.create({...data})
            .catch(e0 => {
                console.log(`Save patientTrackingModel: ${e0}`);
            });

        this.larkService.sendMessageTrackingPatient({
            action: PatientTrackingActionTextEnum[data.action] + (booking ? ` ${ booking?.bookingCode }` : ''),
            patient: patient?.toObject(),
            patientUser: userPatient.toObject(),
            cskhUser: userAction.toObject(),
            partner,
        });
    }

    async repaymentSuccess(body: any) {
        const [booking, { ignoredUserIds = [] }] = await Promise.all([
            this.bookingModel.findOne(
                { bookingCode: body.bookingCode },
                { userId: true, patient: true, cskhUserId: true, partnerId: true, status: true, date: true, id: true, sequenceNumber: true }
            ).exec(),
            this.globalSettingService.findByKeyAndRepoNameJSON('REPAYMENT_LARK_NOTIF_CONFIG'),
        ])

        if (ignoredUserIds.includes(booking.userId)) {
            return {
                ignored: true
            };
        }

        const [hospital, patient, user, userCskh] = await Promise.all([
            this.hospitalModel.findOne({ partnerId: booking.partnerId }, { name: true }).exec(),
            this.patientModel.findById(booking.patient, { surname: true, name: true }).exec(),
            this.userModel.findById(booking.userId).exec(),
            this.userModel.findById(booking.cskhUserId).exec(),
        ]);

        const data = await this.repaymentLogModel.create({
            ...body,
            partnerId: booking.partnerId,
            partnerName: hospital.name,
            MedproId: user.medproId,
            FullName: user.fullname,
            PatientFullName: `${patient.surname} - ${patient.name}`,
            SequenceNumber: booking.sequenceNumber,
            userCskh: userCskh ? `${userCskh.username} - ${userCskh.fullname}` : null,
            bookingStatus: booking.status,
            bookingDate: booking.date,
            bookingId: booking.id,
        });

        await this.addCronJobRepayment(data.toObject());
        return { isOk: true };
    }

    async addCronJobRepayment(data: any) {
        const { minutes = 5 } = await this.globalSettingService.findByKeyAndRepoNameJSON('REPAYMENT_LARK_NOTIF_CONFIG')

        const jobTime = moment(data.createdAt).add(minutes, 'minutes')

        if (jobTime.isBefore(moment())) {
            await this.repaymentLogModel.updateOne(
                { _id: data._id },
                {
                    tracking: {
                        message: 'Time left',
                    },
                    deleted: true,
                }
            ).exec();
            return;
        }

        // const jobTime = moment().utc().add(2, 'second')
        const jobName = `job_repayment_lark_notif_${jobTime.toISOString()}_${data._id}`

        const job = new CronJob(jobTime.toDate(), async () => {
            try {
                this.logger.log(`${jobName} started`);
                const booking = await this.bookingModel.findOne({ bookingCode: data.bookingCode })
                    .populate('subject')
                    .exec();

                if (!booking) {
                    this.repaymentLogModel.updateOne(
                        { _id: data._id },
                        {
                            tracking: {
                                message: 'Booking not found',
                            }
                        }).exec();
                    return;
                }

                const bookingObj = booking.toObject();

                if ([BookingStatus.RESERVE, BookingStatus.SHARETOPAY].includes(bookingObj.status)) {
                    const [user, partner, patient, cskhUser] = await Promise.all([
                        this.userModel.findOne({ _id: booking.userId }).exec(),
                        this.hospitalModel.findOne({ partnerId: booking.partnerId }, { name: true }).exec(),
                        bookingObj?.patientVersion ? this.patientVersionModel.findOne({ _id: bookingObj.patientVersion })
                                .populate('city')
                                .populate('district')
                                .populate('ward')
                                .exec() :
                            this.patientModel.findOne({ id: bookingObj.patientId })
                                .populate('city')
                                .populate('district')
                                .populate('ward')
                                .exec(),
                        this.userModel.findOne({ _id: booking.cskhUserId }).exec(),
                    ])

                    const msgContent = larkMsgRepaymentSuccess({
                        larkEnv: this.larkConfig.larkEnv,
                        message: 'Thanh toán lại phiếu khám',
                        partner: partner.toObject(),
                        user: user.toObject(),
                        patient: {
                            ...patient.toObject(),
                            fullAddress: this.patientMongoService.getFullAddress(patient.toObject()),
                        },
                        booking: bookingObj,
                        subject: bookingObj.subject,
                        time: moment(data.createdAt).add(7, 'hours').format('HH:mm DD/MM/YYYY'),
                        cskhUser: cskhUser?.toObject(),
                        transactionId: data.transactionId,
                    });

                    this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                        webHub: this.larkConfig.larkRepaymentSuccessUrl,
                        data: {
                            msg_type: 'interactive',
                            card: msgContent,
                        },
                    });

                    await this.repaymentLogModel.updateOne(
                        { _id: data._id },
                        {
                            tracking: {
                                bookingStatus: bookingObj.status,
                                isSent: true,
                            }
                        }).exec();
                } else {
                    await this.repaymentLogModel.updateOne(
                        {
                            _id: data._id
                        },
                        {
                            tracking: {
                                bookingStatus: bookingObj.status,
                                cskhUserId: bookingObj.cskhUserId,
                                isSent: false,
                            }
                        }).exec();
                }
            } catch (err) {
                console.error(`${jobName} err`, err);
            } finally {
                await this.repaymentLogModel.updateOne({ _id: data._id }, { deleted: true }).exec();
                this.scheduler.deleteCronJob(jobName);
            }
        })

        try {
            this.scheduler.addCronJob(jobName, job);
            this.logger.log(`${jobName} registered`);
            job.start();
        } catch (error) {
            return;
        }
    }

    async registerExistRepaymentLogItem(id?: string) {
        try {
            const condition = id ? { _id: id } : { deleted: { $ne: true } }
            const data = await this.repaymentLogModel.find(condition).exec();

            for (const item of data) {
                await this.addCronJobRepayment(item.toObject());
            }
        } catch (err) {
            console.error('err registerExistRepaymentLogItem', err);
        }
    }

    async bookingFullSlotHook(body: any) {
        const partnerId = 'umc';
        const { appId = 'umc', bookingId, userId, patientId, subjectId, doctorId, date } = body;

        if (appId === 'momo') {
            return { isOk: false };
        }

        let userIdV1 = userId;
        if (!userIdV1) {
            if (bookingId) {
                const bookings = await this.pkhPatientKnex('booking')
                    .select('user_id')
                    .where('id', bookingId)
                    .limit(1);
                if (bookings.length > 0) {
                    userIdV1 = first<any>(bookings).user_id;
                } else {
                    throw new HttpException({
                        message: 'Không tìm thấy phiếu khám v1',
                        body,
                    }, 404);
                }
            } else {
                throw new HttpException({
                    message: "Tham số không hợp lệ",
                    body,
                }, 400);
            }
        }

        let user;

        if (appId === 'medpro') {
            const findUser = await this.userModel.findById(userId);
            user = findUser?.toObject();
        } else {
            user = await this.pkhPatientKnex('user').where('id', userIdV1).first();
        }

        if (!user) {
            throw new HttpException({
                message: 'Không tìm thấy user',
                body,
            }, 404);
        }

        let subject, doctor, patient;
        if (appId === 'medpro') {
            [subject,doctor,patient ]= await Promise.all([
                this.subjectModel.findOne({ id: subjectId }).exec(),
                this.doctorModel.findOne({ id: doctorId }).exec(),
                this.patientModel.findOne({ id: patientId }).exec(),
            ])
        } else {
            [subject,doctor,patient ]= await Promise.all([
                this.pkhPatientKnex('subject').where('id', subjectId).first(),
                this.pkhPatientKnex('doctor').where('id', doctorId).first(),
                patientId ? this.pkhPatientKnex('patient').where('id', patientId).first() : null,
            ])
        }

        const bookingDate = moment(date);

        // thông báo lark
        const hospital = await this.hospitalModel.findOne({ partnerId }, { name: true }).exec();
        const msgContent = larkBookingFullSlotUmcTemplate({
            larkEnv: this.larkConfig.larkEnv,
            message: `Người dùng booking hết số ở ${hospital.name}`,
            partner: hospital.toObject(),
            userFullName: user.fullname,
            username: user.username,
            subject,
            doctor,
            date,
            patient,
            appId,
        });

        this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
            webHub: this.larkConfig.larkBookingFullSlotUmcUrl,
            data: {
                msg_type: 'interactive',
                card: msgContent,
            },
        })

        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }, { checkBookingFullSlot: true }).exec()
        const { checkBookingFullSlot = {} } = partnerConfig;

        const {
            enabled = false,
            whiteListUserIds = []
        } = checkBookingFullSlot;

        if (enabled || whiteListUserIds.includes(userId)) {
            // lưu lại userd
            const bookingFullSlotData = await this.bookingFullSlotModel.create({
                userId: userIdV1,
                partnerId,
                date: bookingDate.toDate(),
                doctorId,
                subjectId,
                patientId,
                appId,
            })
            await this.addCronJobBookingFullSlot(bookingFullSlotData.toObject());
            return { iOk: true };
        } else {
            return {
                isOk: false,
                enabled,
            }
        }

    }

    async addCronJobBookingFullSlot(data: any) {
        const { partnerId, userId, patientId, subjectId, doctorId, date, appId = 'umc' } = data;
        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }, { checkBookingFullSlot: true }).exec()
        const { checkBookingFullSlot = {} } = partnerConfig;

        const {
            minutes = 15,
            title = 'Đặt khám tại Cơ sở 2',
            content = 'Không còn số với khung giờ bạn đã chọn tại cơ sở 1, bạn có thể đặt khám tại cơ sở 2',
            url = 'https://medpro.vn/chon-lich-kham?feature=booking.date&partnerId=umc2'
        } = checkBookingFullSlot;

        const jobTime = moment(data.createdAt).add(minutes, 'minutes')

        if (jobTime.isBefore(moment())) {
            await this.bookingFullSlotModel.updateOne(
                { _id: data._id },
                {
                    tracking: {
                        message: 'Time left',
                    },
                    deleted: true,
                }
            ).exec();
            return;
        }

        // const jobTime = moment().utc().add(2, 'second')
        const jobName = `job_booking_full_slot_${jobTime.toISOString()}_${data._id}`

        const job = new CronJob(jobTime.toDate(), async () => {
            let skipNoti = false;
            try {
                this.logger.log(`CronJob booking full slot: ${jobName} started`);
                if (appId === 'medpro') {
                    let condition: any = {
                        partnerId,
                        userId,
                        patientId,
                        subjectId,
                        ...(doctorId && { doctorId }),
                        date: {
                            $gt: moment()
                                .utc()
                                .add(1, 'day')
                                .startOf('day')
                                .subtract(7, 'hour')
                                .toDate(),
                        },
                        status: {
                            $in: [0, 1],
                        },
                    };

                    const booking = await this.bookingModel.findOne(condition, { bookingCode: true }).exec();

                    if (!booking) {
                        try {
                            await this.bookingFullSlotNotiLog.create({
                                id: `${moment(date).format('YYYYMMDD')}_${userId}_${subjectId}_${doctorId}`,
                            });
                        } catch (err) {
                            skipNoti = true;
                            return;
                        }

                        const type = 103;
                        const ctaObject = {
                            cta_1: {
                                name: 'Đặt khám',
                                action: 'booking',
                                partnerId: 'umc2',
                            },
                            cta_2: {
                                name: 'Tư vấn từ xa',
                                action: 'booking',
                                feature: 'booking.telemed',
                                screenTitle: 'Tư vấn khám bệnh từ xa',
                            },
                        };

                        this.eventEmmiter.emit(MESSAGE_EVENT, {
                            topic: 'booking.full-slot',
                            userId: userId,
                            title: content || title,
                            type,
                            partnerId,
                            appId,
                            eventData: {
                                content,
                                url,
                                type,
                                ...ctaObject,
                            },
                            isPushNotif: true,
                            isSendMail: false,
                        });

                        await this.createEvent(
                            {
                                topicId: 'booking.full-slot',
                                userId: userId,
                                title: title,
                                type,
                                partnerId,
                                appId,
                                eventData: {
                                    content,
                                    url,
                                    type,
                                    ...ctaObject,
                                },
                                createTime: moment().toISOString(),
                            },
                            true,
                            false,
                            false,
                        );
                    }

                    await this.bookingFullSlotModel
                        .updateOne(
                            { _id: data._id },
                            {
                                tracking: {
                                    hasBooking: !!booking,
                                    condition: JSON.stringify(condition),
                                    booking: booking?.toObject(),
                                },
                            },
                        )
                        .exec();
                } else {
                    const schedules = await this.pkhPatientKnex('schedule')
                        .select('schedule.id as id')
                        .innerJoin('hospital_subject', 'schedule.hospital_subject_id', 'hospital_subject.id')
                        .innerJoin('subject', 'hospital_subject.subject_id', 'subject.id')
                        .where('subject.id', Number(subjectId))
                        .where('schedule.doctor_id', Number(doctorId))
                        .where('schedule.is_old', 0);

                    // check if use has any booking since tomorrow
                    const fromDateV1 = moment()
                        .utc()
                        .add(7, 'hour')
                        .add(1, 'day')
                        .startOf('day');

                    const fromDateMysql = fromDateV1.format('YYYY-MM-DD HH:mm:ss');

                    const query = this.pkhPatientKnex('booking')
                        .select('id')
                        .where('user_id', Number(userId))
                        .whereIn(
                            'schedule_id',
                            schedules.map(s => s.id),
                        )
                        .where('booking_date', '>', fromDateMysql)
                        .whereIn('status', [0, 1]);

                    if (patientId) {
                        query.where('patient_id', Number(patientId));
                    }

                    const booking = await query.first();

                    if (!booking) {
                        try {
                            await this.bookingFullSlotNotiLog.create({
                                id: `${moment(date).format('YYYYMMDD')}_${userId}_${subjectId}_${doctorId}`,
                            });
                        } catch (err) {
                            skipNoti = true;
                            return;
                        }

                        const pushDevices = await this.pkhPatientKnex(this.pushDeviceTableName).where('user_id', userId);
                        if (pushDevices.length > 0) {
                            const [idNotif] = await this.pkhPatientKnex('notification').insert({
                                title,
                                content,
                                type: 3,
                                // type: (!!messageInfo.url ? 3 : 4),
                                url,
                                user_id: userId,
                            });

                            /* tìm lại thông tin notif vừa insert */
                            const insertedNotif = await this.pkhPatientKnex('notification')
                                .where({ id: idNotif })
                                .first();

                            this.eventEmmiter.emit(MESSAGE_EVENT, {
                                topic: 'booking.full-slot',
                                userId: userId,
                                title: content || title,
                                type: 1,
                                partnerId,
                                appId,
                                eventData: {
                                    ...insertedNotif,
                                },
                                isPushNotif: true,
                                isSendMail: false,
                                pushDevicesV1: pushDevices,
                            });
                        }
                    }

                    await this.bookingFullSlotModel
                        .updateOne(
                            { _id: data._id },
                            {
                                tracking: {
                                    hasBooking: !!booking,
                                    condition: JSON.stringify({
                                        userId,
                                        patientId,
                                        fromDateMysql,
                                        scheduleIds: schedules.map(s => s.id),
                                        status: { $in: [0, 1] },
                                    }),
                                    booking,
                                },
                            },
                        )
                        .exec();
                }
            } catch (err) {
                console.error(`${jobName} err`, err);
            } finally {
                await this.bookingFullSlotModel.updateOne({ _id: data._id }, { deleted: true, 'tracking.skipNoti': skipNoti }).exec();
                this.scheduler.deleteCronJob(jobName);
            }
        });

        try {
            this.scheduler.addCronJob(jobName, job);
            this.logger.log(`CronJob booking full slot: ${jobName} is registered`);
            job.start();
        } catch (error) {
            return;
        }
    }

    async registerExistBookingFullSlotItem(id?: string) {
        try {
            const condition = id ? { _id: id } : { deleted: { $ne: true } }
            const data = await this.bookingFullSlotModel.find(condition).exec();

            for (const item of data) {
                await this.addCronJobBookingFullSlot(item.toObject());
            }
        } catch (err) {
            console.error('err registerExistBookingFullSlotItem', err);
        }
    }

    async listenBookingDateChanged(body: any) {
        const { bookingId } = body;
        const booking = await this.bookingModel.findOne({ id: bookingId }).populate('doctor').exec();

        if (!booking) {
            throw new HttpException('Không tìm thấy booking', 404);
        }

        const { userId, appId, partnerId, transactionId, date, doctor, bookingCode } = booking.toObject();
        let { content, title } = await this.globalSettingService.findByKeyAndRepoNameJSON('NOTIF_BOOKING_DATE_CHANGED')

        content = content
            .replace('{date}', moment(date).utc().add(7, 'hours').format('DD/MM/YYYY'))
            .replace('{time}', moment(date).utc().add(7, 'hours').format('HH:mm'))
            .replace('{bookingCode}', bookingCode)
            .replace('{doctor}', doctor?.name);

        const topicId = 'bookings.date-changed'

        this.eventEmmiter.emit(MESSAGE_EVENT, {
            topic: topicId,
            userId: userId,
            title: content || title,
            type: 1,
            partnerId,
            appId,
            eventData: {
                content,
                transactionId,
                type: 1,
            },
            isPushNotif: true,
            isSendMail: false,
        })

        await this.createEvent(
            {
                topicId,
                userId: userId,
                title: title,
                type: 1,
                partnerId,
                appId,
                eventData: {
                    content,
                    transactionId,
                    type: 1,
                },
                createTime: moment().toISOString(),
            },
            true,
            false,
            false
        )

        return { isOk: true };
    }

    async testPushNotifRatingApp(body: any) {
        const { phones = [] } = body;

        let usernames = phones.map(p => p.replace(/^(84|0)/, '+84'));
        usernames = Array.from(new Set(usernames));

        return this.pushNotifRatingApp({
            usernames
        })
    }

    async getUserAndPushNotifRatingApp() {
        const LIMIT = 500;

        const ratingAppUsers = await this.ratingAppUserModel.find({
            status: 0,
        }).limit(LIMIT);

        if (ratingAppUsers.length == 0) {
            return;
        }

        const usernames = ratingAppUsers.map(item => item.username.replace(/^(84|0)/, '+84'));


        try {
            await this.pushNotifRatingApp({
                usernames,
            });

            await this.ratingAppUserModel.updateMany({
                _id: { $in: ratingAppUsers.map(i => i._id) },
            }, {
                status: 1,
            });
        } catch (err) {
            console.log('err exec pushNotifRatingApp', err);
            return;
        }

        if (ratingAppUsers.length === LIMIT) {
            setTimeout(() => this.getUserAndPushNotifRatingApp(), 100);
        }
    }

    async pushNotifRatingApp(body: any) {
        const appId = 'medpro';
        const { usernames = [] } = body;

        const users = await this.userModel.find({
            username: { $in: usernames }
        }, { username: true }).exec();

        const userIds = users.map(u => u._id);

        const [cfNotif, partnerConfig] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoNameJSON('CONFIG_NOTIF_RATING_APP'),
            this.partnerConfigModel.findOne({ partnerId: appId }, { notifAppId: true, notifApiKey: true }).exec(),
        ]);

        const pushDevices = await this.pushDeviceModel.find({ appId, userId: { $in: userIds } }).lean();
        const devicesGroup = groupBy(pushDevices, 'platform')
        const deviceIos = devicesGroup['ios'] || [];
        const deviceAndroid = devicesGroup['android'] || [];

        const clientIdIos = deviceIos.map(d => d.clientId);
        const clientIdAndroid = deviceAndroid.map(d => d.clientId);

        const info = {
            functionName: 'pushNotifRatingApp',
        };

        const client = new OneSignal.Client(partnerConfig.notifAppId, partnerConfig.notifApiKey, { apiRoot: 'https://onesignal.com/api/v1' });

        try {
            const notifIos = {
                contents: {
                    en: cfNotif.contentIos,
                    vi: cfNotif.contentIos,
                },
                headings: {
                    en: cfNotif.titleIos,
                    vi: cfNotif.titleIos,
                },
                data: {
                    ...info,
                    content: cfNotif.contentIos,
                    type: 104,
                },
                include_player_ids: clientIdIos,
            };

            if (clientIdIos.length > 0) {
                const response = await client.createNotification({
                    ...notifIos,
                });
                console.log('response OSN ios', JSON.stringify(response.body, null, 2));
            }
        } catch (err) {
            console.log('err push notif ios', err);
        }

        try {
            const notifIos = {
                contents: {
                    en: cfNotif.contentAndroid,
                    vi: cfNotif.contentAndroid,
                },
                headings: {
                    en: cfNotif.titleAndroid,
                    vi: cfNotif.titleAndroid,
                },
                data: {
                    ...info,
                    content: cfNotif.contentAndroid,
                    type: 104,
                },
                include_player_ids: clientIdAndroid,
            };

            if (clientIdAndroid.length > 0) {
                const response = await client.createNotification({
                    ...notifIos,
                });
                console.log('response OSN android', JSON.stringify(response.body, null, 2));
            }
        } catch (err) {
            console.log('err push notif android', err);
        }

        const topicId = 'app.rating'
        const events = userIds.map(userId => {
            return {
                id: uuid.v4().replace(/-/g, ''),
                topicId,
                userId: userId,
                title: cfNotif.title,
                type: 104,
                partnerId: 'medpro',
                appId,
                eventData: {
                    content: cfNotif.content,
                    type: 104,
                },
                isNotif: true,
                isPushNotif: false,
                isSendMail: false,
                createTime: new Date(),
            };
        })

        await this.eventModel.insertMany(events);
        this.cacheService.delByPattern(`^event:events.*`);

        return {
            isOk: true,
        }
    }

    async setCacheApi() {
        const baseUrl = this.urlConfigService.getUrlApi122

        const versionListConfig = await this.globalSettingService.findByKeyAndRepoName('APP_VERSION_LIST_RESET_CACHE_DS_V3')
        const versionList = versionListConfig.split(',');

        for await (const version of versionList) {
            this.http.get(`${baseUrl}/mongo/hospital/danh-sach-v3-set-cache`,{
                headers: {
                    appid: 'medpro',
                    locale: 'vi',
                    platform: 'android',
                    version
                },
            }).toPromise();
    
            this.http.get(`${baseUrl}/mongo/hospital/danh-sach-v3-set-cache`,{
                headers: {
                    appid: 'medpro',
                    locale: 'vi',
                    platform: 'ios',
                    version
                },
            }).toPromise();
        }

        this.http.get(`${baseUrl}/app-id/feature-in-app-set-cache`,{
            headers: {
                appid: 'medpro',
                locale: 'vi',
                platform: 'pc',
            }
        }).toPromise();

        this.http.get(`${baseUrl}/app-id/feature-in-app-set-cache`,{
            headers: {
                appid: 'medpro',
                locale: 'vi',
                platform: 'web',
            }
        }).toPromise();

        this.http.get(`${baseUrl}/traffic/set-cache/medpro`).toPromise();
    }

    async fixZnsBooking() {
        return true;
        const rows = await this.zndLogModel.find({
            _id: { $lte: '664c543025a5130019bc518a' },
        }).skip(100).lean();

        rows.forEach(async (row) => {
            const bookingCode = get(row, 'params.ma_phieu');
            if (!bookingCode) {
                return;
            }

            const booking = await this.bookingModel.findOne({
                $or: [
                    { bookingCode },
                    { bookingCodeV1: bookingCode },
                ],
                partnerId: 'umc',
            }).exec();

            if (booking.status !== 1) {
                return;
            }

            if (moment(booking.date).isBefore(moment('2024-05-21T17:00:00.000Z'), 'day')) {
                return;
            }

            await UtilService.delay(200);

            await this.pushZnsBookingSuccess({ id: `${booking._id}`});
            await this.fixZndLogModel.create({
                ...row,
                bookingId: `${booking._id}`,
                bookingDate: moment(booking.date).add(7, 'hours').format('HH:mm DD/MM/YYYY'),
                logId: `${row._id}`,
            })
        })
    }

    async getMedproCareTransaction(partnerId: string): Promise<any> {
        try {
            let medproCare: any
            const url = `${this.urlConfigService.getUrlApiV2}/his-gateway/message-event/medpro-care/${partnerId}`;
            const { data } = await this.http.get(url,{
                headers: {
                    partnerid: partnerId,
                    appid: 'medpro',
                },
            }).toPromise();
            const medproCareServices = data?.addonServices?.slice(0,1)
            medproCare = { ...data, addonServices: medproCareServices, status: 0, partner2bill: false }
            const medproCarePrice = reduce(
                medproCare?.addonServices,
                (sum: number, item: any) => {
                    return sum + item.price;
                },
                0,
            )
            return { medproCare, medproCarePrice }
        } catch (error) {
            this.logger.error(`Error when exec getMedproCarePrice. Cause: ${error.message}`);
            throw error;
        }
    }

    // GỬI THÔNG BÁO LARK GROUP KHI NGƯỜI DÙNG CÓ THAO TÁC CHỌN MEDPRO CARE 
    public async sendLarkNotiOnTrackingMedproCare (payload: MedproCareTrackingDTO) {
        try {
            const { userId, patientId, partnerId } = payload;

            const isValid = mongoose.Types.ObjectId.isValid(patientId);
            if (!isValid) { 
                throw new HttpException('Invalid patientId', HttpStatus.BAD_REQUEST); 
            }
            
            this.eventEmmiter.emit(PUSH_NOTIF_TRACKING_MEDPRO_CARE, {
                userId,
                patientId,
                partnerId
            });
            
            return {
                isOK: true
            }

        } catch (error) {
            console.log(error)
            return {
                isOK: false,
                error: error?.message || 'ERROR_SEND_LARK_NOTIF'
            }
        }
    }
    
    // GỬI THÔNG BÁO LARK GROUP KHI NGƯỜI DÙNG THANH TOÁN PHIẾU KHÁM GẶP LỖI
    public async sendLarkNotifPaymentError (payload: any) {
        try {
            this.eventEmmiter.emit(LARK_NOTIF_PAYMENT_ERROR, payload);
            
            return {
                isOK: true
            }

        } catch (error) {
            console.log(error)
            return {
                isOK: false,
                error: error?.message || 'ERROR_SEND_LARK_NOTIF'
            }
        }
    }
    
    // GỬI THÔNG BÁO LARK GROUP KHI SYNC BOOKING GẶP LỖI
    public async larkNotifSyncBookingFail (payload: any) {
        try {
            this.eventEmmiter.emit(LARK_NOTIF_SYNC_BOOKING_FAIL, payload);
            
            return {
                isOK: true
            }
        } catch (error) {
            console.log(error)
            return {
                isOK: false,
                error: error?.message || 'ERROR_SEND_LARK_NOTIF'
            }
        }
    }

    // GỬI THÔNG BÁO LARK GROUP KHI CALLBACK TYPE 7, 8 PAYMENT CARE247 THANH CONG
    public async larkNotifCare247AfterSuccess (payload: any) {
        try {
            this.eventEmmiter.emit(LARK_NOTIF_CARE247_AFTER_SUCCESS, payload);
            
            return {
                isOK: true
            }
        } catch (error) {
            console.log(error)
            return {
                isOK: false,
                error: error?.message || 'ERROR_SEND_LARK_NOTIF'
            }
        }
    }
    
    public async sendSMSCare247(payload: any) {
        try {
            const { locale, service, mobile, user, booking, bookingCare247 } = payload
            const messageTemplate = await this.globalSettingService.findByKeyAndRepoName('SMS_TEMPLATE_CARE247');
            const messageConfig = JSON.parse(messageTemplate);
            const serviceName = slugify.default(service, { replacement: ' ', lower: true, locale: 'vi' })
            let message: string
            switch (locale) {
                case 'vi':
                    message = messageConfig.vi.replace('[Care247Service]', serviceName)
                    break;
                case 'en':
                    message = messageConfig.en.replace('[Care247Service]', serviceName)
                    break;
                case 'km':
                    message = messageConfig.km.replace('[Care247Service]', serviceName)
                    break;
                default:
                    message = messageConfig.vi.replace('[Care247Service]', serviceName)
                    break;
            }
            try {
                await this.smsService.sendCare247SMS({message, mobile})
                const bookingCare247Sms = new this.bookingCare247SmsModel({
                    locale,
                    template: message,
                    username: mobile,
                    user,
                    booking,
                    bookingCare247,
                    status: 1
                });
                await bookingCare247Sms.save();
            } catch (error) {
                const bookingCare247Sms = new this.bookingCare247SmsModel({
                    locale,
                    template: message,
                    username: mobile,
                    user,
                    booking,
                    bookingCare247,
                    status: 0
                });
                await bookingCare247Sms.save();
            }
        } catch (error) {
            console.log(error)
        }
    }

    async notifyViewExamResultChoray(formData: any) {
        // const partnerConfig = await this.partnerConfigModel.findOne({ partnerId: 'choray' }, {
        //     partnerId: true,
        //     noticeMinutesViewResultAfteExamTime: true,
        //     checkBookingStatusToNoticeViewResult: true,
        //     noticeViewResultContent: true
        // })

        const { startDate, endDate } = formData

        const startD = startDate ? startDate : '2024-01-01'
        const endD = endDate ? endDate : '2024-10-10'

        const fromDate = moment(startD).set({
            hours: 0,
            minutes: 0,
            seconds: 0,
        });

        const toDate = moment(endD).set({
            hours: 23,
            minutes: 59,
            seconds: 59,
        });

        // const hospital = await this.hospitalModel.findOne({ partnerId: 'choray' }, { name: true })

        const bookings = await this.bookingModel.find({
            partnerId: 'choray',
            appId: 'medpro',
            status: {
                $in: [1, 2]
            },
            platform: {
                $in: ['android', 'ios']
            },
            date: { $gte: fromDate, $lt: toDate },
        }, { date: true, bookingCode: true, userId: true, _id: true, locale: true, appId: true, partnerId: true, platform: true }).exec();

        const groupByUser = groupBy(bookings, 'userId');
        const data = map(groupByUser, (records) => maxBy(records, (record) => moment(record.date).valueOf()));

        const userBookings = data.map((item) => {
            return { ...item.toObject(), user: item.userId, booking: item._id }
        })

        const chunkData = chunk(userBookings, 1000);
        for await (const chunkDetail of chunkData) {
            await this.userBookingChorayPushNotifModel.insertMany(chunkDetail);
        }

        return {
            isOK: true,
        };
    }

    async seedDataCare247ConstraintUser() {
        try {
            const care247List = await this.bookingCare247Model.find({ status: 1 }).exec();
            for await (const bookingCare247 of care247List) {
                const bookingCare247Obj = bookingCare247.toObject();
                try {
                    const bookingCare247Constraint = new this.bookingCare247ConstraintUserModel({
                        userId: bookingCare247Obj.userId,
                        user: bookingCare247.userId,
                        transactionIds: [bookingCare247Obj.transactionId],
                        care247: [bookingCare247Obj._id],
                        latestPartner: bookingCare247Obj.partner,
                        latestPartnerTime: bookingCare247Obj.date
                    });
                    await bookingCare247Constraint.save();
                } catch (error) {
                    console.log('error', error);
                    try {
                        const existConstraint = await this.bookingCare247ConstraintUserModel
                            .findOne({ userId: bookingCare247Obj.userId })
                            .populate({ path: 'care247', select: { date: true, partner: true } })
                            .read('primary')
                            .exec();
                        const existConstraintrObj = existConstraint.toObject();
                        const care247List = [...existConstraintrObj.care247, { date: bookingCare247Obj.date, partner: bookingCare247Obj.partner }];
                        const bookingDates = care247List.map(c => c.date);
                        const latestBookingDate = this.utilService.getLatestDate(bookingDates)
                        const groupCare247ByDatePartner = reduce(care247List, (acc, item) => {
                            acc[item.date] = item.partner;
                            return acc;
                        }, {});
                        const transactions = [...existConstraint.transactionIds, bookingCare247Obj.transactionId];
                        await this.bookingCare247ConstraintUserModel.findByIdAndUpdate(
                            { _id: existConstraint._id },
                            {
                                transactionIds: [...new Set(transactions)],
                                times: transactions.length,
                                care247: [...new Set([...existConstraint.care247, bookingCare247Obj._id])],
                                multiple: 2,
                                latestPartner: groupCare247ByDatePartner[latestBookingDate],
                                latestPartnerTime: latestBookingDate
                            },
                        ).read('primary').exec();
                    } catch (error) {
                        console.log('error catch', error);
                    }
                }
            }
            return {
                isOk: true,
            };
        } catch (error) {
            console.log('error', error);
        }
    }

    async updateNotifTckqChoray(data: any) {
        try {
            const trackingPushNotif = await this.trackingPushNotifTCKQ
                .find({ userId: data.userId })
                .limit(1)
                .lean();
            const notif = first(trackingPushNotif);
            if (notif) {
                await this.trackingPushNotifTCKQ.findByIdAndUpdate({ _id: notif._id }, { examResults: { ...data } }).exec();
            }
        } catch (error) {
            this.logger.error(error);
        }
    }

    async createKPICSKH(body: any): Promise<any> {
        try {
            const { bookingId } = body
            const booking = await this.bookingModel.findById(bookingId).read('primary').exec();
            const bookingObj = booking.toObject();

            //update booking
            let countDoanhSo = 1;
            try {
                if (bookingObj.partnerId === 'binhthanhhcm') {
                    const payment = await this.paymentModel.findOne({ transactionId: bookingObj.transactionId }).read('primary').exec();
                    if (payment?.paymentMethod === 'THANH_TOAN_TAI_CO_SO') {
                        countDoanhSo = 0
                    }
                }

                if (bookingObj.partnerId === 'bvmathcm') {
                    try {
                        const createDate = moment(bookingObj.createdAt).utc().format('YYYY-MM-DD');
                        const timeStart = moment(createDate, 'YYYY-MM-DD').set({
                            hours: 0,
                            minutes: 0,
                            seconds: 0,
                        }).toDate();
                        const timeEnd = moment(createDate, 'YYYY-MM-DD').set({
                            hours: 23,
                            minutes: 59,
                            seconds: 59,
                        }).toDate();
                        const condition = {
                            cskhUserId: bookingObj.cskhUserId,
                            partnerId: bookingObj.partnerId,
                            fromDate: { $gte: timeStart, $lte: timeEnd }
                        }
                        const schedulers = await this.cskhSchedulersModel.find(condition).exec();
                        for await (const schedule of schedulers) {
                            const compareTime = moment(bookingObj.createdAt).utc().add(7, 'hours');
                            if (moment(compareTime).isAfter(moment(schedule.fromDate))
                                && moment(compareTime).isBefore(moment(schedule.toDate))
                            ) {
                                countDoanhSo = 0
                            }
                        }
                    } catch (error) {
                    }
                }
    
                let updateBooking:any = {
                    user: bookingObj.userId,
                    countDoanhSo
                };
    
                if(bookingObj?.cskhUserId || ''){
                    updateBooking = {
                        ...updateBooking,
                        cskh: bookingObj.cskhUserId,
                    }
                }
    
                await this.bookingModel.findByIdAndUpdate(
                    { _id: bookingObj._id },
                    { ...updateBooking },
                    { new: true })
                    .read('primary')
                    .exec();
            } catch (error) {
                console.log('error', error);
            }

            let mode: string;
            switch (bookingObj.treeId) {
                case 'DATE':
                case 'DOCTOR':
                case 'CSKH':
                    mode = 'booking';
                    break;
                case 'PACKAGE':
                    mode = 'package';
                    break;
                case 'TELEMED':
                case 'TELEMEDNOW':
                    mode = 'telemed';
                    break;
                default:
                    break;
            }
            if (bookingObj?.cskhUserId) {
                const kpiCskh = new this.kpiCskhModel({
                    bookingId: bookingObj._id,
                    booking: bookingObj._id,
                    createdDate: bookingObj.createdAt,
                    partnerId: bookingObj.partnerId,
                    partner: bookingObj.partner,
                    patient: bookingObj.patient,
                    appId: bookingObj.appId,
                    userId: bookingObj.userId,
                    user: bookingObj.userId,
                    cskhUserId: bookingObj.cskhUserId,
                    cskh: bookingObj.cskhUserId,
                    date: bookingObj.date,
                    status: bookingObj.status,
                    paymentStatus: bookingObj.paymentStatus,
                    cancelledBy: bookingObj.cancelledBy,
                    treeId: bookingObj.treeId,
                    platform: bookingObj.platform,
                    subject: bookingObj.subject,
                    room: bookingObj.room,
                    section: bookingObj.section,
                    doctor: bookingObj.doctor,
                    service: bookingObj.service,
                    countDoanhSo: countDoanhSo,
                    mode
                });
                await kpiCskh.save();
            }
        } catch (error) {
            console.log('error createKPICSKH', error);
        }
    }

    public async larkNotifPreventForeigner(payload: any) {
        try {
            const { partnerId, userId, patientId } = payload;
            const [hospital, userById, patient] = await Promise.all([
                this.hospitalModel.findOne({ partnerId }, { name: true }).exec(),
                this.userModel.findById({ _id: userId }).exec(),
                this.patientModel.findById({ _id: patientId })
                    .populate('country')
                    .populate('city')
                    .populate('district')
                    .populate('ward')
                    .exec(),
            ]);
            const patientObj = patient.toObject();
            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            const now = moment().utc().add(7, 'hours').format('HH:mm DD-MM-YYYY');
            const tagnameTop = `Hồ sơ nước ngoài bị chặn đặt khám ${hospital.name}. - ${now}`
            
            const objLarkGroup = {
                larkEnv: larkEnv,
                messageRemiderNotPamentTop: tagnameTop,
                user: `${userById.username} - ${userById.fullname}`,
                patient: `${patientObj.surname} ${patientObj.name} (${patientObj.mobile})`,
                country: patientObj.country.name,
                partner: hospital.name
            };
            const msgContent = larkMsgPartnerPreventForeignerTemplate(objLarkGroup)

            this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                webHub: this.urlConfigService.getLarkNotiPartnerPreventForeignerUrl,
                data: {
                    msg_type: 'interactive',
                    card: msgContent,
                }
            });

            return {
                isOK: true
            }
        } catch (error) {
            console.log(error)
            return {
                isOK: false,
                error: error?.message || 'ERROR_SEND_LARK_NOTIF'
            }
        }
    }

    // GỬI THÔNG BÁO LARK GROUP KHI NGƯỜI DÙNG LIÊN HỆ HỢP TÁC VỚI MEDPRO
    public async larkCooperateMedpro (payload: any) {
        try {
            this.eventEmmiter.emit(LARK_NOTIF_COOPERATE_MEDPRO, payload);
            
            return {
                isOK: true
            }

        } catch (error) {
            console.log(error)
            return {
                isOK: false,
                error: error?.message || 'LARK_NOTIF_COOPERATE_MEDPRO'
            }
        }
    }

    public async larkNotifEnterpriseRegistration(payload: any) {
        try {
            this.eventEmmiter.emit(LARK_NOTIF_ENTERPRISE_REGISTRATION, payload);

            return {
                isOK: true,
            };
        } catch (error) {
            console.log(error);
            return {
                isOK: false,
                error: error?.message || 'LARK_NOTIF_ENTERPRISE_REGISTRATION',
            };
        }
    }

    public async larkNotifRecruitment(payload: any) {
        try {
            this.eventEmmiter.emit(LARK_NOTIF_RECRUITMENT, payload);

            return {
                isOK: true,
            };
        } catch (error) {
            console.log(error);
            return {
                isOK: false,
                error: error?.message || 'LARK_NOTIF_RECRUITMENT',
            };
        }
    }

    public async larkNotifRetrySyncBooking(payload: any) {
        try {
            const { transactionId, userId, userActionId } = payload;
            const [booking, userById, userActionById] = await Promise.all([
                this.bookingModel.findOne({ transactionId })
                    .populate({ path: 'patient', select: { name: true, surname: true, mobile: true } })
                    .populate({ path: 'partner', select: { name: true } })
                    .exec(),
                this.userModel.findById({ _id: userId }).exec(),
                this.userModel.findById({ _id: userActionId }).exec(),
            ]);
            const bookingObj = booking.toObject();

            let status: string;
            switch (bookingObj.status) {
                case -2:
                    status = 'Đã hủy';
                    break;
                case 0:
                    status = 'Chưa thanh toán';
                    break;
                case 1:
                    status = 'Đã thanh toán';
                    break;
                case 6:
                    status = 'Thanh toán hộ';
                    break;
                case 2:
                    status = 'Đã khám';
                    break;
            }

            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            const now = moment().utc().add(7, 'hours').format('HH:mm DD-MM-YYYY');
            const tagnameTop = `Hệ thống ghi nhận thao tác đồng bộ lại phiếu khám. - ${now}`
            
            const objLarkGroup = {
                larkEnv: larkEnv,
                messageRemiderNotPamentTop: tagnameTop,
                userAction: `${userActionById.username} - ${userActionById.fullname}`,
                bookingCode: bookingObj.bookingCode,
                status,
                medproId: `${userById.username} - ${userById.fullname}`,
                patient: `${bookingObj.patient.surname} ${bookingObj.patient.name} (${bookingObj.patient.mobile})`,
                date: moment(bookingObj.date).utc().add(7, 'hours').format('HH:mm DD-MM-YYYY'),
                partner: bookingObj.partner.name || ''
            };
            const msgContent = larkMsgRetrySyncBookingTemplate(objLarkGroup)

            this.eventEmmiter.emit(PUSH_NOTIF_LARK, {
                webHub: this.urlConfigService.getLarkNotiRetrySyncBooking,
                data: {
                    msg_type: 'interactive',
                    card: msgContent,
                }
            });

            return {
                isOK: true
            }
        } catch (error) {
            console.log(error)
            return {
                isOK: false,
                error: error?.message || 'ERROR_SEND_LARK_NOTIF'
            }
        }
    }
}

