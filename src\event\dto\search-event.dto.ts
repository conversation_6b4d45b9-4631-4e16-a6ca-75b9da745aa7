import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

export class SearchEventByUserPhoneDTO {
  @ApiProperty({ description: 'userPhone', required: true, type: String })
  @IsNotEmpty({ message: 'userPhone is required' })
  @Transform(value => `${value}`.trim())
  userPhone: string;

  @ApiProperty({ description: 'partnerId', required: false, type: String, default: null })
  partnerId?: string;

  @ApiProperty({ description: 'pageIndex', required: true, type: Number })
  @IsNotEmpty({ message: 'pageIndex is required' })
  @Transform(value => Number(value))
  pageIndex: number;

  @ApiProperty({ description: 'pageSize', required: false, type: Number, default: 10 })
  @Transform(value => Number(value))
  pageSize: number;

  @ApiProperty({ description: 'topicId', required: false, type: String, default: '' })
  topicId?: string;

  @ApiProperty({ description: 'type', required: false, type: Number, default: null })
  type?: number;
}
