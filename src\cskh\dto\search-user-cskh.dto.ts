import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { Pagination } from 'src/common/base/pagination';

export class SearchUserCskh extends Pagination {
    @ApiProperty({ description: 'Tìm kiếm theo tên' })
    @Transform(value =>
        `${value}`
            .trim()
            .replace(/^[+]84|^0/, '84')
            .replace(/^84/, '84')
            .replace(/^9/, '849')
            .replace(/^3/, '843'),
    )
    phone?: string;
}
