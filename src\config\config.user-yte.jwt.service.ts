import { JwtOptionsFactory, JwtModuleOptions } from '@nestjs/jwt';
import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';

@Injectable()
export class JwtUserYTeConfigService extends ConfigManager implements JwtOptionsFactory {

    provideConfigSpec() {
        return {
            USER_YTE_JWT_SECRET: {
                validate: Joi.string(),
                required: true,
            },
            USER_YTE_JWT_EXPIRES_IN: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    createJwtOptions(): JwtModuleOptions {
        return {
            secret: this.get<string>('USER_YTE_JWT_SECRET'),
            signOptions: { expiresIn: this.get<string>('USER_YTE_JWT_EXPIRES_IN') },
        };
    }
}
