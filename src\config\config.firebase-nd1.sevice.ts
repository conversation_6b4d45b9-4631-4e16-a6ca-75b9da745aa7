import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Joi from 'joi';
import * as admin3 from 'firebase-admin';
import { FirebaseOptions } from './firebaseConnectionSecond';

@Injectable()
export class FirebaseNd1ConfigService extends ConfigManager {
    provideConfigSpec() {
        return {
            FIREBASE_PROJECT_ID_NHI_DONG_1: {
                validate: Joi.string(),
                required: true,
            },
            FIREBASE_PRIVATE_KEY_NHI_DONG_1: {
                validate: Joi.string(),
                required: true,
            },
            FIREBASE_CLIENT_EMAIL_NHI_DONG_1: {
                validate: Joi.string(),
                required: true,
            },
            FIREBASE_DATABASE_URL_NHI_DONG_1: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    createServiceAccount(): FirebaseOptions {
        return {
            credential: admin3.credential.cert({
                projectId: this.get<string>('FIREBASE_PROJECT_ID_NHI_DONG_1'),
                privateKey: this.get<string>('FIREBASE_PRIVATE_KEY_NHI_DONG_1').replace(/\\n/g, '\n'), /* _______________ */
                clientEmail: this.get<string>('FIREBASE_CLIENT_EMAIL_NHI_DONG_1'),
            }),
            databaseURL: this.get<string>('FIREBASE_DATABASE_URL_NHI_DONG_1'),
        };
    }
}
