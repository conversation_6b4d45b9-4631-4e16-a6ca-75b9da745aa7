import { Global, HttpModule, Module } from '@nestjs/common';
import { PkhConfigService } from './config.pkh.service';
import { PatientConfigService } from './config.patient.service';
import { ThuDucHospitalConfigService } from './config.thuduc.hospital.service';
import { ChoRayHospitalConfigService } from './config.choray.hospital.service';
import { ConfigManagerModule } from '@nestjsplus/config';
import { ZaloConfigService } from './config.zalo.service';
import { ZaloDaLieuConfigService } from './config.zalo-dalieu.service';
import { ZaloNd1ConfigService } from './config.zalo-nd1.service';
import { ZaloUMCConfigService } from './config.zalo-umc.service';
import { JwtAdminConfigService } from './config.admin.jwt.service';
import { JwtUserConfigService } from './config.user.jwt.service';
import { FirebaseConfigService } from './config.firebase.sevice';
import { FirebaseSecondConfigService } from './config.firebase-second.sevice';
import { FirebaseDaLieuConfigService } from './config.firebase-dalieu.sevice';
import { FirebaseUMCConfigService } from './config.firebase-umc.sevice';
import { FirebaseNd1ConfigService } from './config.firebase-nd1.sevice';
import { SendgridConfigService } from './config.sendgrid.sevice';
import { UrlConfigService } from './config.url.service';
import { PaymentMethodRateConfigService } from './config.paymentMethodRate.service';
import { FeeConfigService } from './config.fee.service';
import { PrefixTransactionCodeTTConfigService } from './config.prefix.transcode.service';
import { MomoDKKBConfigService } from './config.momo.dkkb.service';
import { MomoVPConfigService } from './config.momo.vp.service copy';
import { OneSignalTeleMedicineConfigService } from './config.onesignal.telemedicine.service';
import { UMCPartnerConfigService } from './config.umc.partner.service';
import { UtilService } from './util.service';
import { ConfigMongoService } from './config.mongo.service';
import { RestfulAPIOldHospitalConfigService } from './config.rest-api.old.hospital';
import { PkhHttpService } from './config.http.service';
import { ConfigSentryService } from './config.sentry.service';
import { JwtUserYTeConfigService } from './config.user-yte.jwt.service';
import { ConfigSMSMedproService } from './config.sms.medpro.service';
import { ConfigGridFSMullterService } from './config.gridfs.multer.service';
import { ConfigCacheManagerService } from './config.cache-manager.service';
import { ConfigRepoService } from './config.repo.service';
import { ConfigMessageService } from './config.message.service';
import { LogService } from './log.service';
import { ConfigLarkService } from './config.lark.service';
import { LarkService } from './lark.service';
import { MailgunConfigService } from './config.mailgun.sevice';

@Global()
@Module({
    imports: [
        ConfigManagerModule.register({
            useEnv: {
                folder: 'config',
            },
            allowExtras: true,
        }),
        HttpModule,
    ],
    providers: [
        PkhConfigService,
        PatientConfigService,
        ThuDucHospitalConfigService,
        ChoRayHospitalConfigService,
        ZaloConfigService,
        ZaloDaLieuConfigService,
        ZaloNd1ConfigService,
        ZaloUMCConfigService,
        JwtAdminConfigService,
        JwtUserConfigService,
        FirebaseConfigService,
        FirebaseSecondConfigService,
        FirebaseDaLieuConfigService,
        FirebaseUMCConfigService,
        FirebaseNd1ConfigService,
        SendgridConfigService,
        MailgunConfigService,
        UrlConfigService,
        PaymentMethodRateConfigService,
        FeeConfigService,
        PrefixTransactionCodeTTConfigService,
        MomoDKKBConfigService,
        MomoVPConfigService,
        OneSignalTeleMedicineConfigService,
        UMCPartnerConfigService,
        UtilService,
        ConfigMongoService,
        RestfulAPIOldHospitalConfigService,
        PkhHttpService,
        ConfigSentryService,
        JwtUserYTeConfigService,
        ConfigSMSMedproService,
        ConfigGridFSMullterService,
        ConfigCacheManagerService,
        ConfigRepoService,
        ConfigMessageService,
        LogService,
        ConfigLarkService,
        LarkService,
    ],
    exports: [
        PkhConfigService,
        PatientConfigService,
        ThuDucHospitalConfigService,
        ChoRayHospitalConfigService,
        ZaloConfigService,
        ZaloDaLieuConfigService,
        ZaloNd1ConfigService,
        ZaloUMCConfigService,
        JwtAdminConfigService,
        JwtUserConfigService,
        FirebaseConfigService,
        FirebaseSecondConfigService,
        FirebaseDaLieuConfigService,
        FirebaseUMCConfigService,
        FirebaseNd1ConfigService,
        SendgridConfigService,
        MailgunConfigService,
        UrlConfigService,
        PaymentMethodRateConfigService,
        FeeConfigService,
        PrefixTransactionCodeTTConfigService,
        MomoDKKBConfigService,
        MomoVPConfigService,
        OneSignalTeleMedicineConfigService,
        UMCPartnerConfigService,
        UtilService,
        ConfigMongoService,
        RestfulAPIOldHospitalConfigService,
        PkhHttpService,
        ConfigSentryService,
        JwtUserYTeConfigService,
        ConfigSMSMedproService,
        ConfigGridFSMullterService,
        ConfigCacheManagerService,
        ConfigRepoService,
        ConfigMessageService,
        LogService,
        ConfigLarkService,
        LarkService,
    ],
})
export class ConfigModule {}
