import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';
import { KnexOptions } from './pkhConnection/index';

@Injectable()
export class PkhConfigService extends ConfigManager {
    provideConfigSpec() {
        return {
            PKH_ADMIN_HOST: {
                validate: Joi.string(),
                required: false,
                default: 'localhost',
            },
            PKH_ADMIN_PORT: {
                validate: Joi.number(),
                required: false,
                default: 5432,
            },
            PKH_ADMIN_USER: {
                validate: Joi.string(),
                required: true,
            },
            PKH_ADMIN_DATABASE: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    createKnexOptions(): KnexOptions {
        return {
            client: 'mysql',
            debug: false,
            connection: {
                host: this.get<string>('PKH_ADMIN_HOST'),
                user: this.get<string>('PKH_ADMIN_USER'),
                password: this.get<string>('PKH_ADMIN_PASSWORD'),
                database: this.get<string>('PKH_ADMIN_DATABASE'),
                port: this.get<number>('PKH_ADMIN_PORT'),
            },
        };
    }
}
