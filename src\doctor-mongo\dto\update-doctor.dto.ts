
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Exclude } from 'class-transformer';
import { MaxLength, ValidateIf, IsNotEmpty } from 'class-validator';
import { CreateDoctorDTO } from './create-doctor.dto';

export class UpdateDoctorDTO extends CreateDoctorDTO {

    @ApiProperty({
        description: 'Id Doctor',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @MaxLength(100, {
        message: 'Không vượt quá 100 ký tự',
    })
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    readonly id: string;

    @Exclude()
    readonly createdAt: string;
}
