import { Injectable, CanActivate, ExecutionContext, HttpException, HttpStatus } from '@nestjs/common';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';

@Injectable()
export class ClinicGuard implements CanActivate {
    private readonly CLINIC_CLIENT_ID: string = 'CLINIC_CLIENT_ID';
    private readonly CLINIC_SECRET_KEY: string = 'CLINIC_SECRET_KEY';
    constructor(private globalSettingService: GlobalSettingService) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const { clientId, secretKey } = request.body;
        try {
            const [clinicClientId, clinicSecretKey] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName(this.CLINIC_CLIENT_ID),
                this.globalSettingService.findByKeyAndRepoName(this.CLINIC_SECRET_KEY),
            ]);
            const validate = clientId === clinicClientId && secretKey === clinicSecretKey;
            if (!validate) {
                throw new HttpException('Thông tin xác thực gửi lên không chính xác. Vui lòng kiểm tra lại!', HttpStatus.FORBIDDEN);
            }
            return true;
        } catch (error) {
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng kiểm tra lại!', HttpStatus.BAD_REQUEST);
        }
    }
}
