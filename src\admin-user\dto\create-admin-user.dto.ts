import { ApiProperty } from '@nestjs/swagger';

export class CreateAdminUserDTO {
    @ApiProperty({
        description: 'Email',
        required: true,
        type: 'string',
    })
    readonly email: string;
    @ApiProperty({
        description: '<PERSON><PERSON>t khẩu đăng nhập',
        required: true,
        type: 'string',
    })
    readonly password: string;
    @ApiProperty({
        description: 'Họ và tên',
        required: true,
        type: 'string',
    })
    readonly fullname: string;
}
