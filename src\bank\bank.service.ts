import { Injectable, Inject } from '@nestjs/common';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';

@Injectable()
export class BankService {
    constructor(
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
    ) { }

    async getBankInfoById(id: number): Promise<any> {
        return await this.pkhPatientKnex('bank')
            .where('id', id)
            .first();
    }
}
