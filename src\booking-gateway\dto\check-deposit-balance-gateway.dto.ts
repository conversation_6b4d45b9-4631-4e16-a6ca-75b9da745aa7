
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

export class CheckDepositBalanceGatewayDTO {

    @ApiProperty({
        description: 'billId',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    readonly billId?: string;

    @ApiProperty({
        description: 'patientId',
        required: false,
        type: 'string',
    })
    @Transform(value => `${value}`.trim())
    readonly patientId?: string;

}
