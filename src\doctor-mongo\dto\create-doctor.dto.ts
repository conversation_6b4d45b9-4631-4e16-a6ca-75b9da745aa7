
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { MaxLength, ValidateIf, IsNotEmpty } from 'class-validator';

export class CreateDoctorDTO {

    @ApiProperty({
        description: 'partner id',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @MaxLength(100, {
        message: 'Không vượt quá 100 ký tự',
    })
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    readonly partnerId: string;

    @ApiProperty({
        description: 'Họ và tên Bác sĩ',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    @MaxLength(100, {
        message: 'Dữ liệu không được vượt quá 100 ký tự.',
    })
    readonly fullname: string;

    @ApiProperty({
        description: 'Tê<PERSON> bác sĩ',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: '<PERSON><PERSON> lòng bổ sung thông tin',
    })
    @MaxLength(50, {
        message: 'Dữ liệu không được vượt quá 50 ký tự.',
    })
    readonly name: string;

    @ApiProperty({
        description: 'Chức vụ',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    @MaxLength(20, {
        message: 'Dữ liệu không được vượt quá 20 ký tự.',
    })
    readonly role: string;

    @ApiProperty({
        description: 'Giới tính true: Nam false: Nữ',
        required: true,
        type: Boolean,
        default: true,
    })
    @Transform(value => Boolean(value))
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    readonly gender: boolean;

    @ApiProperty({
        description: 'Hình đại diện',
        required: false,
        type: String,
    })
    readonly avatar: string;
}
