import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { TRANSACTION_LOG_NAME } from './constants';

const Schema = mongoose.Schema;

export const TransactionLogSchema = new Schema({
    id: { type: String },
    transactionId: { type: String, required: true },
    responseData: { type: Object },
    status: { type: Boolean, default: false },
}, {
    collection: TRANSACTION_LOG_NAME,
    timestamps: true,
}).plugin(jsonMongo);
