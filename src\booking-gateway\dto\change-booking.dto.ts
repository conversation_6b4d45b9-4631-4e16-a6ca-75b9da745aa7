
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, ValidateIf, IsDateString } from 'class-validator';

export class ChangeBookingDTO {

    @ApiProperty({
        description: 'apiKey',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    apiKey: string;

    @ApiProperty({
        description: 'Id booking',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    id: string;

    @ApiProperty({
        description: 'Id Dịch vụ',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    serviceId?: string;

    @ApiProperty({
        description: 'Id chuyên khoa',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    subjectId?: string;

    @ApiProperty({
        description: 'Id phòng khám',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    roomId?: string;

    @ApiProperty({
        description: 'Id Bác sĩ',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    doctorId?: string;

    @ApiProperty({
        description: 'Format kiểu ISO String',
        required: false,
        type: String,
    })
    @IsDateString({ strict: true }, {
        message: 'Thông tin ngày đặt khám. ISOString',
    })
    @ValidateIf(o => o.startTimeString)
    startTimeString?: string;

    @ApiProperty({
        description: 'Format kiểu ISO String',
        required: false,
        type: String,
    })
    @IsDateString({ strict: true }, {
        message: 'Thông tin ngày đặt khám. ISOString',
    })
    @ValidateIf(o => o.startTime)
    startTime?: string;

    @ApiProperty({
        description: 'Format kiểu ISO String',
        required: false,
        type: String,
    })
    @IsDateString({ strict: true }, {
        message: 'Thông tin ngày đặt khám. ISOString',
    })
    @ValidateIf(o => o.endTime)
    endTime?: string;

    @ApiProperty({
        description: 'Booking Slot Id',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    bookingSlotId?: string;

}
