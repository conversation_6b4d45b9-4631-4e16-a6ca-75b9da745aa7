import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';

@Injectable()
export class UMCPartnerConfigService extends ConfigManager {

    provideConfigSpec() {
        return {
            UMC_HOSPITAL_PARTNER_URL: {
                validate: Joi.string(),
                required: true,
            },
            UMC_HOSPITAL_PARTNER_SEARCH_PATIENT_BY_MSBN: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    getBaseUrl(): string {
        return this.get<string>('UMC_HOSPITAL_PARTNER_URL');
    }

    getPatientBySoHoSo(): string {
        const router = this.get<string>('UMC_HOSPITAL_PARTNER_SEARCH_PATIENT_BY_MSBN');
        return `${this.getBaseUrl()}${router}`;
    }

    getPatientByExtraInfo(): string {
        const router = this.get<string>('UMC_HOSPITAL_PARTNER_SEARCH_PATIENT_BY_EXTRA_INFO');
        return `${this.getBaseUrl()}${router}`;
    }

    updatePatientMSBN(): string {
        const router = this.get<string>('UMC_HOSPITAL_PARTNER_UPDATE_MSBN');
        return `${this.getBaseUrl()}${router}`;
    }

    getBookedIn(): string {
        const router = this.get<string>('UMC_HOSPITAL_PARTNER_GET_ALREADY_BOOKED_IN');
        return `${this.getBaseUrl()}${router}`;
    }
}
