import * as mongoose from 'mongoose';
import { APP_ID_COLLECTION_NAME } from './constants';
import * as uuid from 'uuid';
import { HospitalInAppSchema } from './hospital-in-app.schema';
const Schema = mongoose.Schema;
const id = () => {
    const uuidv4 = uuid.v4();
    const idv4 = uuidv4.replace(/-/g, '');
    return idv4;
};
export const AppIdSchema = new Schema(
    {
        id: { type: String, default: id },
        name: { type: String },
        appId: { type: String, unique: true, required: true },
        description: { type: String },
        detail: [HospitalInAppSchema],
    },
    {
        collection: APP_ID_COLLECTION_NAME,
        timestamps: true,
        versionKey: false,
    },
);
