import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';

@Injectable()
export class MomoVPConfigService extends ConfigManager {
    provideConfigSpec() {
        return {
            MOMO_VP_ACCESS_KEY: {
                validate: Joi.string(),
                required: true,
            },
            MOMO_VP_SECRET_KEY: {
                validate: Joi.string(),
                required: true,
            },
            MOMO_VP_RETURN_URL: {
                validate: Joi.string(),
                required: true,
            },
            MOMO_VP_NOTIFY_URL: {
                validate: Joi.string(),
                required: true,
            },
            MOMO_VP_PARTNER_CODE: {
                validate: Joi.string(),
                required: true,
            },
            MOMO_VP_APP_SCHEME: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    getMoMo_VP_Config() {
        return {
            momo_vp_access_key: this.get<string>('MOMO_VP_ACCESS_KEY'),
            momo_vp_secret_key: this.get<string>('MOMO_VP_SECRET_KEY'),
            momo_vp_return_url: this.get<string>('MOMO_VP_RETURN_URL'),
            momo_vp_notify_url: this.get<string>('MOMO_VP_NOTIFY_URL'),
            momo_vp_partner_code: this.get<string>('MOMO_VP_PARTNER_CODE'),
            momo_vp_app_scheme: this.get<string>('MOMO_VP_APP_SCHEME'),
        };
    }

}
