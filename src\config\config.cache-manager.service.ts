import { REDIS, MEMORY } from './../cache-manager/cache-manager.constant';
import { CacheModuleOptions, Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';
import * as redisStore from 'cache-manager-redis-store';

@Injectable()
export class ConfigCacheManagerService extends ConfigManager {
    provideConfigSpec() {
        return {
            CACHE_MANAGER_TYPE: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    getCacheManagerType(): string {
        return this.get<string>('CACHE_MANAGER_TYPE');
    }

    getCacheTtlDefault(): number {
        return this.get<number>('CACHE_TTL_DEFAULT');
    }

    getRedisHost(): string {
        return this.get<string>('REDIS_HOST');
    }

    getRedisPort(): number {
        return this.get<number>('REDIS_PORT');
    }

    getRedisPassword(): string {
        return this.get<string>('REDIS_PASSWORD');
    }

    get createCacheOptions(): any {
        const ttl = this.getCacheTtlDefault();
        switch (this.getCacheManagerType()) {
            case REDIS:
                const host = this.getRedisHost();
                const port = this.getRedisPort();
                return {
                    store: redisStore.create({
                        host,
                        port,
                        auth_pass: this.getRedisPassword()
                    }),

                    // host,
                    // port,
                    ttl,
                    // password: this.getRedisPassword(),
                };
            case MEMORY:
                return {
                    ttl,
                };
        }
    }
}
