import { ValidationArguments, ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';

@ValidatorConstraint()
export class ValidProfileCodeXnc implements ValidatorConstraintInterface {
    validate(text: string, validationArguments: ValidationArguments) {
        const regexCode = new RegExp(/^HCM.{8}$/);
        const regexSpecialChar = new RegExp(/[^a-zA-Z0-9]/);
        return regexCode.test(text) && !regexSpecialChar.test(text);
    }
}
