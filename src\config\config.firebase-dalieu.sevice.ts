import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Joi from 'joi';
import * as admin2 from 'firebase-admin';
import { FirebaseOptions } from './firebaseConnectionSecond';

@Injectable()
export class FirebaseDaLieuConfigService extends ConfigManager {
    provideConfigSpec() {
        return {
            FIREBASE_PROJECT_ID_DA_LIEU: {
                validate: Joi.string(),
                required: true,
            },
            FIREBASE_PRIVATE_KEY_DA_LIEU: {
                validate: Joi.string(),
                required: true,
            },
            FIREBASE_CLIENT_EMAIL_DA_LIEU: {
                validate: Joi.string(),
                required: true,
            },
            FIREBASE_DATABASE_URL_DA_LIEU: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    createServiceAccount(): FirebaseOptions {
        return {
            credential: admin2.credential.cert({
                projectId: this.get<string>('FIREBASE_PROJECT_ID_DA_LIEU'),
                privateKey: this.get<string>('FIREBASE_PRIVATE_KEY_DA_LIEU').replace(/\\n/g, '\n'), /* _______________ */
                clientEmail: this.get<string>('FIREBASE_CLIENT_EMAIL_DA_LIEU'),
            }),
            databaseURL: this.get<string>('FIREBASE_DATABASE_URL_DA_LIEU'),
        };
    }
}
