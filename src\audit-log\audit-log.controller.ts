import { ServiceLogRequest } from './dto/service-log.request.dto';
import { AuditLogService } from './audit-log.service';
import { Controller, Get, Body, Post, HttpStatus, HttpCode, Param, Headers, Logger, HttpException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody, ApiParam } from '@nestjs/swagger';
import { AuditLogRequest } from './dto/audit-log.request';
import { IAuditLog } from './interfaces/audit-log.interface';
import { IServiceLog } from './interfaces/service-log.schema';
import { UrlConfigService } from 'src/config/config.url.service';

@Controller('audit-logs')
@ApiTags('Quản lý log request')
export class AuditLogController {
    private logger = new Logger(AuditLogController.name);
    private appBoId: string;
    private appBoKey: string;
    constructor(private service: AuditLogService, private readonly configService: UrlConfigService) {
        this.appBoId = this.configService.getAPIBoId();
        this.appBoKey = this.configService.getAPIBoKey();
    }

    @Post('/request/danh-sach')
    @HttpCode(200)
    @ApiOperation({ summary: 'Lấy danh sách log request' })
    @ApiBody({
        type: AuditLogRequest,
        required: true,
    })
    async getAuditLogs(@Body() formData: AuditLogRequest): Promise<any> {
        return this.service.getAuditLogs(formData);
    }

    @Get('/request/:id')
    @ApiOperation({ summary: 'Lấy thông tin request theo id' })
    @ApiParam({
        name: 'id',
        type: String,
        required: true,
    })
    async getAuditLogById(@Param('id') id: string): Promise<IAuditLog> {
        return this.service.getAuditLogById(id);
    }

    @Post('/service/danh-sach')
    @HttpCode(200)
    @ApiOperation({ summary: 'Lấy danh sách log service' })
    @ApiBody({
        type: ServiceLogRequest,
        required: true,
    })
    async getServiceLogs(@Body() formData: ServiceLogRequest): Promise<any> {
        return this.service.getServiceLogs(formData);
    }

    @Get('/service/:id')
    @ApiOperation({ summary: 'Lấy thông tin log service theo id' })
    @ApiParam({
        name: 'id',
        type: String,
        required: true,
    })
    async getServiceLogById(@Param('id') id: string): Promise<IServiceLog> {
        return this.service.getServiceLogById(id);
    }

    @Post('service')
    @ApiOperation({ summary: 'Create Service Log' })
    async createServiceLog(@Body() formData: any, @Headers('appboid') appBoId?: string, @Headers('appbokey') appBoKey?: string) {
        const validate = !appBoId ? true : this.appBoId === appBoId && this.appBoKey === appBoKey;
        if (!validate) {
            throw new HttpException(`Xác thực không thành công. Vui lòng kiểm tra lại!`, HttpStatus.UNAUTHORIZED);
        }
        return this.service.createServiceLog(formData);
    }
}
