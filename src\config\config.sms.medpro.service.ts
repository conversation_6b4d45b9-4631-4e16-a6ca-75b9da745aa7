import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';

@Injectable()
export class ConfigSMSMedproService extends ConfigManager {

    provideConfigSpec() {
        return {
            SMS_SOAP_URL: {
                validate: Joi.string(),
                required: true,
            },
            SMS_BRAND_NAME_MEDPRO: {
                validate: Joi.string(),
                required: true,
            },
            SMS_USER: {
                validate: Joi.string(),
                required: true,
            },
            SMS_AUTHEN: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    getSmsMedproConfig() {
        return {
            url: this.get<string>('SMS_SOAP_URL'),
            brandName: this.get<boolean>('SMS_BRAND_NAME_MEDPRO'),
            user: this.get<string>('SMS_USER'),
            pass: this.get<string>('SMS_AUTHEN'),
        };
    }

    getMessageHubUrl(): string {
        return this.get<string>('MESSAGE_HUB_URL');
    }

    getMedproAdminClientId(): string {
        return this.get<string>('MESSAGE_HUB_CLIENT_ID');
    }

    getMedproAdminShareKey(): string {
        return this.get<string>('MESSAGE_HUB_SHARE_KEY');
    }

    isActiveMessageHub(): boolean {
        return this.get<boolean>('ACTIVE_MESSAGE_HUB') || false;
    }

    getMessageServiceProvider(): string {
        return this.get<string>('MESSAGE_SERVICE_PROVIDER') || 'MESSAGE_HUB';
    }
}
