import { UploadedFiles } from '@nestjs/common';
import { Headers } from '@nestjs/common';
import { Body, Controller, Post, UseInterceptors } from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ClinicService } from './clinic.service';
import { RegisterClinicDto } from './dto/register-clinic.dto';

@Controller('clinic')
@ApiTags(`Quản lý phòng mạch`)
export class ClinicController {
    constructor(private service: ClinicService) {}

    @Post(`register`)
    @ApiOperation({summary: `Đăng ký tài khoản quản lý phòng mạch`})
    @ApiBody({
        type: RegisterClinicDto,
    })
    async registerClinic(@Body() formData: RegisterClinicDto): Promise<any> {
        return this.service.registerClinic(formData);
    }

    @Post(`upload`)
    @UseInterceptors(
        FileFieldsInterceptor([
            { name: 'file', maxCount: 1 },
        ]),
    )
    @ApiConsumes('multipart/form-data')
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    })
    async uploadFileClinic(
        @UploadedFiles() files,
    ): Promise<any> {
        return this.service.uploadFileClinic(files);
    }
}
