import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { UserSchema } from 'src/user/schemas/user.schema';
import { CskhController } from './cskh.controller';
import { CskhService } from './cskh.service';

@Module({
    imports: [MongooseModule.forFeature([
        { name: USER_COLLECTION_NAME, schema: UserSchema },
        { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
    ])],
    controllers: [CskhController],
    providers: [CskhService],
})
export class CskhModule {}
