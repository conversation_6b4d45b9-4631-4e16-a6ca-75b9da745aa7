import { ApiProperty } from '@nestjs/swagger';

export class DoctorDTO {
    @ApiProperty({
        description: 'Id unique',
        required: true,
        type: Number,
    })
    readonly id: number;

    @ApiProperty({
        description: '<PERSON><PERSON> và tên <PERSON>',
        required: true,
        type: String,
    })
    readonly fullname: string;

    @ApiProperty({
        description: '<PERSON>ên bác s<PERSON>',
        required: true,
        type: String,
    })
    readonly name: string;

    @ApiProperty({
        description: 'Chức vụ',
        required: true,
        type: String,
    })
    readonly role: string;

    @ApiProperty({
        description: 'Giới tính',
        required: true,
        type: Number,
    })
    readonly gender: number;

    @ApiProperty({
        description: 'Hình đại diện',
        required: true,
        type: String,
    })
    readonly avatar: string;

    @ApiProperty({
        description: 'Lịch làm việc',
        required: true,
        type: String,
    })
    readonly workingDay: string;
}
