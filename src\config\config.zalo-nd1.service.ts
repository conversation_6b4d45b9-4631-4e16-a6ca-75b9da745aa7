import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';

@Injectable()
export class ZaloNd1ConfigService extends ConfigManager {
    provideConfigSpec() {
        return {
            ZALO_NHI_DONG_1_URL_ACCESS_TOKEN: {
                validate: Joi.string(),
                required: true,
                default: 'https://oauth.zaloapp.com/v3/access_token',
            },
            ZALO_NHI_DONG_1_URL_GET_INFO: {
                validate: Joi.string(),
                required: true,
                default: 'https://graph.zalo.me/v2.0/me',
            },
            ZALO_NHI_DONG_1_APP_ID: {
                validate: Joi.string(),
                required: true,
                default: '1837150420993020578',
            },
            ZALO_NHI_DONG_1_APP_SECRET: {
                validate: Joi.string(),
                required: true,
                default: 'QR8EOmdOZVDLRnNILm11',
            },
        };
    }

    getZaloConfig() {
        return {
            appId: this.get<string>('ZALO_NHI_DONG_1_APP_ID'),
            appSecret: this.get<string>('ZALO_NHI_DONG_1_APP_SECRET'),
            urlAccessToken: this.get<string>('ZALO_NHI_DONG_1_URL_ACCESS_TOKEN'),
            urlZaloInfo: this.get<string>('ZALO_NHI_DONG_1_URL_GET_INFO'),
        };
    }

}
