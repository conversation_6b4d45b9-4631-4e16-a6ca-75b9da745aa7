import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';

@Injectable()
export class OneSignalTeleMedicineConfigService extends ConfigManager {
    provideConfigSpec() {
        return {
            ONESIGNAL_TELE_MEDICINE_API_ROOT: {
                validate: Joi.string(),
                required: true,
            },
            ONESIGNAL_TELE_MEDICINE_APP_ID: {
                validate: Joi.string(),
                required: true,
            },
            ONESIGNAL_TELE_MEDICINE_API_KEY: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    getOneSignalConfig() {
        return {
            apiRoot: this.get<string>('ONESIGNAL_TELE_MEDICINE_API_ROOT'),
            appId: this.get<string>('ONESIGNAL_TELE_MEDICINE_APP_ID'),
            apiKey: this.get<string>('ONESIGNAL_TELE_MEDICINE_API_KEY'),
        };
    }

}
