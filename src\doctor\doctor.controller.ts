import { Controller, Post, HttpStatus, HttpCode } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { DoctorService } from './doctor.service';
import { OutDoctorsDTO } from './dto/outDoctor.dto';
import { plainToClass } from 'class-transformer';

@Controller('doctor')
@ApiTags('Doctor - <PERSON><PERSON><PERSON>n l<PERSON>')
export class DoctorController {
    constructor(
        private readonly doctorService: DoctorService,
    ) { }

    @Post('all')
    @ApiOperation({ summary: '<PERSON><PERSON><PERSON><PERSON> sách Thông tin <PERSON>c sĩ.', description: '<PERSON><PERSON><PERSON> sách Thông tin <PERSON>ác sĩ.' })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        type: OutDoctorsDTO,
        description: '<PERSON><PERSON>y danh sách tỉnh thành.',
    })
    async allDoctors(): Promise<OutDoctorsDTO> {
        return plainToClass(OutDoctorsDTO, await this.doctorService.allDoctors());
    }

}
