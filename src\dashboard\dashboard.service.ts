import { Inject, Injectable } from '@nestjs/common';
import { PKH_CONNECTION } from '../config/pkhConnection/index';
import { PKH_PATIENT_CONNECTION } from '../config/pkhPatientConnection/index';
import { THU_DUC_HOSPITAL_CONNECTION } from '../config/thuDucHospitalConnection/index';

@Injectable()
export class DashboardService {

    constructor(

        @Inject(PKH_CONNECTION) private readonly pkhKnex,
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        @Inject(THU_DUC_HOSPITAL_CONNECTION) private readonly hospitalKnex,

    ) {
        // this.userName = pkhConfigService.get<string>('DB_USERNAME');
    }

    filterBookingList(): Promise<any> {
        const medprodb = 'medprodb';
        const userTable = `${medprodb}.user`;
        const paymentTable = 'payment';
        const bookingTable = 'booking';
        const patientTable = 'patient';
        const scheduleTable = 'schedule';
        const subjectTable = 'subject';
        const roomTable = 'room';
        const doctorTable = 'doctor';
        return this.hospitalKnex()
            .select(
                `${bookingTable}.id as bookingId`,
                `${bookingTable}.transaction_code_gd as transaction_code_gd`,
                `${userTable}.username as username`,
                /*     PAYMENT TABLE        */
                `${paymentTable}.amount as amount`,
                `${paymentTable}.transaction_code_tt as transactionCode`,
                `${paymentTable}.card_code as cardCode`,
                `${paymentTable}.method_id as methodId`,
                `${paymentTable}.result as typeResultBank`,
                /*     PATIENT TABLE        */
                `${patientTable}.id as patientId`,
                `${patientTable}.name as patientName`,
                `${patientTable}.surname as patientSurname`,
                `${patientTable}.cmnd as patientCmnd`,
                `${patientTable}.sex as patientSex`,
                `${patientTable}.mobile as patientMobile`,
                `${patientTable}.email as patientEmail`,
                `${patientTable}.address as patientAddress`,
                `${patientTable}.birthyear as birthYear`,
                `${patientTable}.birthdate as birthDate`,
                `${patientTable}.bv_id as bvId`,
                `${patientTable}.old_bv_id as oldBvId`,
                `${patientTable}.medpro_id as medproId`,
                `${patientTable}.bhyt as patientBhyt`,
                /*     SCHEDULE TABLE        */
                `${scheduleTable}.id as scheduleId`,
                /*     SUBJECT TABLE        */
                `${subjectTable}.id as subjectId`,
                `${subjectTable}.name as subjectName`,
                /*     ROOM TABLE        */
                `${roomTable}.id as roomId`,
                `${roomTable}.name as roomName`,
                `${roomTable}.description as roomDescription`,
                /*     DOCTOR TABLE        */
                `${doctorTable}.id as doctorId`,
                `${doctorTable}.name as doctorName`,
                `${doctorTable}.sex as doctorGender`,

            )
            .from(bookingTable)
            .innerJoin(userTable, `${userTable}.id`, `${bookingTable}.user_id`)
            .innerJoin(patientTable, `${patientTable}.id`, `${bookingTable}.patient_id`)
            .innerJoin(scheduleTable, `${scheduleTable}.id`, `${bookingTable}.schedule_id`)
            .innerJoin(subjectTable, `${subjectTable}.id`, `${scheduleTable}.subject_id`)
            .innerJoin(roomTable, `${roomTable}.id`, `${scheduleTable}.room_id`)
            .innerJoin(doctorTable, `${doctorTable}.id`, `${scheduleTable}.doctor_id`)
            .leftJoin(paymentTable, `${paymentTable}.id`, `${bookingTable}.payment_id`)
            .offset(5)
            .limit(10);
        // .where(`${bookingTable}.id`, 725);
    }

    getPhieuKhamTrongNgay(): {} {
        return {
            dashboard: {
                totalBookings: 100,
            },
        };
    }

    getUserTrongNgay(): {} {
        return {
            dashboard: {
                totalUsers: 200,
            },
        };
    }

    getBenhNhanTrongNgay(): {} {
        return {
            dashboard: {
                totalPatients: 300,
            },
        };
    }

    getChartInfoByDate(): {} {
        return {
            dashboard: {
                thisWeek: [
                    {
                        '16-12-2019': 100,
                    },
                    {
                        '17-12-2019': 150,
                    },
                    {
                        '18-12-2019': 134,
                    },
                    {
                        '19-12-2019': 167,
                    },
                    {
                        '20-12-2019': 133,
                    },
                    {
                        '21-12-2019': 120,
                    },
                ],
            },
        };
    }
}
