import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { Model } from 'mongoose';
import * as moment from 'moment';
import { get } from "lodash";
import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { JwtUserYTeConfigService } from 'src/config/config.user-yte.jwt.service';
import { BookingCovid19DTO } from 'src/booking-gateway/dto/booking-covid19.dto';
import { BookingStatus } from 'src/his-gateway/dto/bookingStatus.dto';
import { InsuranceChoiceValue } from 'src/booking-gateway/dto/insurance-choice.dt';
import { UrlConfigService } from 'src/config/config.url.service';
import { UtilService } from 'src/config/util.service';

@Injectable()
export class BookingCovidService {

    constructor(
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        private readonly jwtUserYTeConfigService: JwtUserYTeConfigService,
        private readonly urlConfigService: UrlConfigService,
        private readonly utilService: UtilService
    ) { }

    getInvoiceUrl(bookingInfoObj: any): any {
        if (!!bookingInfoObj.invoiceId) {
            const baseUrl = this.urlConfigService.getInvoiceUrl();
            const invoiceUrl = `${baseUrl}/invoice-api/invoice/file/${bookingInfoObj.partnerId}/${bookingInfoObj.invoiceId}.pdf`;
            return invoiceUrl;
        }
        return '';
    }

    // getBookingText(booking: any): string {
    //     const { status, noPayment = false, partnerId = '' } = booking;
    //     switch (status) {
    //         case BookingStatus.RESERVE:
    //         case BookingStatus.SHARETOPAY:
    //             return 'Chưa thanh toán';
    //         case BookingStatus.CONFIRMED: {
    //             if (noPayment) {
    //                 if (partnerId && partnerId === 'pkdkanhao') {
    //                     return 'Đặt khám thành công';
    //                 } else {
    //                     return 'Đã xác nhận';
    //                 }
    //             } else {
    //                 return 'Đã thanh toán';
    //             }
    //         }
    //         case BookingStatus.DA_KHAM:
    //             return 'Đã khám';
    //         case BookingStatus.EXPIRED_RESERVATION:
    //             return 'Hết hạn';
    //         case BookingStatus.CANCEL_RESERVATION:
    //             return 'Đã hủy';
    //         default:
    //             return 'Chờ xác nhận';
    //     }
    // }

    getInsuranceChoiceText(booking: any): string {
        const getInsuranceChoice = get(booking, 'insuranceChoice', null);
        if (getInsuranceChoice) {
            switch (getInsuranceChoice) {
                case InsuranceChoiceValue.CHUYEN_TUYEN:
                    return 'Chuyển tuyến';
                case InsuranceChoiceValue.DUNG_TUYEN:
                    return 'Đúng tuyến';
                case InsuranceChoiceValue.TAI_KHAM:
                    return 'Tái khám';
                default:
                    return '';
            }
        }
        return '';
    }

    async handleDayForBookingCovid19(day?): Promise<any> {
        let fromDateConfig = { hours: 0, minutes: 0, seconds: 0 };
        let toDateConfig = { hours: 23, minutes: 59, seconds: 59 };
        if (day === 1) {
            fromDateConfig = { hours: 0, minutes: 0, seconds: 0 };
            toDateConfig = { hours: 12, minutes: 0, seconds: 0 };
        }
        if (day === 2) {
            fromDateConfig = { hours: 12, minutes: 0, seconds: 0 };
            toDateConfig = { hours: 23, minutes: 59, seconds: 59 };
        }
        return { fromDateConfig, toDateConfig };
    }

    async handleTimeGetBookingCovid(date?, day?): Promise<any> {
        const currentDate = date ? moment(date) : moment();
        const { fromDateConfig, toDateConfig } = await this.handleDayForBookingCovid19(day);
        const fromDate = currentDate.add(7, 'hours').set(fromDateConfig).toDate();
        const toDate = currentDate.set(toDateConfig).toDate();
        return { fromDate, toDate };
    }

    async handleParamsForBookingCovid(partnerId: string, fromData: BookingCovid19DTO): Promise<any> {
        const { serviceId = 'leloi_VRSARS', date = '', day = 0 } = fromData;
        const { fromDate, toDate } = await this.handleTimeGetBookingCovid(date, day);
        let params: any = { status: 1, fromDate, toDate };
        if (partnerId) {
            params = { ...params, partnerId };
        }
        if (serviceId) {
            params = { ...params, serviceId };
        }
        return params;
    }

    async allBookingCovid19(partnerId: string, fromData: BookingCovid19DTO): Promise<any> {
        const { fromDate, toDate, ...params } = await this.handleParamsForBookingCovid(partnerId, fromData);
        Logger.log(`fromDate: ${fromDate}, toDate: ${toDate}`);
        const bookingsData = await this.bookingModel
            .find({ ...params, date: { $gte: fromDate, $lt: toDate } })
            .populate('subject')
            .populate('service')
            .populate('room')
            .populate('section')
            .populate('doctor')
            .populate('bookingSlot')
            .populate({ path: 'partner', select: { name: true } })
            .populate('patient')
            .populate('patientVersion')
            .sort({ date: 'asc' })
            .exec();
        /* duyệt từng thằng để trả về status */
        const bookings = bookingsData.map(item => {
            const bookingOb = item.toObject();
            /* tiến hành đảo thông tin */
            const changeObj: any = {};
            if (typeof item.patientVersion !== typeof undefined) {
                changeObj.patient = item.patientVersion;
            }
            const description = this.utilService.getBookingText(bookingOb);
            const insuranceChoiceText = this.getInsuranceChoiceText(bookingOb);
            const invoiceUrl = this.getInvoiceUrl(bookingOb);
            const objDateDisplay: any = {};
            if (bookingOb.date === null) {
                objDateDisplay.waitingConfirmDate = 'Chờ xác nhận';
            }
            const rewriteBookingCode =
                typeof bookingOb.bookingCodeV1 !== typeof undefined && bookingOb.bookingCodeV1 ? bookingOb.bookingCodeV1 : bookingOb.bookingCode;
            return {
                ...bookingOb,
                description,
                insuranceChoiceText,
                invoiceUrl,
                ...changeObj,
                ...objDateDisplay,
                bookingCode: rewriteBookingCode,
            };
        });
        return bookings;
    }
}
