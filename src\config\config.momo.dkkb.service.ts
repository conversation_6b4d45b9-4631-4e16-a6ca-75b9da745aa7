import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Joi from 'joi';

@Injectable()
export class MomoDKKBConfigService extends ConfigManager {
    provideConfigSpec() {
        return {
            MOMO_DKKB_ACCESS_KEY: {
                validate: Joi.string(),
                required: true,
            },
            MOMO_DKKB_SECRET_KEY: {
                validate: Joi.string(),
                required: true,
            },
            MOMO_DKKB_RETURN_URL: {
                validate: Joi.string(),
                required: true,
            },
            MOMO_DKKB_NOTIFY_URL: {
                validate: Joi.string(),
                required: true,
            },
            MOMO_DKKB_PARTNER_CODE: {
                validate: Joi.string(),
                required: true,
            },
            MOMO_DKKB_APP_SCHEME: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    getMoMo_DKKB_Config() {
        return {
            momo_dkkb_access_key: this.get<string>('MOMO_DKKB_ACCESS_KEY'),
            momo_dkkb_secret_key: this.get<string>('MOMO_DKKB_SECRET_KEY'),
            momo_dkkb_return_url: this.get<string>('MOMO_DKKB_RETURN_URL'),
            momo_dkkb_notify_url: this.get<string>('MOMO_DKKB_NOTIFY_URL'),
            momo_dkkb_partner_code: this.get<string>('MOMO_DKKB_PARTNER_CODE'),
            momo_dkkb_app_scheme: this.get<string>('MOMO_DKKB_APP_SCHEME'),
        };
    }

}
