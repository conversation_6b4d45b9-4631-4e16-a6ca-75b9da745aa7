import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { PageOptionnsDto } from 'src/common/base/page-options.dto';

export class PaymentPageOptionDto extends PageOptionnsDto {
    @ApiProperty({ description: 'id booking'})
    @Transform(value => `${value}`.trim())
    bookingId?: string;

    @ApiProperty({ description: 'Mã phiếu'})
    @Transform(value => `${value}`.trim())
    bookingCode?: string;

    @ApiProperty({ description: 'Mã giao dịch'})
    @Transform(value => `${value}`.trim())
    transactionId?: string;

    @ApiProperty({ description: 'Parnter Id'})
    @Transform(value => `${value}`.trim())
    partnerId?: string;

    @ApiProperty({ description: 'App Id'})
    @Transform(value => `${value}`.trim())
    appId?: string;
}
