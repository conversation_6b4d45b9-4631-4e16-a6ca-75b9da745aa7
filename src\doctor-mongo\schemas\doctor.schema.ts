import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { DOCTOR_COLLECTION_NAME } from './constants';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const DoctorSchema = new Schema({
    fullname: String,
    name: String,
    role: String,
    gender: Boolean,
    avatar: String,
    partnerId: String,
    hospitalId: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
}, {
    collection: DOCTOR_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
