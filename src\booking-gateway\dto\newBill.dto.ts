
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

export class NewBillDTO {

    @ApiProperty({
        description: 'transactionId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung transactionId',
    })
    transactionId: string;

    @ApiProperty({
        description: 'orderId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung orderId',
    })
    orderId: string;

    @ApiProperty({
        description: 'userId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung userId',
    })
    userId: string;

    @ApiProperty({
        description: 'name',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung name',
    })
    name: string;

    @ApiProperty({
        description: 'partnerId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung partnerId',
    })
    partnerId: string;

    @ApiProperty({
        description: 'tennantId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung tennantId',
    })
    tennantId: string;

    @ApiProperty({
        description: 'gatewayId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung gatewayId',
    })
    gatewayId: string;

    @ApiProperty({
        description: 'type',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung type',
    })
    type: string;

    @ApiProperty({
        description: 'code',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung code',
    })
    code: string;

    @ApiProperty({
        description: 'subTotal',
        required: true,
        type: Number,
    })
    @Transform(value => Number(value))
    @IsNotEmpty({
        message: 'Vui lòng bổ sung subTotal',
    })
    subTotal: number;

    @ApiProperty({
        description: 'grandTotal',
        required: true,
        type: Number,
    })
    @Transform(value => Number(value))
    @IsNotEmpty({
        message: 'Vui lòng bổ sung grandTotal',
    })
    grandTotal: number;

    @ApiProperty({
        description: 'totalFee',
        required: true,
        type: Number,
    })
    @Transform(value => Number(value))
    @IsNotEmpty({
        message: 'Vui lòng bổ sung totalFee',
    })
    totalFee: number;

    @ApiProperty({
        description: 'totalFee',
        required: true,
        type: Number,
    })
    @Transform(value => Number(value))
    @IsNotEmpty({
        message: 'Vui lòng bổ sung totalFee',
    })
    medproFee: number;

    @ApiProperty({
        description: 'description',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung description',
    })
    description: string;

    @ApiProperty({
        description: 'status',
        required: true,
        type: Number,
    })
    @Transform(value => Number(value))
    @IsNotEmpty({
        message: 'Vui lòng bổ sung status',
    })
    status: number;

    @ApiProperty({
        description: 'description',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung description',
    })
    message: string;

    @ApiProperty({
        description: 'description',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung description',
    })
    gatewayMessage: string;

    @ApiProperty({
        description: 'feeCode',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung feeCode',
    })
    feeCode: string;

    @ApiProperty({
        description: 'billId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung billId',
    })
    billId: string;

    @ApiProperty({
        description: 'patientId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung patientId',
    })
    patientId: string;

    @ApiProperty({
        description: 'patientEmrno',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung patientEmrno',
    })
    patientEmrno: string;

    @ApiProperty({
        description: 'typeId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung typeId',
    })
    typeId: string;
}
