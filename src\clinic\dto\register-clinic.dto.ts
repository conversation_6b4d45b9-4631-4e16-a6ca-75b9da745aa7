import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEmail, IsMobilePhone, IsNotEmpty, ValidateIf } from 'class-validator';

export class RegisterClinicDto {
    @ApiProperty()
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng nhập họ tên !',
    })
    fullName: string;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng nhập số điện thoại !',
    })
    @IsMobilePhone('vi-VN', { strictMode: false }, {
        message: 'Số điện thoại không hợp lệ !',
    })
    phone: string;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng nhập email !',
    })
    @IsEmail({}, { message: '<PERSON><PERSON> không đúng định dạng !' })
    @ValidateIf(o => o.email)
    email: string;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng nhập học hàm / học vị !',
    })
    degree: string;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng nhập chuyên khoa ! ',
    })
    subject: string;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng nhập tên phòng mạch !',
    })
    clinicName: string;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    workPlace: string;

    @ApiProperty()
    avatarList: string[];

    @ApiProperty()
    clinicImageList: string[];

    @ApiProperty()
    degreeImageList: string[];

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng nhập giấy phép đăng ký kinh doanh !',
    })
    clinicERC: string;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    clinicIntroduce: string;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng nhập mã tỉnh / thành phố !',
    })
    city: string;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng nhập mã quận / huyện !',
    })
    district: string;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng nhập mã phường / xã !',
    })
    ward: string;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng nhập địa chỉ !',
    })
    address: string;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng nhập loại phòng mạch hay phòng khám !',
    })
    clinicType: string;
}
