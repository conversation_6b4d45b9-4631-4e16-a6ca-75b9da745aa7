import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, ValidateIf } from "class-validator";

import { Transform, Expose } from 'class-transformer';
export class ReserveByTranstionDTO {

    @ApiProperty({
        description: 'id',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung id',
    })
    id: string;

    @ApiProperty({
        description: 'Đường dẫn trả kết quả',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin mã giao dịch',
    })
    @ValidateIf(o => o.platform)
    redirectUrl: string;
    @ApiProperty({
        description: 'Phương thức thanh toán',
        required: true,
        type: String,
        default: 'momo',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    methodId: string;

    @ApiProperty({
        description: 'Thông tin payment type detail',
        required: true,
        type: String,
        default: 'momo',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    paymentTypeDetail: string;
    @ApiProperty({
        description: 'groupId',
        required: false,
        type: Number,
    })
    @Transform(value => Number(value))
    @ValidateIf(o => o.groupId)
    groupId?: number;
}
