import { <PERSON>du<PERSON> } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>ontroller } from './doctor-mongo.controller';
import { DoctorMongoService } from './doctor-mongo.service';
import { MongooseModule } from '@nestjs/mongoose';
import { DOCTOR_COLLECTION_NAME } from './schemas/constants';
import { DoctorSchema } from './schemas/doctor.schema';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';

@Module({
  imports: [MongooseModule.forFeature([
    { name: DOCTOR_COLLECTION_NAME, schema: DoctorSchema },
    { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
  ])],
  controllers: [DoctorMongoController],
  providers: [DoctorMongoService],
})
export class DoctorMongoModule { }
