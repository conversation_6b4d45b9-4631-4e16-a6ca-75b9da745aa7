import { ApiProperty } from '@nestjs/swagger';
import { Transform, Expose } from 'class-transformer';
import { IsNotEmpty, IsDateString, ValidateIf } from 'class-validator';
import * as uuid from 'uuid';

export class UpdateStatusDTO {

    @ApiProperty({
        description: 'Transaction',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    transactionId: string;

    @ApiProperty({
        description: 'Trạng thái',
        required: true,
        type: Number,
    })
    @Transform(value => Number(value))
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    status: number;

    @ApiProperty({
        description: 'Message',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.message)
    message: string;

}
