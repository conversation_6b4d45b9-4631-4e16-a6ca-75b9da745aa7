import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { EXAM_RESULT_LOG_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const ExamResultLogSchema = new Schema({
    id: { type: String },
    userId: { type: String },
    patientId: { type: String },
    requestData: { type: String },
    responseData: { type: String },
    createTime: { type: Date },
}, {
    collection: EXAM_RESULT_LOG_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
