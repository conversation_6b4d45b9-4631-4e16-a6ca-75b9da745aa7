import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { BOOKING_COLLECTION_NAME, BOOKING_ACTION_COLLECTION_NAME } from './constants';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';

const Schema = mongoose.Schema;

export const BookingActionSchema = new Schema({
    id: { type: String },
    bookingId: { type: String },
    booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
    preStatus: { type: Number },
    status: { type: Number },
    username: { type: String },
    email: { type: String },
    user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
    createdTime: { type: Date },
    ip: { type: String },
    errorCode: { type: Number },
    errorMessage: { type: String },
}, {
    collection: BOOKING_ACTION_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
