import { Module, DynamicModule, Provider, Global } from '@nestjs/common';
import { FirebaseServiceUMC } from './firebase.service';
import { FIREBASE_SERVICE_ACCOUNT_UMC } from './constants';
import {
    FirebaseOptions,
    FireBaseAsyncServiceAccount,
    FirebaseOptionsFactory,
} from './interfaces';
import { createFirebaseUMCProviders } from './firebase.providers';

import { connectionFactory } from './firebase-connection.provider';

@Global()
@Module({
    providers: [FirebaseServiceUMC, connectionFactory],
    exports: [FirebaseServiceUMC, connectionFactory],
})
export class FirebaseAdminUMCModule {
    /**
     * Registers a configured NestKnex Module for import into the current module
     */
    public static register(options: FirebaseOptions): DynamicModule {
        return {
            module: FirebaseAdminUMCModule,
            providers: createFirebaseUMCProviders(options),
        };
    }

    /**
     * Registers a configured NestKnex Module for import into the current module
     * using dynamic options (factory, etc)
     */
    public static registerAsync(options: FireBaseAsyncServiceAccount): DynamicModule {
        return {
            module: FirebaseAdminUMCModule,
            providers: [...this.createProviders(options)],
        };
    }

    private static createProviders(options: FireBaseAsyncServiceAccount): Provider[] {
        if (options.useExisting || options.useFactory) {
            return [this.createOptionsProvider(options)];
        }

        return [
            this.createOptionsProvider(options),
            {
                provide: options.useClass,
                useClass: options.useClass,
            },
        ];
    }

    private static createOptionsProvider(options: FireBaseAsyncServiceAccount): Provider {
        if (options.useFactory) {
            return {
                provide: FIREBASE_SERVICE_ACCOUNT_UMC,
                useFactory: options.useFactory,
                inject: options.inject || [],
            };
        }

        // For useExisting...
        return {
            provide: FIREBASE_SERVICE_ACCOUNT_UMC,
            useFactory: async (optionsFactory: FirebaseOptionsFactory) =>
                await optionsFactory.createServiceAccount(),
            inject: [options.useExisting || options.useClass],
        };
    }
}
