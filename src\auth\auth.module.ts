import { Modu<PERSON> } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AdminUserModule } from '../admin-user/admin-user.module';
import { PassportModule } from '@nestjs/passport';
import { LocalStrategy } from './local.strategy';
import { JwtStrategy } from './jwt.strategy';
import { JwtModule } from '@nestjs/jwt';
import { JwtAdminConfigService } from 'src/config/config.admin.jwt.service';
import { ConfigManagerModule } from '@nestjsplus/config';

@Module({
  imports: [
    AdminUserModule,
    PassportModule,
    JwtModule.registerAsync({
      // imports: [ConfigManagerModule],
      useExisting: JwtAdminConfigService,
    }),
  ],
  providers: [AuthService, LocalStrategy, JwtStrategy],
  exports: [AuthService],
})
export class AuthModule { }
