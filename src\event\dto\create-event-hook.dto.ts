
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, IsDateString, ValidateIf } from 'class-validator';

export class CreateEventHookDTO {
    @ApiProperty({
        description: 'Format kiểu ISO String',
        required: true,
        type: String,
    })
    @IsDateString({ strict: true }, {
        message: 'Thông tin createTime. ISOString',
    })
    date: string;

    @ApiProperty({
        description: 'topicId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung topicId',
    })
    topicId: string;

    @ApiProperty({
        description: 'feederData',
        required: true,
        type: Object,
    })
    feederData: object;

    @ApiProperty({
        description: 'appId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: '<PERSON>ui lòng bổ sung appId',
    })
    appId: string;

    @ApiProperty({
        description: 'title',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung title',
    })
    title: string;

    @ApiProperty({
        description: 'content',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung content',
    })
    @ValidateIf(o => o.content)
    content?: string;

    @ApiProperty({
        description: 'partnerId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung partnerId',
    })
    partnerId: string;

    // @ApiProperty({
    //     description: 'type',
    //     required: false,
    //     type: Number,
    // })
    // @Transform(value => Number(value))
    // @IsNotEmpty({
    //     message: 'Vui lòng bổ sung type',
    // })
    // @ValidateIf(o => o.type)
    // type?: number;

    @ApiProperty({
        description: 'userId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung userId',
    })
    userId: string;

    id?: string;
}
