import { CLINIC_COLLECTION_NAME } from './../constant';

import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';

const Schema = mongoose.Schema;

export const ClinicSchema = new Schema({
    fullName: {type : String},
    phone: {type : String},
    email: {type : String},
    degree: {type : String},
    subject: {type : String},
    clinicName: {type : String},
    workPlace: {type : String},
    avatarList: {type : [String]},
    clinicImageList: {type : [String]},
    degreeImageList: {type : [String]},
    clinicERC: {type : String},
    clinicIntroduce: {type : String},
    city: {type : String},
    district: {type : String},
    ward: {type : String},
    address: {type : String},
    clinicType:  {type : String},
}, {
    collection: CLINIC_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
