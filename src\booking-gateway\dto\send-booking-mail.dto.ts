
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, ValidateIf, IsMobilePhone, IsEmail } from 'class-validator';

export class SendBookingMailSMSDTO {

    @ApiProperty({
        description: 'id của booking',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    readonly id: string;

    @ApiProperty({
        description: 'email',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @IsEmail({}, { message: 'Email không đúng định dạng' })
    @ValidateIf(o => o.email)
    email?: string;

    @ApiProperty({
        description: 'Số điện thoại',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @IsMobilePhone('vi-VN', { strictMode: false }, {
        message: 'Số điện thoại không hợp lệ.',
    })
    @ValidateIf(o => o.mobile)
    mobile?: string;

}
