import { Injectable } from '@nestjs/common';
import { BookingStatus } from 'src/his-gateway/dto/bookingStatus.dto';
import { OrderItemStatus } from 'src/vaccine/dto/order-item-status.dto';
import * as QRCode from 'qrcode-svg';
import * as JsBarcode from 'jsbarcode';
import { DOMImplementation, XMLSerializer } from 'xmldom';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { xorBy } from 'lodash';
import { PinoLogger } from 'nestjs-pino';
import * as moment from 'moment';
import uuid = require('uuid');

@Injectable()
export class UtilService {
    constructor(private readonly pinoLogger: PinoLogger) {}

    orderItemStatus = (status: number): object => {
        switch (status) {
            case OrderItemStatus.DA_TIEM:
                return {
                    cls: 3,
                    status: 'Đã tiêm',
                    statusDisplay: 'Đã tiêm',
                };
            case OrderItemStatus.CHUA_TIEM:
                return {
                    cls: 2,
                    status: 'Chưa tiêm',
                    statusDisplay: 'Đổi lịch tiêm',
                };
            case OrderItemStatus.CHUA_DAT_LICH:
                return {
                    cls: 1,
                    status: 'Chưa đặt lịch tiêm',
                    statusDisplay: 'Đặt lịch tiêm',
                };
            default:
                return {};
        }
    }
    getRandomInt = (min: number, max: number): number => {
        min = Math.ceil(min);
        max = Math.floor(max);
        return Math.floor(Math.random() * (max - min)) + min;
    }
    randomText(vLength = 16) {
        const alphabet = 'abcdefghijklmnopqrstuwxyzABCDEFGHIJKLMNOPQRSTUWXYZ0123456789';
        let pass = [];
        const alphaLength = alphabet.length - 1;
        for (let i = 0; i < vLength; i++) {
            const n = this.getRandomInt(0, alphaLength);
            pass = [...pass, alphabet[n]];
        }
        return pass.join('');
    }

    randomNumber(vLength = 4) {
        const alphabet = '0123456789';
        let pass = [];
        const alphaLength = alphabet.length - 1;
        for (let i = 0; i < vLength; i++) {
            const n = this.getRandomInt(0, alphaLength);
            pass = [...pass, alphabet[n]];
        }
        return pass.join('');
    }

    oldHospitalSync(): Set<{}> {
        const newSetOldHospitals = new Set();
        newSetOldHospitals.add('nhidong1');
        // newSetOldHospitals.add('dalieuhcm');
        newSetOldHospitals.add('ctchhcm');
        newSetOldHospitals.add('thuduc');
        newSetOldHospitals.add('umc');
        return newSetOldHospitals;
    }

    listAppId(): Set<{}> {
        const newSetListAppId = new Set();
        newSetListAppId.add('medpro');
        newSetListAppId.add('nhidong1');
        newSetListAppId.add('umc');
        newSetListAppId.add('momo');
        newSetListAppId.add('umcmono');
        newSetListAppId.add('cskh');
        newSetListAppId.add('1900');
        return newSetListAppId;
    }

    patientIdV1Key(partnerId: string): string {
        let key = '';
        switch (partnerId) {
            case 'nhidong1':
                key = 'patientIdV1';
                break;
            case 'dalieuhcm':
                key = 'patientIdV1DaLieu';
                break;
            case 'umc':
                key = 'patientIdV1UMC';
                break;
            case 'ctchhcm':
                key = 'patientIdV1CTCH';
                break;
            case 'thuduc':
                key = 'patientIdV1ThuDuc';
                break;
            default:
                break;
        }
        return key;
    }

    transformPhone(phone: string): string {
        return `${phone}`
            .replace(/^[+]84|^0/, '+84')
            .replace(/^9/, '+849')
            .replace(/^3/, '+843');
    }

    errorHandler(error: any): any {
        try {
            const { response } = error;
            if (response?.data || response?.config) {
                return {
                    message: response?.message,
                    data: response.data,
                    config: {
                        url: response?.url || '',
                        method: response?.method || '',
                        data: response?.data || '',
                        headers: response?.headers || '',
                    },
                };
            }
            return JSON.parse(JSON.stringify(error));
        } catch (error) {
            return {
                message: 'Không parse được Error',
            };
        }
    }

    textMessageBooking(type: string): string {
        if (type === 'booking') {
            return 'Đặt khám thành công';
        } else {
            return 'Đã thanh toán';
        }
    }

    getBookingText(booking: any): string {
        const { status, noPayment = false, partnerId = '', bookingGroupType = 'booking' } = booking;
        switch (status) {
            case BookingStatus.RESERVE:
            case BookingStatus.SHARETOPAY:
                return 'Chưa thanh toán';
            case BookingStatus.CONFIRMED: {
                if (noPayment) {
                    if (partnerId && partnerId === 'pkdkanhao') {
                        return 'Đặt khám thành công';
                    } else {
                        return 'Đặt khám thành công';
                    }
                } else {
                    if (partnerId && partnerId === 'choray') {
                        return this.textMessageBooking(bookingGroupType);
                    } else {
                        if (booking?.subTotal === 0) {
                            return 'Đặt khám thành công';
                        } else {
                            return 'Đã thanh toán';
                        }
                    }
                }
            }
            case BookingStatus.DA_KHAM:
                return 'Đã khám';
            case BookingStatus.EXPIRED_RESERVATION:
                return 'Hết hạn';
            case BookingStatus.CANCEL_RESERVATION:
                return 'Đã hủy';
            default:
                return 'Chờ xác nhận';
        }
    }

    compareVersion(versionA: string, versionB: string): number {
        const arrVersionA = versionA.split('.').map(Number);
        const arrVersionB = versionB.split('.').map(Number);
        for (let i = 0; i < arrVersionA.length; ++i) {
            if (arrVersionB.length === i) {
                return 1;
            }
            if (arrVersionA[i] === arrVersionB[i]) {
                continue;
            } else if (arrVersionA[i] > arrVersionB[i]) {
                return 1;
            } else {
                return -1;
            }
        }
        if (arrVersionA.length !== arrVersionB.length) {
            return -1;
        }
        return 0;
    }

    generateQRCode(text: string) {
        try {
            const data = new QRCode({
                content: text,
                join: false,
                predefined: false,
            }).svg();
            return data;
        } catch (error) {
            throw error;
        }
    }

    generateBarCode(text: string) {
        const xmlSerializer = new XMLSerializer();
        const document = new DOMImplementation().createDocument('http://www.w3.org/1999/xhtml', 'html', null);
        const svgNode = document.createElementNS('http://www.w3.org/2000/svg', 'svg');

        JsBarcode(svgNode, text, {
            xmlDocument: document,
            displayValue: false,
        });

        const svgText = xmlSerializer.serializeToString(svgNode);
        return svgText;
    }

    sortPartnersPriorityFolowGlobalConfig(parnerGlobalConfig: string[], partners: IHospital[]): IHospital[] {
        const endOfList = partners.filter(partner => parnerGlobalConfig.includes(partner.partnerId));
        const priorityList = xorBy(partners, endOfList, 'partnerId');
        return [...priorityList, ...endOfList];
    }

    getGroupIdAfterCheck(query: any): number {
        const { cskhtoken, cskhInfo, booking } = query;
        if (cskhtoken || cskhInfo) {
            return 3;
        } else if (!cskhtoken && booking?.cskhUserId) {
            return 4;
        }
        return 1;
    }

    slugVietnameseName(value: string): string {
        const specialCharacter = /[`~!@#$%^&*()_|+\-=?;:'",.<>\{\}\[\]\\\/]/gi;
        value = value.replace(specialCharacter, '');
        value = value.replace(/\s+|\s+$/g, '-');
        value = value.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
        value = value.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
        value = value.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
        value = value.replace(/o|ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
        value = value.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
        value = value.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
        value = value.replace(/đ|d/g, 'd');
        value = value.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ''); // Huyền sắc hỏi ngã nặng
        value = value.replace(/\u02C6|\u0306|\u031B/g, ''); // Â, Ê, Ă, Ơ, Ư
        return value;
    }

    generateUUID(): string {
        const idV4 = uuid.v4();

        return idV4.replace(/-/g, '');
    }

    tranformArrToObj(arr: any[]): any {
        let obj: any = {};
        for (const item of arr) {
            obj = {
                ...obj,
                [`${item.key}`]: item.value,
            };
        }
        return obj;
    }

    splitFullName(fullName: string = ''): string[] {
        const [name, ...surnameTokens] = fullName
            .trim()
            .split(' ')
            .map(t => t.trim())
            .reverse();
        return [surnameTokens.reverse().join(' '), name];
    }

    getTableNameV1ByPartnerId(partnerId: string): string {
        switch (partnerId) {
            case 'umc':
                return 'booking';
            case 'nhidong1':
                return 'nd1_booking';
            case 'ctchhcm':
                return 'ctch_booking';
            default:
                break;
        }
    }

    oldHospital(): Set<string> {
        const newSetOldHospitals = new Set<string>();
        newSetOldHospitals.add('nhidong1');
        newSetOldHospitals.add('ctchhcm');
        newSetOldHospitals.add('umc');
        return newSetOldHospitals;
    }

    static delay(ms: number) {
        return new Promise(res => setTimeout(res, ms));
    }

    getCountryByLocale(locale = 'vi') {
        switch (locale) {
            case 'km':
                return 'KH'
            default:
                return 'VN'
        }
    }

    displayError(key: string): string {
        switch (key) {
            case 'birthdate':
                return 'Ngày sinh';
            case 'sex':
                return 'Giới tính';
            case 'city':
                return 'Tỉnh/Thành';
            case 'district':
                return 'Quận/Huyện';
            case 'ward':
                return 'Phường/Xã';
            case 'address':
                return 'Địa chỉ';
            case 'profession':
                return 'Nghề nghiệp';
            case 'relative_name':
                return 'Tên người thân';
            case 'relative_mobile':
                return 'SDT người thân';
            case 'relative_type_id':
                return 'Mối quan hệ';
            default:
                return '';
        }
    }

    getLatestDate(dates: string[]): string {
        return dates.reduce((latest, current) => 
            moment(current).isAfter(latest) ? current : latest
        );
    }
}

