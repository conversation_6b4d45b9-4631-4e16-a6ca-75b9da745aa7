import { Injectable, Inject, Logger } from '@nestjs/common';
import { FIREBASE_SERVICE_ACCOUNT_SECOND } from './constants';
import { FirebaseOptions } from './interfaces';
import * as admin2 from 'firebase-admin';
import * as uuid from 'uuid';

interface IFirebaseService {
    createServiceAccount();
}

@Injectable()
export class FirebaseServiceSecond implements IFirebaseService {
    private readonly logger: Logger;
    // tslint:disable-next-line: variable-name
    private _firebaseConnectionSecond: any;
    // tslint:disable-next-line: variable-name
    constructor(@Inject(FIREBASE_SERVICE_ACCOUNT_SECOND) private _firebaseOptions: FirebaseOptions) {
        this.logger = new Logger('FirebaseService');
        this.logger.log(`Options: ${JSON.stringify(this._firebaseOptions)}`);
    }

    createServiceAccount() {
        if (!this._firebaseConnectionSecond) {
            this._firebaseConnectionSecond = admin2.initializeApp(this._firebaseOptions, uuid.v4());
        }
        return this._firebaseConnectionSecond;
    }
}
