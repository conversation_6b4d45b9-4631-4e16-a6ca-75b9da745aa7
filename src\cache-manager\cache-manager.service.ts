import { CACHE_MANAGER, Inject, Injectable } from '@nestjs/common';
import { Cache } from 'cache-manager';

@Injectable()
export class CacheManagerService {
    constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

    async get(key: string): Promise<any> {
        return await this.cacheManager.get(key);
    }

    async set(key: string, value: any): Promise<void> {
        await this.cacheManager.set(key, value);
    }

    async del(key: string): Promise<void> {
        await this.cacheManager.del(key);
    }

    async delByPattern(pattern: string | RegExp): Promise<any> {
        let regex = new RegExp(pattern);
        console.log(`delByPattern: ${pattern}`);
        const keys = await this.cacheManager.store.keys();

        try {
            // console.debug(keys);
            return keys.filter((key) => key.match(regex)).map((key) => {
                console.log(`key matching pattern: ${key}`);
                this.cacheManager.del(key)
            });
        } catch (err) {
            console.log(`error del key by pattern ${pattern}: `, err);
            throw err;
        }
    }
}
