import { HttpService, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { EventService } from 'src/event/event.service';
import { UrlConfigService } from 'src/config/config.url.service';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { ConfigRepoService } from 'src/config/config.repo.service';
import { BookingGatewayService } from 'src/booking-gateway/booking-gateway.service';
import { MessageEventService } from 'src/message-event/message-event.service';
import { ServiceMongoService } from '../service-mongo/service-mongo.service';
import { AxiosRequestConfig } from 'axios';

@Injectable()
export class TaskService {
    private readonly logger = new Logger(TaskService.name);

    private env: string;
    // private runOneEnv: string;
    private readonly MESSAGE_EVENT_JOB: string = 'MESSAGE_EVENT_JOB';
    private readonly REPO_NAME: string;
    private readonly REPO_NAME_VALID: string;

    constructor(
        private readonly eventService: EventService,
        private readonly bookingGateWayService: BookingGatewayService,
        private readonly urlConfigService: UrlConfigService,
        private readonly globalSettingService: GlobalSettingService,
        private readonly configRepoService: ConfigRepoService,
        private readonly messageEvent: MessageEventService,
        private readonly serviceMongoService: ServiceMongoService,
        private readonly httpService: HttpService,
    ) {
        this.env = this.urlConfigService.getEnv();
        // this.runOneEnv = this.urlConfigService.getRunOneEnv();
        this.REPO_NAME = this.configRepoService.getRepoName();
        this.REPO_NAME_VALID = this.configRepoService.getRepoNameValidRunSync();
    }

    @Cron(CronExpression.EVERY_DAY_AT_3AM)
    async sendCashBackNotiOnUserBookingSuccessAfter1Days() {
        return this.messageEvent.pushCashbackNotiToUser();
    }

    @Cron(CronExpression.EVERY_DAY_AT_7AM)
    async pushNotificationCare247Remider() {
        return this.messageEvent.pushNotificationCare247Remider();
    }

    //  @Cron('*/5 * * * * *') // Run every 5 seconds to check
    async pushNotificationBookingRemider() {
        return this.messageEvent.pushNotificationBookingRemider();
    }
    
    @Cron(CronExpression.EVERY_DAY_AT_3AM)
    async pushZNSCare247Reminder() {
        return this.messageEvent.pushZnsCare247Remider();
    }

    @Cron('*/2 * * * * *')
    async handleMessageEventPushNotifCare247Reminder() {
        return this.eventService.handleMessageEventPushNotifCare247Reminder();
    }

    @Cron('*/2 * * * * *')
    async handleMessageProccessEventPushNotifCare247Reminder() {
        return this.eventService.handleMessageProccessEventPushNotifCare247Reminder();
    }

    @Cron('*/2 * * * * *')
    async handleMessageProccessEventUpdateNotifCare247Reminder() {
        return this.eventService.handleMessageProccessEventUpdateNotifCare247Reminder();
    }

    @Cron(CronExpression.EVERY_DAY_AT_8PM)
    async syncYesterdayNotifCare247(): Promise<any> {
        return this.eventService.syncYesterdayNotifCare247();
    }

    @Cron(CronExpression.EVERY_30_MINUTES)
    async setCacheApiHomePageJob () {
        const trace = `${this.REPO_NAME} - ${TaskService.name} - ${this.setCacheApiHomePageJob.name}`;
        const config = await this.globalSettingService.findByKeyAndRepoName(`HOME_PAGE_BLOCK_CONFIG_api-v2`);
        const { enableCache } = JSON.parse(config);

        if (!enableCache) {
            this.logger.log('Cache api homepage không được bật!' , trace);
            return;
        }

        try {
            const url = this.urlConfigService.getUrlApiV2;
        
            let config: AxiosRequestConfig = {
                method: 'post',
                url: `${url}/home-page/data-set-cache`,
                headers: { 
                  'accept': '*/*'
                }
            };

            await this.httpService.request(config).toPromise();
            
            this.logger.log('Gửi request trigger set cache api homepage thành công!' , trace);
            return;
        } catch (error) {
            this.logger.error('Gửi request trigger set cache api homepage thất bại!' , trace);
            return;        
        }
    }

    @Cron(CronExpression.EVERY_5_MINUTES)
    async cancelExpiredBookings() {
        // const enableExpiredBooking = await this.globalSettingService.findByKeyAndRepoName('ENABLE_EXPIRED_BOOKING');
        // if (this.env !== 'DEVELOPMENT' && enableExpiredBooking === 'ON' && this.REPO_NAME === this.REPO_NAME_VALID) {
        return this.bookingGateWayService.cancelExpiredBookings();
        // }
    }

    @Cron('*/20 * * * * *')
    async handlePushNotifForTelemedReminder() {
        // const telmedJobRepo = await this.globalSettingService.findByKeyAndRepoName(this.REPO_NAME_RUN_TELEMED_JOB);
        // const validate = this.env !== 'DEVELOPMENT' && this.REPO_NAME === this.REPO_NAME_VALID;
        // if (validate) {
        return this.eventService.getEventsForPushNotifyTelemed();
        // }
    }

    @Cron('*/4 * * * * *')
    async handleMessageEvent() {
        // const messageEventJob = await this.globalSettingService.findByKeyAndRepoName(this.MESSAGE_EVENT_JOB);
        // const validate = this.env !== 'DEVELOPMENT' && messageEventJob === 'ON' && this.REPO_NAME === this.REPO_NAME_VALID;
        // if (validate) {
        return this.eventService.handleMessageEvent();
        // }
    }

    @Cron('*/2 * * * * *')
    async handleMessageProccessEvent() {
        // const messageEventJob = await this.globalSettingService.findByKeyAndRepoName(this.MESSAGE_EVENT_JOB);
        // const validate = this.env !== 'DEVELOPMENT' && messageEventJob === 'ON' && this.REPO_NAME === this.REPO_NAME_VALID;
        // if (validate) {
        return this.eventService.handleMessageEventProcess();
        // }
    }

    @Cron('*/2 * * * * *')
    async handleMessageEventVExam() {
        // const messageEventJob = await this.globalSettingService.findByKeyAndRepoName(this.MESSAGE_EVENT_JOB);
        // const validate = this.env !== 'DEVELOPMENT' && messageEventJob === 'ON' && this.REPO_NAME === this.REPO_NAME_VALID;
        // if (validate) {
        return this.eventService.handleMessageEventVExam();
        // }
    }

    @Cron('*/2 * * * * *')
    async handleMessageProccessEventVExam() {
        // const messageEventJob = await this.globalSettingService.findByKeyAndRepoName(this.MESSAGE_EVENT_JOB);
        // const validate = this.env !== 'DEVELOPMENT' && messageEventJob === 'ON' && this.REPO_NAME === this.REPO_NAME_VALID;
        // if (validate) {
        return this.eventService.handleMessageEventProcessVExam();
        // }
    }

    @Cron('*/2 * * * * *')
    async handleMessageEventRemindExamUmc() {
        // const messageEventJob = await this.globalSettingService.findByKeyAndRepoName(this.MESSAGE_EVENT_JOB);
        // const validate = this.env !== 'DEVELOPMENT' && messageEventJob === 'ON' && this.REPO_NAME === this.REPO_NAME_VALID;
        // if (validate) {
        return this.eventService.handleMessageEventReminderExamUmc();
        // }
    }

    @Cron('*/2 * * * * *')
    async handleMessageProccessRemindExamUmc() {
        // const messageEventJob = await this.globalSettingService.findByKeyAndRepoName(this.MESSAGE_EVENT_JOB);
        // const validate = this.env !== 'DEVELOPMENT' && messageEventJob === 'ON' && this.REPO_NAME === this.REPO_NAME_VALID;
        // if (validate) {
        return this.eventService.handleMessageProcessEventReminderExamUmc();
        // }
    }


    @Cron('*/2 * * * * *')
    async handleMessageEventSurveyForm() {
        // const messageEventJob = await this.globalSettingService.findByKeyAndRepoName(this.MESSAGE_EVENT_JOB);
        // const validate = this.env !== 'DEVELOPMENT' && messageEventJob === 'ON' && this.REPO_NAME === this.REPO_NAME_VALID;
        // if (validate) {
        return this.eventService.handleMessageEventSurveyForm();
        // }
    }

    @Cron('*/2 * * * * *')
    async handleMessageProccessSurveyForm() {
        // const messageEventJob = await this.globalSettingService.findByKeyAndRepoName(this.MESSAGE_EVENT_JOB);
        // const validate = this.env !== 'DEVELOPMENT' && messageEventJob === 'ON' && this.REPO_NAME === this.REPO_NAME_VALID;
        // if (validate) {
        return this.eventService.handleMessageProcessEventSurveyForm();
        // }
    }

    @Cron('*/2 * * * * *')
    async handleMessageEventNotifTelemedNow() {
        // const messageEventJob = await this.globalSettingService.findByKeyAndRepoName(this.MESSAGE_EVENT_JOB);
        // const validate = this.env !== 'DEVELOPMENT' && messageEventJob === 'ON' && this.REPO_NAME === this.REPO_NAME_VALID;
        // if (validate) {
        return this.eventService.handleMessageEventNotifTelemedNow();
        // }
    }

    @Cron('*/2 * * * * *')
    async handleMessageProccessNotifTelemedNow() {
        // const messageEventJob = await this.globalSettingService.findByKeyAndRepoName(this.MESSAGE_EVENT_JOB);
        // const validate = this.env !== 'DEVELOPMENT' && messageEventJob === 'ON' && this.REPO_NAME === this.REPO_NAME_VALID;
        // if (validate) {
        return this.eventService.handleMessageProcessEventNotifTelemed();
        // }
    }

    @Cron('*/6 * * * * *')
    async handleMessageEventBookingNewNotice() {
        // const messageEventJob = await this.globalSettingService.findByKeyAndRepoName(this.MESSAGE_EVENT_JOB);
        // const validate = this.env !== 'DEVELOPMENT' && messageEventJob === 'ON' && this.REPO_NAME === this.REPO_NAME_VALID;
        // if (validate) {
        return this.eventService.handleMessageEventBookingNewNotice();
        // }
    }

    @Cron('*/4 * * * * *')
    async handleMessageProccessEventBookingNewNotice() {
        // const messageEventJob = await this.globalSettingService.findByKeyAndRepoName(this.MESSAGE_EVENT_JOB);
        // const validate = this.env !== 'DEVELOPMENT' && messageEventJob === 'ON' && this.REPO_NAME === this.REPO_NAME_VALID;
        // if (validate) {
        return this.eventService.handleMessageEventProcessBookingNewNotice();
        // }
    }

    @Cron('*/6 * * * * *')
    async handleMessageEventMedproDoctor() {
        // const messageEventJob = await this.globalSettingService.findByKeyAndRepoName(this.MESSAGE_EVENT_JOB);
        // const validate = this.env !== 'DEVELOPMENT' && messageEventJob === 'ON' && this.REPO_NAME === this.REPO_NAME_VALID;
        // if (validate) {
        return this.eventService.handleMessageEventMedproDoctor();
        // }
    }

    @Cron('*/6 * * * * *')
    async handleMessageEventcta() {
        // const messageEventJob = await this.globalSettingService.findByKeyAndRepoName(this.MESSAGE_EVENT_JOB);
        // const validate = this.env !== 'DEVELOPMENT' && messageEventJob === 'ON' && this.REPO_NAME === this.REPO_NAME_VALID;
        // if (validate) {
        return this.eventService.handleMessageEventCTA();
        // }
    }

    @Cron('*/4 * * * * *')
    async handleMessageProccessEventMedproDoctor() {
        // const messageEventJob = await this.globalSettingService.findByKeyAndRepoName(this.MESSAGE_EVENT_JOB);
        // const validate = this.env !== 'DEVELOPMENT' && messageEventJob === 'ON' && this.REPO_NAME === this.REPO_NAME_VALID;
        // if (validate) {
        return this.eventService.handleMessageEventProcessMedproDoctor();
        // }
    }

    @Cron('*/4 * * * * *')
    async handleMessageProccessCTADoctor() {
        // const messageEventJob = await this.globalSettingService.findByKeyAndRepoName(this.MESSAGE_EVENT_JOB);
        // const validate = this.env !== 'DEVELOPMENT' && messageEventJob === 'ON' && this.REPO_NAME === this.REPO_NAME_VALID;
        // if (validate) {
        return this.eventService.handleMessageEventProcessCTA();
        // }
    }


    @Cron('*/4 * * * * *')
    async handleMessageEventNotifInform() {
        // const messageEventJob = await this.globalSettingService.findByKeyAndRepoName(this.MESSAGE_EVENT_JOB);
        // const validate = this.env !== 'DEVELOPMENT' && messageEventJob === 'ON' && this.REPO_NAME === this.REPO_NAME_VALID;
        // if (validate) {
        return this.eventService.handleMessageEventNotifInform();
        // }
    }

    @Cron('*/4 * * * * *')
    async handleMessageProcessEventNotifInform() {
        // const messageEventJob = await this.globalSettingService.findByKeyAndRepoName(this.MESSAGE_EVENT_JOB);
        // const validate = this.env !== 'DEVELOPMENT' && messageEventJob === 'ON' && this.REPO_NAME === this.REPO_NAME_VALID;
        // if (validate) {
        return this.eventService.handleMessageProcessEventNotifInform();
        // }
    }

    @Cron(CronExpression.EVERY_DAY_AT_11PM)
    async pushBackgroundRemindExamBooking(): Promise<void> {
        // const config = await this.globalSettingService.findByKeyAndRepoName('CONFIG_BACKGROUND_PUSH_REMIND_EXAM');
        // const configObj = config ? JSON.parse(config) : null;
        // const setRepo = new Set(configObj?.repo || []);
        // if (setRepo.has(this.REPO_NAME) || configObj?.env === 'ON') {
        return this.messageEvent.pushBackgroundRemindExamBooking();
        // }
    }

    @Cron(CronExpression.EVERY_5_MINUTES)
    async removeInvalidMessageEvent(): Promise<void> {
        return this.messageEvent.removeInvalidMessageEvent();
    }

    @Cron(CronExpression.EVERY_DAY_AT_11PM)
    async pushBookingNotif(): Promise<void> {
        // if (this.REPO_NAME === 'api-v2-beta') {
        return this.eventService.pushBookingNotif({ vdate: '', bookingCode: '' })
        // }
    }

    @Cron(CronExpression.EVERY_DAY_AT_9PM)
    async pushBookingNotifPartnerV2(): Promise<any> {
        return this.eventService.pushBookingNotifPartnerV2({});
    }

    @Cron(CronExpression.EVERY_DAY_AT_2AM)
    async pushNotifSurveyFormNhidong1(): Promise<any> {
        return this.messageEvent.pushNotifSurveyFormPartner({});
    }

    @Cron(CronExpression.EVERY_DAY_AT_8PM)
    async pushSurvey(): Promise<any> {
        return this.eventService.pushSurveyFormUmc1();
    }

    @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
    async pushZnsBooking(): Promise<any> {
        return this.messageEvent.pushZnsBookingStored();
    }

    // EVERY_DAY_AT_2AM = "0 02 * * *",
    @Cron("30 04 * * *")
    async pushNotifTelemedNow(): Promise<any> {
        this.logger.warn('pushNotifTelemedNow: THời gian nhắc NOtif Telemed sau 3 ngày ')
        return this.eventService.pushNotifTelemedNow({ });
    }

    @Cron("59 16 * * *")
    async pushNotifRemindConfirmBookingTelemed(): Promise<any> {
        return this.messageEvent.pushNotifRemindConfirmBookingTelemed({});
    }

    // 10AM (UTC+7)
    @Cron(CronExpression.EVERY_DAY_AT_3AM)
    async pushDoctorNotLoggedInWarning() {
        await this.eventService.pushDoctorNotLoggedInWarning();
    }

    @Cron('0 */2 * * * *')
    async removeDoctorTelemedDescriptionNotActive() {
        await this.serviceMongoService.removeDoctorTelemedDescriptionNotActive({});
    }

    @Cron('0 */30 * * * *')
    async setCacheApi() {
        await this.messageEvent.setCacheApi();
    }

    @Cron(CronExpression.EVERY_DAY_AT_11PM)
    async pushNotifResultExamAfterExam(): Promise<void> {
        await this.eventService.notifyViewExamResult();
    }

    @Cron('*/2 * * * * *')
    async handleUserBookingChorayNotif() {
        return this.eventService.handleUserBookingChorayNotif();
    }

    @Cron('*/2 * * * * *')
    async handleMessageEventTckqcr() {
        return this.eventService.handleMessageEventTckqcr();
    }

    @Cron('*/2 * * * * *')
    async handleMessageEventProcessTckqcr() {
        return this.eventService.handleMessageEventProcessTckqcr();
    }

    @Cron('*/2 * * * * *')
    async handleViewNotifAndUpdateTckqChoray() {
        return this.eventService.handleViewNotifAndUpdateTckqChoray();
    }

    @Cron(CronExpression.EVERY_DAY_AT_8PM)
    async syncYesterdayNotifTckqChoray(): Promise<any> {
        return this.eventService.syncYesterdayNotifTckqChoray();
    }

    // @Cron(CronExpression.EVERY_DAY_AT_7AM)
    // async seedKpiCskh() {
    //     return this.eventService.seedKpiCskh();
    // }

    @Cron('*/2 * * * * *')
    async handleUpdateKpiCskhPayment() {
        return this.eventService.handleUpdateKpiCskhPayment();
    }
}
