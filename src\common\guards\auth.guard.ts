import { Injectable, CanActivate, ExecutionContext, HttpException, HttpStatus } from '@nestjs/common';
import { UrlConfigService } from 'src/config/config.url.service';

@Injectable()
export class AuthMedproAdminGuard implements CanActivate {
    constructor(private urlConfigService: UrlConfigService) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const { secretKey } = request.query;
        const validate = secretKey === this.urlConfigService.getSecretKey();
        if (!validate) {
            throw new HttpException(`Thông tin xác thực gửi lên không chính xác. Vui lòng kiểm tra lại!`, HttpStatus.FORBIDDEN);
        }
        return true;
    }
}
