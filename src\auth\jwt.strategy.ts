import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import { JwtAdminConfigService } from 'src/config/config.admin.jwt.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
    constructor(
        private readonly jwtAdminConfigService: JwtAdminConfigService,
    ) {
        super({
            jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: jwtAdminConfigService.get<string>('ADMIN_JWT_SECRET'),
        });
    }

    async validate(payload: any) {
        return { id: payload.sub, email: payload.email };
    }
}
