import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { FEATURE_COLLECTION_NAME } from 'src/feature-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const FeatureInAppSchema = new Schema(
    {
        name: String,
        image: String,
        priority: Number,
        status: Boolean,
        mobileStatus: Boolean,
        type: { type: String },
        message: { type: String, default: '' },
        disabled: { type: Boolean, default: false },
        children: [
            new Schema({
                parentId: String,
                name: String,
                image: String,
                priority: Number,
                status: Boolean,
                mobileStatus: Boolean,
                type: String,
                message: String,
                disabled: Boolean,
                webRoute: String,
                mobileRoute: String,
            }),
        ],
        mobileIcon: { type: String },
        webRoute: { type: String },
        mobileRoute: { type: String },
    },
    { timestamps: true },
).plugin(jsonMongo);
