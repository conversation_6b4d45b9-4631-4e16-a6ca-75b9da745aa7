import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { NEW_BILL_LOG_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const NewBillLogSchema = new Schema({
    id: { type: String },
    transactionId: { type: String },
    requestBody: { type: Object },
}, {
    collection: NEW_BILL_LOG_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
