import { Controller, Get, Headers } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { CountryMongoService } from './country-mongo.service';

@Controller('country-mongo')
@ApiTags('Quốc gia - <PERSON>uản lý danh mục Quốc gia trên MongoDB')
export class CountryMongoController {
    constructor(
        private readonly countryMongoService: CountryMongoService,
    ) { }

    @Get('get-all-by-partner')
    async find(): Promise<any> {
        return this.countryMongoService.find();
    }

    @Get('seed-data')
    async seed(@Headers('partnerid') partnerid: string): Promise<any> {
        return this.countryMongoService.seed(partnerid);
    }

    @Get('booking-xnc')
    async filterByBookingXnc(): Promise<any> {
        return this.countryMongoService.filterByBookingXnc();
    }
}
