import { Injectable, Inject } from '@nestjs/common';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';
import { OutDoctorsDTO } from './dto/outDoctor.dto';

@Injectable()
export class DoctorService {

    private tableName = 'doctor';

    constructor(
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
    ) { }

    async allDoctors(): Promise<OutDoctorsDTO> {
        const list = await this.pkhPatientKnex(this.tableName)
            .select(`id`, 'name as fullname', 'name_only as name', 'role', 'sex as gender', 'avatar', 'lichkham as workingDay');
        return { list };
    }
    async allTeleMedicineDoctors(): Promise<OutDoctorsDTO> {
        const list = await this.pkhPatientKnex(this.tableName)
            .select(`id`, 'name as fullname', 'name_only as name', 'role', 'sex as gender', 'avatar', 'lichkham as workingDay');
        return { list };
    }

}
