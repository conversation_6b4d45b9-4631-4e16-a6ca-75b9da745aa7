import { ApiProperty } from '@nestjs/swagger';
import { PageOptionnsDto } from 'src/common/base/page-options.dto';
import { Transform } from 'class-transformer';

export class BookigPageOptionsDto extends PageOptionnsDto {
    @ApiProperty({ description: 'Mã giao dịch'})
    @Transform(value => `${value}`.trim())
    transactionId?: string;

    @ApiProperty({ description: 'id booking'})
    @Transform(value => `${value}`.trim())
    id?: string;

    @ApiProperty({ description: 'Mã phiếu'})
    @Transform(value => `${value}`.trim())
    bookingCode?: string;

    @ApiProperty({ description: 'Mã sms'})
    @Transform(value => `${value}`.trim())
    smsCode?: string;

    @ApiProperty({ description: 'Parnter Id'})
    @Transform(value => `${value}`.trim())
    partnerId?: string;

    @ApiProperty({ description: 'App Id'})
    @Transform(value => `${value}`.trim())
    appId?: string;
}
