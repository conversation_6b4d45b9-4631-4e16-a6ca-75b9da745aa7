import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';
import * as admin2 from 'firebase-admin';
import { FirebaseOptions } from './firebaseConnectionSecond';

@Injectable()
export class FirebaseUMCConfigService extends ConfigManager {
    provideConfigSpec() {
        return {
            FIREBASE_PROJECT_ID_UMC: {
                validate: Joi.string(),
                required: true,
            },
            FIREBASE_PRIVATE_KEY_UMC: {
                validate: Joi.string(),
                required: true,
            },
            FIREBASE_CLIENT_EMAIL_UMC: {
                validate: Joi.string(),
                required: true,
            },
            FIREBASE_DATABASE_URL_UMC: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    createServiceAccount(): FirebaseOptions {
        return {
            credential: admin2.credential.cert({
                projectId: this.get<string>('FIREBASE_PROJECT_ID_UMC'),
                privateKey: this.get<string>('FIREBASE_PRIVATE_KEY_UMC').replace(/\\n/g, '\n'), /* _______________ */
                clientEmail: this.get<string>('FIREBASE_CLIENT_EMAIL_UMC'),
            }),
            databaseURL: this.get<string>('FIREBASE_DATABASE_URL_UMC'),
        };
    }
}
