
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class BookingSlotIdQueryStringDTO {

    @ApiProperty({
        description: 'Booking Slot String Value',
        required: true,
        type: String,
        default: 'service0002_2020-05-13T00:00:00.000Z_2020-05-13T08:00:00.000Z_2020-05-13T08:30:00.000Z_111111',
    })
    @Transform(value => `${value}`.trim())
    bookingSlotId: string;

}
