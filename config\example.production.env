

SMS_CHECK_PHONE=https://alpha-api.medpro.com.vn/api/user/check_phone
SMS_CHECK_CODE=https://alpha-api.medpro.com.vn/api/user/check_code

PKH_PATIENT_HOST=*************
PKH_PATIENT_PORT=33066
PKH_PATIENT_DATABASE=medprodb
PKH_PATIENT_USER=root
PKH_PATIENT_PASSWORD=abc123!!!

PKH_ADMIN_HOST=*************
PKH_ADMIN_PORT=33066
PKH_ADMIN_DATABASE=medproadmindb
PKH_ADMIN_USER=root
PKH_ADMIN_PASSWORD=abc123!!!

HOSPITAL_HOST=*************
HOSPITAL_PORT=33066
HOSPITAL_DATABASE=medpro_thuduc
HOSPITAL_USER=root
HOSPITAL_PASSWORD=abc123!!!

ZALO_URL_ACCESS_TOKEN=https://oauth.zaloapp.com/v3/access_token
ZALO_URL_GET_INFO=https://graph.zalo.me/v2.0/me
ZALO_APP_ID=1301505877331454263
ZALO_APP_SECRET=2E32LLX2vAVLytRON1VV

ADMIN_JWT_SECRET=adminpkh123!?
ADMIN_JWT_EXPIRES_IN=100y

USER_JWT_SECRET=userpkh123!?
USER_JWT_EXPIRES_IN=100y

FIREBASE_PROJECT_ID=bvthuducdev
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_DATABASE_URL=https://bvthuducdev.firebaseio.com

SENDGRID_API_KEY=*********************************************************************
SENDGRID_EMAIL_RECEIVE_ERROR_1=<EMAIL>
SENDGRID_EMAIL_RECEIVE_ERROR_2=<EMAIL>
SENDGRID_EMAIL_RECEIVE_ERROR_3=<EMAIL>
SENDGRID_EMAIL_RECEIVE_ERROR_4=<EMAIL>