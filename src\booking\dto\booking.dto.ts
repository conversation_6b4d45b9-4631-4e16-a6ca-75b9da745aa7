import { ApiProperty } from '@nestjs/swagger';

export class BookingDTO {

    @ApiProperty({
        description: 'Id unique',
        required: true,
        type: Number,
    })
    readonly id: number;

    @ApiProperty({
        description: 'Booking Date',
        required: true,
        type: String,
    })
    readonly bookingDate: string;

    @ApiProperty({
        description: 'Transaction Code',
        required: true,
        type: String,
    })
    readonly transactionCode: string;

    @ApiProperty({
        description: 'Id Thanh toán',
        required: true,
        type: Number,
    })
    readonly paymentId: number;

    @ApiProperty({
        description: 'Id Lịch khám',
        required: true,
        type: Number,
    })
    readonly scheduleId: number;

    @ApiProperty({
        description: 'Id Bệnh nhân',
        required: true,
        type: Number,
    })
    readonly patientId: number;

    @ApiProperty({
        description: 'Id Booking Time ',
        required: true,
        type: Number,
    })
    readonly bookingTimeId: number;

    @ApiProperty({
        description: 'Số thứ tự',
        required: true,
        type: Number,
    })
    readonly bookingNumber: number;

    @ApiProperty({
        description: '<PERSON><PERSON>iện thoại đặt khám',
        required: true,
        type: String,
    })
    readonly bookingPhone: string;

    @ApiProperty({
        description: 'Số bảo hiểm y tế hay không',
        required: true,
        type: Boolean,
    })
    readonly bhytAccept: boolean;

    @ApiProperty({
        description: 'Email',
        required: true,
        type: String,
    })
    readonly email: string;

    @ApiProperty({
        description: 'Trạng thái đặt khám',
        required: true,
        type: Number,
    })
    readonly status: number;

    @ApiProperty({
        description: 'Ngày tạo Đặt khám',
        required: true,
        type: String,
    })
    readonly dateCreate: string;

}
