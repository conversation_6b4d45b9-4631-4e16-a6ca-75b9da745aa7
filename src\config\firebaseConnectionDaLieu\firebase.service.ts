import { Injectable, Inject, Logger } from '@nestjs/common';
import { FIREBASE_SERVICE_ACCOUNT_DA_LIEU } from './constants';
import { FirebaseOptions } from './interfaces';
import * as admin2 from 'firebase-admin';
import * as uuid from 'uuid';

interface IFirebaseService {
    createServiceAccount();
}

@Injectable()
export class FirebaseServiceDa<PERSON><PERSON> implements IFirebaseService {
    private readonly logger: Logger;
    // tslint:disable-next-line: variable-name
    private _firebaseConnectionDaLieu: any;
    // tslint:disable-next-line: variable-name
    constructor(@Inject(FIREBASE_SERVICE_ACCOUNT_DA_LIEU) private _firebaseOptions: FirebaseOptions) {
        this.logger = new Logger('FirebaseService');
        this.logger.log(`Options: ${JSON.stringify(this._firebaseOptions)}`);
    }

    createServiceAccount() {
        if (!this._firebaseConnectionDaLieu) {
            this._firebaseConnectionDaLieu = admin2.initializeApp(this._firebaseOptions, uuid.v4());
        }
        return this._firebaseConnectionDaLieu;
    }
}
