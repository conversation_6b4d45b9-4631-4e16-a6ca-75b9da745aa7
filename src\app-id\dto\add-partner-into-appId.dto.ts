import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

export class AddPartnerIntoAppIdDTO {
    @ApiProperty({ description: 'appId', type: String, required: true })
    @IsNotEmpty({ message: 'appId is required' })
    @Transform(value => `${value}`.trim())
    appId: string;

    @ApiProperty({ description: 'partnerId', type: String, required: true })
    @IsNotEmpty({ message: 'partnerId is required' })
    @Transform(value => `${value}`.trim())
    partnerId: string;
}
