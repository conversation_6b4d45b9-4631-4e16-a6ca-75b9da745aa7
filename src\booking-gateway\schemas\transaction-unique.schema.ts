import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { TRANSACTION_UNIQUE_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const TransactionUniqueSchema = new Schema(
    {
        transactionId: { type: String, unique: true, required: true },
        bookingCode: { type: String },
    },
    { collection: TRANSACTION_UNIQUE_COLLECTION_NAME, timestamps: true, versionKey: false },
).plugin(jsonMongo);
