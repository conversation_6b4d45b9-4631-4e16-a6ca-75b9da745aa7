import { Injectable, Inject, HttpException, HttpStatus } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { COUNTRY_COLLECTION_NAME } from 'src/country-mongo/schemas/constants';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';
import { Model } from 'mongoose';
import { ICity } from 'src/city-mongo/interfaces/city.interface';
import { ICountry } from 'src/country-mongo/interfaces/country.interface';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { DISTRICT_COLLECTION_NAME } from './schemas/constants';
import { IDistrict } from './interfaces/district.interface';

@Injectable()
export class DistrictMongoService {
    constructor(
        @InjectModel(DISTRICT_COLLECTION_NAME) private districtModel: Model<IDistrict>,
        @InjectModel(CITY_COLLECTION_NAME) private cityModel: Model<ICity>,
        @InjectModel(COUNTRY_COLLECTION_NAME) private countryModel: Model<ICountry>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitaltModel: Model<IHospital>,
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
    ) { }

    async find(partnerId: string, cityId: string): Promise<any> {
        // tìm lại code của cities
        const cityInfo = await this.cityModel.findOne({ id: cityId }).exec();
        if (!cityInfo) {
            throw new HttpException('Không tìm thấy thông tin tỉnh thành', HttpStatus.BAD_REQUEST);
        }
        const districts = await this.districtModel
            .find({ partnerId: 'medpro', parent: `${cityInfo.code}`, status: 1 }, { name: 1, id: 1 })
            .sort({ name: 'asc' })
            .exec();
        return districts;
    }

    async seed(partnerId: string): Promise<any> {
        const hospital = await this.hospitaltModel.findOne({ partnerId }).exec();
        const districts = await this.pkhPatientKnex('dm_district');
        for await (const district of districts) {
            const districtM = new this.districtModel({
                code: district.id,
                name: district.name,
                parent: district.city_id,
                status: district.status,
                createTime: 0,
                sourceUpdateTime: 0,
                updateTime: 0,
                partnerId,
                hospitalId: hospital._id,
            });
            await districtM.save();
        }
        return true;
    }
}
