import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as moment from 'moment';
import { 
    CreateNotificationCampaignDto, 
    UpdateNotificationCampaignDto, 
    QueryNotificationCampaignDto,
    QueryNotificationCampaignLogDto 
} from './dto';
import { NOTIFICATION_CAMPAIGN_COLLECTION, NOTIFICATION_CAMPAIGN_LOG_COLLECTION } from './schemas/constants';
import { BOOKING_COLLECTION_NAME } from '../booking-gateway/schemas/constants';
import { MessageEventService } from '../message-event/message-event.service';

@Injectable()
export class NotificationCampaignService {
    private readonly logger = new Logger(NotificationCampaignService.name);

    constructor(
        @InjectModel(NOTIFICATION_CAMPAIGN_COLLECTION) private campaignModel: Model<any>,
        @InjectModel(NOTIFICATION_CAMPAIGN_LOG_COLLECTION) private campaignLogModel: Model<any>,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<any>,
        private messageEventService: MessageEventService,
    ) {}

    // CRUD Operations for Notification Campaign
    async createCampaign(createDto: CreateNotificationCampaignDto): Promise<any> {
        const campaign = new this.campaignModel({
            ...createDto,
            startDate: createDto.startDate ? new Date(createDto.startDate) : undefined,
            endDate: createDto.endDate ? new Date(createDto.endDate) : undefined,
        });
        
        return await campaign.save();
    }

    async updateCampaign(id: string, updateDto: UpdateNotificationCampaignDto): Promise<any> {
        const updateData: any = { ...updateDto };
        
        if (updateDto.startDate) {
            updateData.startDate = new Date(updateDto.startDate);
        }
        if (updateDto.endDate) {
            updateData.endDate = new Date(updateDto.endDate);
        }

        const campaign = await this.campaignModel.findByIdAndUpdate(
            id, 
            updateData, 
            { new: true }
        );

        if (!campaign) {
            throw new NotFoundException('Campaign not found');
        }

        return campaign;
    }

    async deleteCampaign(id: string): Promise<any> {
        const campaign = await this.campaignModel.findByIdAndDelete(id);
        
        if (!campaign) {
            throw new NotFoundException('Campaign not found');
        }

        return { message: 'Campaign deleted successfully' };
    }

    async getCampaign(id: string): Promise<any> {
        const campaign = await this.campaignModel.findById(id);
        
        if (!campaign) {
            throw new NotFoundException('Campaign not found');
        }

        return campaign;
    }

    async getCampaigns(queryDto: QueryNotificationCampaignDto): Promise<any> {
        const { page = 1, limit = 10, search, partnerId, appId, isActive, startDate, endDate } = queryDto;
        const skip = (page - 1) * limit;

        // Build query
        const query: any = {};
        
        if (partnerId) query.partnerId = partnerId;
        if (appId) query.appId = appId;
        if (typeof isActive === 'boolean') query.isActive = isActive;
        
        if (search) {
            query.$or = [
                { name: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } },
                { title: { $regex: search, $options: 'i' } },
                { content: { $regex: search, $options: 'i' } }
            ];
        }

        if (startDate || endDate) {
            query.createdAt = {};
            if (startDate) query.createdAt.$gte = new Date(startDate);
            if (endDate) query.createdAt.$lte = new Date(endDate);
        }

        const [campaigns, total] = await Promise.all([
            this.campaignModel.find(query)
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit)
                .exec(),
            this.campaignModel.countDocuments(query)
        ]);

        return {
            data: campaigns,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit)
            }
        };
    }

    // Campaign Log Operations
    async getCampaignLogs(queryDto: QueryNotificationCampaignLogDto): Promise<any> {
        const { 
            page = 1, 
            limit = 10, 
            campaignId, 
            bookingCode, 
            userId, 
            partnerId, 
            status,
            bookingDateFrom,
            bookingDateTo 
        } = queryDto;
        const skip = (page - 1) * limit;

        // Build query
        const query: any = {};
        
        if (campaignId) query.campaignId = campaignId;
        if (bookingCode) query.bookingCode = bookingCode;
        if (userId) query.userId = userId;
        if (partnerId) query.partnerId = partnerId;
        if (status) query.status = status;

        if (bookingDateFrom || bookingDateTo) {
            query.bookingDate = {};
            if (bookingDateFrom) query.bookingDate.$gte = new Date(bookingDateFrom);
            if (bookingDateTo) query.bookingDate.$lte = new Date(bookingDateTo);
        }

        const [logs, total] = await Promise.all([
            this.campaignLogModel.find(query)
                .populate('campaignId', 'name description')
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit)
                .exec(),
            this.campaignLogModel.countDocuments(query)
        ]);

        return {
            data: logs,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit)
            }
        };
    }

    // Main function to process daily notifications
    async processDailyNotifications(): Promise<any> {
        try {
            this.logger.log('Starting daily notification processing...');
            
            const today = moment().startOf('day');
            const todayEnd = moment().endOf('day');
            const currentDayOfWeek = moment().format('dddd').toLowerCase();

            // Get active campaigns for today
            const activeCampaigns = await this.campaignModel.find({
                isActive: true,
                targetDays: currentDayOfWeek,
                $or: [
                    { startDate: { $exists: false } },
                    { startDate: { $lte: today.toDate() } }
                ],
                $and: [
                    {
                        $or: [
                            { endDate: { $exists: false } },
                            { endDate: { $gte: today.toDate() } }
                        ]
                    }
                ]
            }).exec();

            this.logger.log(`Found ${activeCampaigns.length} active campaigns for today`);

            let totalProcessed = 0;
            let totalSent = 0;
            let totalFailed = 0;

            for (const campaign of activeCampaigns) {
                try {
                    const result = await this.processCampaignForToday(campaign, today.toDate(), todayEnd.toDate());
                    totalProcessed += result.processed;
                    totalSent += result.sent;
                    totalFailed += result.failed;
                } catch (error) {
                    this.logger.error(`Error processing campaign ${campaign._id}:`, error);
                }
            }

            this.logger.log(`Daily notification processing completed. Processed: ${totalProcessed}, Sent: ${totalSent}, Failed: ${totalFailed}`);

            return {
                success: true,
                totalCampaigns: activeCampaigns.length,
                totalProcessed,
                totalSent,
                totalFailed
            };

        } catch (error) {
            this.logger.error('Error in daily notification processing:', error);
            throw error;
        }
    }

    private async processCampaignForToday(campaign: any, todayStart: Date, todayEnd: Date): Promise<any> {
        this.logger.log(`Processing campaign: ${campaign.name} (${campaign._id})`);

        // Find bookings for today that match the campaign criteria
        const bookings = await this.bookingModel.find({
            partnerId: campaign.partnerId,
            appId: campaign.appId,
            date: { $gte: todayStart, $lte: todayEnd },
            status: { $in: [1, 2] }, // Only confirmed bookings
            visible: true
        })
        .populate('user', 'fullname phone')
        .exec();

        this.logger.log(`Found ${bookings.length} bookings for campaign ${campaign.name}`);

        let processed = 0;
        let sent = 0;
        let failed = 0;

        for (const booking of bookings) {
            try {
                // Check if notification already sent for this booking and campaign
                const existingLog = await this.campaignLogModel.findOne({
                    campaignId: campaign._id,
                    bookingId: booking._id.toString(),
                    sentDate: { $gte: todayStart, $lte: todayEnd }
                });

                if (existingLog) {
                    this.logger.log(`Notification already sent for booking ${booking.bookingCode}`);
                    continue;
                }

                // Send notification
                const result = await this.sendNotificationForBooking(campaign, booking);
                
                if (result.success) {
                    sent++;
                } else {
                    failed++;
                }
                
                processed++;

            } catch (error) {
                this.logger.error(`Error processing booking ${booking.bookingCode}:`, error);
                failed++;
                processed++;
            }
        }

        // Update campaign statistics
        await this.campaignModel.findByIdAndUpdate(campaign._id, {
            $inc: {
                totalSent: sent,
                totalFailed: failed
            }
        });

        return { processed, sent, failed };
    }

    private async sendNotificationForBooking(campaign: any, booking: any): Promise<any> {
        try {
            // Create log entry first
            const logEntry = new this.campaignLogModel({
                campaignId: campaign._id,
                bookingId: booking._id.toString(),
                bookingCode: booking.bookingCode,
                userId: booking.userId,
                partnerId: booking.partnerId,
                appId: booking.appId,
                title: campaign.title,
                content: campaign.content,
                url: campaign.url,
                status: 'pending',
                bookingDate: booking.date,
                sentDate: new Date()
            });

            await logEntry.save();

            // Prepare notification payload for pushNotiToUser method
            const notificationPayload = {
                bookingCode: booking.bookingCode,
                titleMessage: campaign.title,
                content: campaign.content,
                topicId: campaign.topicId,
                url: campaign.url
            };

            // Send notification through MessageEventService
            const result = await this.messageEventService.pushNotiToUser(notificationPayload);

            // Update log entry with result
            await this.campaignLogModel.findByIdAndUpdate(logEntry._id, {
                status: 'sent',
                sentAt: new Date()
            });

            this.logger.log(`Notification sent successfully for booking ${booking.bookingCode}`);

            return { success: true, result };

        } catch (error) {
            this.logger.error(`Failed to send notification for booking ${booking.bookingCode}:`, error);

            // Update log entry with error
            await this.campaignLogModel.findOneAndUpdate(
                {
                    campaignId: campaign._id,
                    bookingId: booking._id.toString(),
                    status: 'pending'
                },
                {
                    status: 'failed',
                    error: error.message,
                    errorDetails: error
                }
            );

            return { success: false, error: error.message };
        }
    }

    // Manual trigger for specific campaign
    async triggerCampaign(campaignId: string, targetDate?: string): Promise<any> {
        const campaign = await this.campaignModel.findById(campaignId);

        if (!campaign) {
            throw new NotFoundException('Campaign not found');
        }

        if (!campaign.isActive) {
            throw new Error('Campaign is not active');
        }

        const processDate = targetDate ? moment(targetDate) : moment();
        const dayStart = processDate.startOf('day').toDate();
        const dayEnd = processDate.endOf('day').toDate();

        const result = await this.processCampaignForToday(campaign, dayStart, dayEnd);

        return {
            success: true,
            campaign: {
                id: campaign._id,
                name: campaign.name
            },
            processDate: processDate.format('YYYY-MM-DD'),
            ...result
        };
    }

    // Get campaign statistics
    async getCampaignStats(campaignId: string, dateFrom?: string, dateTo?: string): Promise<any> {
        const campaign = await this.campaignModel.findById(campaignId);

        if (!campaign) {
            throw new NotFoundException('Campaign not found');
        }

        const query: any = { campaignId };

        if (dateFrom || dateTo) {
            query.sentDate = {};
            if (dateFrom) query.sentDate.$gte = new Date(dateFrom);
            if (dateTo) query.sentDate.$lte = new Date(dateTo);
        }

        const [totalLogs, sentLogs, failedLogs, pendingLogs] = await Promise.all([
            this.campaignLogModel.countDocuments(query),
            this.campaignLogModel.countDocuments({ ...query, status: 'sent' }),
            this.campaignLogModel.countDocuments({ ...query, status: 'failed' }),
            this.campaignLogModel.countDocuments({ ...query, status: 'pending' })
        ]);

        return {
            campaign: {
                id: campaign._id,
                name: campaign.name,
                partnerId: campaign.partnerId,
                appId: campaign.appId
            },
            statistics: {
                total: totalLogs,
                sent: sentLogs,
                failed: failedLogs,
                pending: pendingLogs,
                successRate: totalLogs > 0 ? ((sentLogs / totalLogs) * 100).toFixed(2) : 0
            }
        };
    }
}
