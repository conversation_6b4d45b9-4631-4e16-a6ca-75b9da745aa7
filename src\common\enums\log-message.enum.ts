export enum LogMessage {
    TRACKING_USER_LOGIN = 'Login Action',
    TRACKING_USER_CHANGE_PASSWORD = 'User Change Password',
    WRONG_PASSWORD = 'Wrong Password',
    ERROR_CREATE_SESSION_V1 = 'Error Get userId & Create Session V1',
    ERROR_SYNC_USER_ACCOUNT = 'Error Sync User Account',
    ERROR_WHEN_SYNC_TO_V1 = 'Error Sync From V1',
    SECRET_KEY_EXPIRED = 'Secret Key Expired',
    SECRET_KEY_INVALID = 'Secret Key Invalid',
    SECRET_KEY_INACTIVE = 'Secret Key InActive',
    SOME_ERROR = 'Some Error',
    NOT_FOUND = 'Not Found',
}
