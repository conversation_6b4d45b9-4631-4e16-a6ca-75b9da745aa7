import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { NOTIFICATION_CAMPAIGN_COLLECTION } from './constants';

const Schema = mongoose.Schema;

export const NotificationCampaignSchema = new Schema({
    id: { type: String },
    name: { type: String, required: true }, // Tên chiến dịch
    description: { type: String }, // Mô tả chiến dịch
    
    // Cấu hình partner/hospital
    partnerId: { type: String, required: true }, // ID bệnh viện
    appId: { type: String, required: true }, // medpro, care247, etc.
    
    // Cấu hình nội dung notification
    title: { type: String, required: true },
    content: { type: String, required: true },
    url: { type: String, required: true }, // URL điều hướng
    
    // Cấu hình gửi
    topicId: { type: String, default: 'booking.partner.promotion' },
    type: { type: Number, default: 100 }, // Type notification
    typeId: { type: String, default: 'NOTIFICATION' },
    
    // Thời gian gửi (theo giờ UTC+7)
    sendTime: { type: String, default: '07:00' }, // Format: HH:mm
    
    // Điều kiện áp dụng
    isActive: { type: Boolean, default: true },
    startDate: { type: Date }, // Ngày bắt đầu chiến dịch
    endDate: { type: Date }, // Ngày kết thúc chiến dịch
    
    // Cấu hình nâng cao
    targetDays: { type: [String], default: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] }, // Các ngày trong tuần áp dụng
    
    // Thống kê
    totalSent: { type: Number, default: 0 },
    totalSuccess: { type: Number, default: 0 },
    totalFailed: { type: Number, default: 0 },
    
    // Metadata
    createdBy: { type: String },
    updatedBy: { type: String },
}, {
    collection: NOTIFICATION_CAMPAIGN_COLLECTION,
    timestamps: true,
}).plugin(jsonMongo);
