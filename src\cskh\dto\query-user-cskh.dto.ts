import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { Pagination } from 'src/common/base/pagination';

export class QueryUserCskh extends Pagination {
    @ApiProperty({ description: 'Filter theo username', required: false })
    @Transform(value => `${value}`.trim())
    username?: string;

    @ApiProperty({ description: 'Filter theo email', required: false })
    @Transform(value => `${value}`.trim())
    email?: string;

    @ApiProperty({ description: 'Filter theo medproId', required: false })
    @Transform(value => `${value}`.trim())
    medproId?: string;

    @ApiProperty({ description: 'Filter theo họ tên', required: false })
    @Transform(value => `${value}`.trim())
    fullname?: string;

    @ApiProperty({ description: 'IsCS', required: false })
    @Transform(value => `${value}`.trim())
    isCS?: boolean;
}
