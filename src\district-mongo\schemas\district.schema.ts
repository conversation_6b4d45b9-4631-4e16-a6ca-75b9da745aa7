import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { DISTRICT_COLLECTION_NAME } from './constants';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const DistrictSchema = new Schema({
    name: String,
    code: String,
    parent: String,
    status: Number,
    createTime: Number,
    sourceUpdateTime: Number,
    updateTime: Number,
    partnerId: String,
    hospitalId: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
}, {
    collection: DISTRICT_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
