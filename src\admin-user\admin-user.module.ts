import { Module, HttpService, HttpModule } from '@nestjs/common';
import { AdminUserService } from './admin-user.service';
import { AdminUserController } from './admin-user.controller';
import { UserService } from 'src/user/user.service';
import { JwtService, JwtModule } from '@nestjs/jwt';
import { SessionService } from 'src/session/session.service';
import { PassportModule } from '@nestjs/passport';
import { ConfigManagerModule } from '@nestjsplus/config';
import { JwtUserConfigService } from 'src/config/config.user.jwt.service';
import { PhoneLoginService } from 'src/phone-login/phone-login.service';
import { SmsService } from 'src/sms/sms.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
    USER_COLLECTION_NAME,
    SIGNIN_PROVIDER_COLLECTION_NAME,
    CONSTRAINTS_PROVIDER_COLLECTION_NAME,
    CONSTRAINTS_USER_COLLECTION_NAME,
    USER_PROFILE_COLLECTION_NAME,
    HOC_VI_COLLECTION_NAME,
    VI_TRI_CONG_VIEC_COLLECTION_NAME,
    ORG_PROFILE_COLLECTION_NAME,
    REFERRAL_CODE_REGISTER_COLLECTION_NAME,
    USER_APP_COLLECTION_NAME,
} from 'src/user/schemas/constants';
import { UserSchema } from 'src/user/schemas/user.schema';
import { SignInProviderSchema } from 'src/user/schemas/signin-provider.schema';
import { ProviderConstraintSchema } from 'src/user/schemas/provider-constraints.schema';
import { UserConstraintSchema } from 'src/user/schemas/user-constraints.schema';
import { UserProfileSchema } from 'src/user/schemas/user-profile.schema';
import { BOOKING_SEARCH_COLLECTION_NAME, BOOKING_COLLECTION_NAME, BOOKING_ORDER_COLLECTION_NAME, NEW_BILL_LOG_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { BookingSearchSchema } from 'src/booking-gateway/schemas/search-booking.schema';
import { USER_ACCOUNT_COLLECTION_NAME } from 'src/user-account/schemas/constants';
import { UserAccountSchema } from 'src/user-account/schemas/user-account.schema';
import { FilesService } from 'src/files/files.service';
import { HocViSchema } from 'src/user/schemas/hoc-vi.schema';
import { ViTriCongViecSchema } from 'src/user/schemas/vi-tri-cong-viec.schema';
import { BookingSchema } from 'src/booking-gateway/schemas/booking.schema';
import { ReferralCodeService } from 'src/referral-code/referral-code.service';
import { REFERRAL_CODE_COLLECTION_NAME } from 'src/referral-code/schemas/constants';
import { ReferralCodeSchema } from 'src/referral-code/schemas/referral-code.schema';
import { SYNC_DALIEU_PATIENT, SYNC_DALIEU_BOOKING, SYNC_NHI_DONG_1_PATIENT, SYNC_NHI_DONG_1_BOOKING, SYNC_DHYD_PATIENT, SYNC_DHYD_BOOKING } from 'src/event/schemas/constants';
import { SyncDaLieuPatientSchema } from 'src/event/schemas/sync-dalieu-patient.schema';
import { SyncDaLieuBookingSchema } from 'src/event/schemas/sync-dalieu-booking.schema';
import { OrgProfileSchema } from 'src/user/schemas/org-profile.schema';
import { SyncNhiDong1PatientSchema } from 'src/event/schemas/sync-nhidong1-patient.schema';
import { SyncNhiDong1BookingSchema } from 'src/event/schemas/sync-nhidong1-booking.schema';
import { ReferralCodeRegisterSchema } from 'src/user/schemas/referral-code-register';
import {
    PATIENT_COLLECTION_NAME,
    PATIENT_PROFILE_COLLECTION_NAME,
    PATIENT_RELATION_COLLECTION_NAME,
    RELATIVE_TYPE_COLLECTION_NAME,
} from 'src/patient-mongo/schemas/constants';
import { PatientProfileSchema } from 'src/patient-mongo/schemas/patient-profile.schema';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { PartnerConfigSchema } from 'src/partner-config/schemas/partner-config.schema';
import { SyncDHYDPatientSchema } from 'src/event/schemas/sync-dhyd-patient.schema';
import { SyncDHYDBookingSchema } from 'src/event/schemas/sync-dhyd-booking.schema';
import { BookingOrderSchema } from 'src/booking-gateway/schemas/booking-order';
import { NewBillLogSchema } from 'src/booking-gateway/schemas/new-bill-log.schema';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { UserAppSchema } from 'src/user/schemas/user-app.schema';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { UserModule } from '../user/user.module';
import { PatientSchema } from '../patient-mongo/schemas/patient.schema';
import { PatientRelationSchema } from '../patient-mongo/schemas/patient-relation.schema';
import { RelativeSchema } from '../patient-mongo/schemas/relative-mongo.schema';

@Module({
  imports: [
    HttpModule,
    PassportModule.register({ defaultStrategy: 'user-jwt' }),
    JwtModule.registerAsync({
      // imports: [ConfigManagerModule],
      useExisting: JwtUserConfigService,
    }),
    MongooseModule.forFeature([
      { name: USER_COLLECTION_NAME, schema: UserSchema },
      { name: SIGNIN_PROVIDER_COLLECTION_NAME, schema: SignInProviderSchema },
      { name: CONSTRAINTS_PROVIDER_COLLECTION_NAME, schema: ProviderConstraintSchema },
      { name: CONSTRAINTS_USER_COLLECTION_NAME, schema: UserConstraintSchema },
      { name: USER_PROFILE_COLLECTION_NAME, schema: UserProfileSchema },
      { name: ORG_PROFILE_COLLECTION_NAME, schema: OrgProfileSchema },
      { name: BOOKING_SEARCH_COLLECTION_NAME, schema: BookingSearchSchema },
      { name: USER_ACCOUNT_COLLECTION_NAME, schema: UserAccountSchema },
      { name: HOC_VI_COLLECTION_NAME, schema: HocViSchema },
      { name: VI_TRI_CONG_VIEC_COLLECTION_NAME, schema: ViTriCongViecSchema },
      { name: REFERRAL_CODE_REGISTER_COLLECTION_NAME, schema: ReferralCodeRegisterSchema },
      { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
      { name: REFERRAL_CODE_COLLECTION_NAME, schema: ReferralCodeSchema },
      { name: SYNC_DALIEU_PATIENT, schema: SyncDaLieuPatientSchema },
      { name: SYNC_DALIEU_BOOKING, schema: SyncDaLieuBookingSchema },
      { name: SYNC_NHI_DONG_1_PATIENT, schema: SyncNhiDong1PatientSchema },
      { name: SYNC_NHI_DONG_1_BOOKING, schema: SyncNhiDong1BookingSchema },
      { name: SYNC_DHYD_PATIENT, schema: SyncDHYDPatientSchema },
      { name: SYNC_DHYD_BOOKING, schema: SyncDHYDBookingSchema },
      { name: PATIENT_PROFILE_COLLECTION_NAME, schema: PatientProfileSchema },
      { name: PARTNER_CONFIG_COLLECTION_NAME, schema: PartnerConfigSchema },
      { name: BOOKING_ORDER_COLLECTION_NAME, schema: BookingOrderSchema },
      { name: NEW_BILL_LOG_COLLECTION_NAME, schema: NewBillLogSchema },
      // { name: GLOBAL_SETTING_COLLECTION_NAME, schema: GlobalSettingSchema },
      { name: USER_APP_COLLECTION_NAME, schema: UserAppSchema },
      { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
      { name: PATIENT_COLLECTION_NAME, schema: PatientSchema },
      { name: PATIENT_RELATION_COLLECTION_NAME, schema: PatientRelationSchema },
      { name: RELATIVE_TYPE_COLLECTION_NAME, schema: RelativeSchema },
    ]),
  ],
  providers: [AdminUserService, UserService, SessionService, PhoneLoginService, SmsService, FilesService, ReferralCodeService],
  exports: [AdminUserService],
  controllers: [AdminUserController],
})

export class AdminUserModule { }
