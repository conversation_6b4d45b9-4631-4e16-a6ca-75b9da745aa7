import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';
import * as moment from 'moment';

@Injectable()
export class PrefixTransactionCodeTTConfigService extends ConfigManager {

    provideConfigSpec() {
        return {
            PREFIX_TRANSACTION_CODE_TT: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    getPrefixTransactionCodeTT(text: string, prefix?: string): string {
        const dateFormat = moment().add(7, 'hours').format('YYMMDD');
        const pre = prefix ? prefix : this.get<string>('PREFIX_TRANSACTION_CODE_TT');
        return `${pre}${dateFormat}${text}`;
    }
}
