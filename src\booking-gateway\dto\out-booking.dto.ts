
import { ApiProperty } from '@nestjs/swagger';

export class OutBookingDTO {

    @ApiProperty({
        description: 'Id Dịch vụ',
        required: true,
        type: String,
    })
    readonly serviceId: string;
    @ApiProperty({
        description: 'Id Booking',
        required: true,
        type: String,
    })
    readonly bookingId: string;
    @ApiProperty({
        description: 'Id Booking Slot',
        required: true,
        type: String,
    })
    readonly bookingSlotId: string;
    @ApiProperty({
        description: 'Số thứ tự khám',
        required: true,
        type: String,
    })
    readonly sequenceNumber: number;
    @ApiProperty({
        description: 'Thời gian bắt đầu khám',
        required: true,
        type: String,
    })
    readonly date: Date;
    @ApiProperty({
        description: 'Loại bảo hiểm',
        required: true,
        type: String,
    })
    readonly insuranceType: string;
    @ApiProperty({
        description: 'Trạng thái booking',
        required: true,
        type: String,
    })
    readonly status: number;
    @ApiProperty({
        description: 'Mã lỗi',
        required: true,
        type: String,
    })
    readonly errorCode: number;
    @ApiProperty({
        description: 'Miêu tả lỗi',
        required: true,
        type: String,
    })
    readonly errorDescription: string;
    @ApiProperty({
        description: 'Id thanh toán',
        required: true,
        type: String,
    })
    readonly paymentId: string;
    @ApiProperty({
        description: 'Thông tin bệnh nhân',
        required: true,
        type: String,
    })
    readonly patient: string;
    @ApiProperty({
        description: 'Thông tin thay đổi booking',
        required: true,
        type: String,
    })
    readonly changeTo: string;
}
