import { Injectable } from '@nestjs/common';
import { MulterModuleOptions, MulterOptionsFactory } from '@nestjs/platform-express';
import * as GridFsStorage from 'multer-gridfs-storage';
import { ConfigManager } from '@nestjsplus/config';
import * as <PERSON><PERSON> from 'joi';
import { ConfigModuleOptions } from '@nestjsplus/config/dist/interfaces';
@Injectable()
export class ConfigGridFSMullterService extends ConfigManager implements MulterOptionsFactory {
    gridFsStorage: GridFsStorage;
    constructor(options: ConfigModuleOptions) {
        super(options);
        this.gridFsStorage = new GridFsStorage({
            url: this.getUri(),
            file: (req, file) => {
                return new Promise((resolve, reject) => {
                    const filename = file.originalname.trim();
                    const fileInfo = {
                        filename,
                    };
                    resolve(fileInfo);
                });
            },
        });
    }

    createMulterOptions(): MulterModuleOptions {
        return {
            storage: this.gridFsStorage,
        };
    }

    getMongoConfig() {
        return {
            username: this.get<string>('MONGO_USERNAME'),
            password: this.get<string>('MONGO_PASSWORD'),
            host: this.get<string>('MONGO_HOST'),
            port: this.get<string>('MONGO_PORT'),
            database: this.get<string>('MONGO_DATABASE'),
        };
    }

    getUri(): string {
        const { username, password, host, port, database } = this.getMongoConfig();
        const uri = `mongodb://${!!username && !!password ? `${username}:${encodeURIComponent(password)}@` : ``}${host}:${port}/${database}?authSource=admin`;
        return uri;
    }

    provideConfigSpec() {
        return {
            MONGO_HOST: {
                validate: Joi.string(),
                required: true,
            },
            MONGO_PORT: {
                validate: Joi.number(),
                required: true,
                default: 27018,
            },
            MONGO_DATABASE: {
                validate: Joi.string(),
                required: true,
                default: 'pkhsuper',
            },
        };
    }

}
