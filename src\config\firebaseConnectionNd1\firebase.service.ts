import { Injectable, Inject, Logger } from '@nestjs/common';
import { FIREBASE_SERVICE_ACCOUNT_ND1 } from './constants';
import { FirebaseOptions } from './interfaces';
import * as admin3 from 'firebase-admin';
import * as uuid from 'uuid';

interface IFirebaseService {
    createServiceAccount();
}

@Injectable()
export class FirebaseServiceNd1 implements IFirebaseService {
    private readonly logger: Logger;
    // tslint:disable-next-line: variable-name
    private _firebaseConnectionNd1: any;
    // tslint:disable-next-line: variable-name
    constructor(@Inject(FIREBASE_SERVICE_ACCOUNT_ND1) private _firebaseOptions: FirebaseOptions) {
        this.logger = new Logger('FirebaseService');
        this.logger.log(`Options: ${JSON.stringify(this._firebaseOptions)}`);
    }

    createServiceAccount() {
        if (!this._firebaseConnectionNd1) {
            this._firebaseConnectionNd1 = admin3.initializeApp(this._firebaseOptions, uuid.v4());
        }
        return this._firebaseConnectionNd1;
    }
}
