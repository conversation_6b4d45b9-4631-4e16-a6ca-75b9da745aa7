import { Document } from 'mongoose';
import { ICheckFilter } from 'src/filter-process/intefaces/check-filter.inteface';
import { IPatientXc } from 'src/patient-mongo/intefaces/patient-xc.interface';
import { ITelemedBookingInfo } from './telemed-booking-info.interface';
import { MedproCare } from './medpro-care.interface';

export interface IBooking extends Document {
    id?: string;
    readonly bookingId: string;
    bookingCode: string;
    bookingCodeV1: string;
    readonly bookingSlotId?: string;
    bookingSlot?: string;
    sequenceNumber: number;
    readonly date?: Date;
    readonly bookingChangeTime?: Date;
    readonly prevBookingCode?: string;
    readonly nextBookingCode?: string;
    readonly subjectId: string;
    subject?: string;
    readonly roomId: string;
    room?: string;
    readonly doctorId: string;
    doctor?: string;
    serviceId: string;
    service?: string;
    sectionId?: string;
    section?: string;
    readonly insuranceType: string;
    readonly status: number;
    paymentStatus?: number;
    paymentMessage?: string;
    transactionId?: string;
    readonly errorCode: number;
    readonly errorDescription: string;
    readonly paymentId: string;
    readonly patientId: string;
    patientVersionId: string;
    patient: string;
    patientVersion: string;
    readonly changeTo: string;
    partnerId?: string;
    appId?: string;
    partner?: string;
    platform?: string;
    invoiceId?: string;
    invoiceCode?: string;
    userId?: string;
    user?: string;
    prevUserId: string;
    insuranceCode?: string;
    insuranceChoice?: string;
    insuranceTransferCode?: string;
    visible: boolean;
    checkInRoom: object;
    syncStatus?: string;
    bookingInternalId?: string;
    syncDate?: Date;
    syncAt?: Date;
    bookingNote?: string;
    noPayment: boolean;
    sharePayment: boolean;
    serviceType: string;
    idReExam: string;
    syncBookingType: number;
    syncBookingIdV1: number;
    syncPatientIdV1: number;
    syncUserIdV1: number;
    patientNameV1: string;
    patientPhoneV1: string;
    patientMSBNV1: string;
    /*cskh */
    cskhUserId: string;
    cskh: string;
    createdAt?: Date;
    /* patient profile */
    patientProfile?: string;
    vatInvoice?: string;
    vatInvoiceData?: {
        companyName?: string;
        taxIdNumber?: string;
        address?: string;
    };
    extraInfo?: {
        transactionIdV1: string;
        methodIdV1: number;
        booking: object;

    };
    referralCode?: string;
    orderId?: string;
    canceledDate?: Date;
    bookingOrder?: string;
    bookingOrderId?: string;
    bookingsRelation?: [
        {
            idBooking: string,
            info: object,
        }
    ];
    bookingExpiredData: object;
    treeId?: string;
    updatedAt?: Date;
    insuranceFileUrl?: string;
    filterCheckData?: ICheckFilter[];
    addonServices?: any;
    serviceInfo?: any;
    smsCode: string;
    promotion?: boolean;
    relatePromotion?: string;
    promotionChilds?: string[];
    xcInfo?: IPatientXc;
    secretBooking?: string;
    endTime?: Date;
    numberConfirmed?: boolean;
    isChanged?: boolean;
    implementAgent?: string;
    implementLocation?: string;
    telemedInfoBooking?: ITelemedBookingInfo;
    optionBHYT?: number;
    locale?: string;
    refundData : {
        title: string;
        content: string;
        date: Date;
    };
    medproCare: MedproCare;
    care247: MedproCare;
    cancelledInfo: any;
    cancelledBy: string;
    userPortalCancelBooking: string;
    countDoanhSo: number;
}
