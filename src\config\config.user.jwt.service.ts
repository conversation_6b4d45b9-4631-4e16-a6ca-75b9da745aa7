import { JwtOptionsFactory, JwtModuleOptions } from '@nestjs/jwt';
import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Joi from 'joi';

@Injectable()
export class JwtUserConfigService extends ConfigManager implements JwtOptionsFactory {

    provideConfigSpec() {
        return {
            USER_JWT_SECRET: {
                validate: Joi.string(),
                required: true,
            },
            USER_JWT_EXPIRES_IN: {
                validate: Joi.string(),
                required: true,
            },
            JWT_ADD_PATIENT_USER: {
                validate: Joi.string(),
                required: true,
            },
            JWT_ADD_PATIENT_USER_EXPIRES_IN: {
                validate: Joi.string(),
                required: true,
            },
            JWT_MOMO_USER: {
                validate: Joi.string(),
                required: true,
            },
            JWT_MOMO_USER_EXPIRES_IN: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    createJwtOptions(): JwtModuleOptions {
        return {
            secret: this.get<string>('USER_JWT_SECRET'),
            signOptions: { expiresIn: this.get<string>('USER_JWT_EXPIRES_IN') },
        };
    }

    verifyPhoneJwtOptions(): JwtModuleOptions {
        return {
            secret: this.get<string>('JWT_ADD_PATIENT_USER'),
            signOptions: { expiresIn: this.get<string>('JWT_ADD_PATIENT_USER_EXPIRES_IN') },
        };
    }

    verifyCSKHJwtOptions(): JwtModuleOptions {
        return {
            secret: this.get<string>('JWT_CSKH_PATIENT_USER'),
            signOptions: { expiresIn: this.get<string>('JWT_CSKH_PATIENT_USER_EXPIRES_IN') },
        };
    }

    verifyChangePINJwtOptions(): JwtModuleOptions {
        return {
            secret: this.get<string>('JWT_CHANGE_PIN'),
            signOptions: { expiresIn: this.get<string>('JWT_CHANGE_PIN_EXPIRES_IN') },
        };
    }

    verifyMomoJwtOptions(): JwtModuleOptions {
        return {
            secret: this.get<string>('JWT_MOMO_USER'),
            signOptions: { expiresIn: this.get<string>('JWT_MOMO_USER_EXPIRES_IN') },
        };
    }
}
