import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Joi from 'joi';

@Injectable()
export class FeeConfigService extends ConfigManager {

    provideConfigSpec() {
        return {
            FEE_GET_SECURITY_URL: {
                validate: Joi.string(),
                required: true,
            },
            PARTNER_URL: {
                validate: Joi.string(),
                required: true,
            },
            FEE_GET_FEE_URL: {
                validate: Joi.string(),
                required: true,
            },
            GET_PATIENT_BY_ID_URL: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    getUrlGenerateSecurityParams(): string {
        return this.get<string>('FEE_GET_SECURITY_URL');
    }

    getFeeUrl(): string {
        const partnerURL = this.get<string>('PARTNER_URL');
        const feeUrl = this.get<string>('FEE_GET_FEE_URL');
        return `${partnerURL}${feeUrl}`;
    }

    getPatientByIdUrl(): string {
        const partnerURL = this.get<string>('PARTNER_URL');
        const getPatientById = this.get<string>('GET_PATIENT_BY_ID_URL');
        return `${partnerURL}${getPatientById}`;
    }
}
