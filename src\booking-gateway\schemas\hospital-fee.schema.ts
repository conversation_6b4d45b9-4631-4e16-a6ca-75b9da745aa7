import * as mongoose from 'mongoose';
import * as json<PERSON>ongo from '@meanie/mongoose-to-json';
import { HOSPITAL_FEE_COLLECTION_NAME } from './constants';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const HospitalFeeSchema = new Schema({
    id: { type: String },
    bill_id: { type: String },
    fee_code: { type: String },
    amount: { type: Number },
    bv_id: { type: String },
    patientEMRNo: { type: String },
    fullname: { type: String },
    sex: { type: Number },
    birthdate: { type: String },
    subject_id: { type: Number },
    subject_name: { type: String },
    typeId: { type: Number },
    mobile: { type: String },
    email: { type: String },
    content: { type: String },
    place: { type: String },
    number: { type: Number },
    status: { type: Number },
    is_sent: { type: Number },
    message: { type: String },
    transactionId: { type: String },
    paymentStatus: { type: Number },
    paymentMessage: { type: String },
    userId: { type: String },
    appId: { type: String },
    partnerId: { type: String },
    partner: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
    platform: { type: String },
    invoiceId: { type: String, default: '' },
    invoiceCode: { type: String, default: '' },
    requestLog: { type: String, default: '' },
    responseLog: { type: String, default: '' },
    isSyncPartner: { type: Boolean, default: false },
    actionType: { type: String },
}, {
    collection: HOSPITAL_FEE_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
