import { Module, DynamicModule, Provider, Global } from '@nestjs/common';
import { FirebaseServiceNd1 } from './firebase.service';
import { FIREBASE_SERVICE_ACCOUNT_ND1 } from './constants';
import {
    FirebaseOptions,
    FireBaseAsyncServiceAccount,
    FirebaseOptionsFactory,
} from './interfaces';
import { createFirebaseNd1Providers } from './firebase.providers';

import { connectionFactory } from './firebase-connection.provider';

@Global()
@Module({
    providers: [FirebaseServiceNd1, connectionFactory],
    exports: [FirebaseServiceNd1, connectionFactory],
})
export class FirebaseAdminNd1Module {
    /**
     * Registers a configured NestKnex Module for import into the current module
     */
    public static register(options: FirebaseOptions): DynamicModule {
        return {
            module: FirebaseAdminNd1Module,
            providers: createFirebaseNd1Providers(options),
        };
    }

    /**
     * Registers a configured NestKnex Module for import into the current module
     * using dynamic options (factory, etc)
     */
    public static registerAsync(options: FireBaseAsyncServiceAccount): DynamicModule {
        return {
            module: FirebaseAdminNd1Module,
            providers: [...this.createProviders(options)],
        };
    }

    private static createProviders(options: FireBaseAsyncServiceAccount): Provider[] {
        if (options.useExisting || options.useFactory) {
            return [this.createOptionsProvider(options)];
        }

        return [
            this.createOptionsProvider(options),
            {
                provide: options.useClass,
                useClass: options.useClass,
            },
        ];
    }

    private static createOptionsProvider(options: FireBaseAsyncServiceAccount): Provider {
        if (options.useFactory) {
            return {
                provide: FIREBASE_SERVICE_ACCOUNT_ND1,
                useFactory: options.useFactory,
                inject: options.inject || [],
            };
        }

        // For useExisting...
        return {
            provide: FIREBASE_SERVICE_ACCOUNT_ND1,
            useFactory: async (optionsFactory: FirebaseOptionsFactory) =>
                await optionsFactory.createServiceAccount(),
            inject: [options.useExisting || options.useClass],
        };
    }
}
