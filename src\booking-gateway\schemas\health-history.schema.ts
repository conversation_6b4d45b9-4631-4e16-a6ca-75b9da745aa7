import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { HEALTH_HISTORY_COLLECTION_NAME } from './constants';
import { DOCTOR_COLLECTION_NAME } from 'src/doctor-mongo/schemas/constants';
import { PATIENT_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const HealthHistorySchema = new Schema(
    {
        id: { type: String },
        userId: { type: String },
        partnerId: { type: String },
        examId: { type: String },

        patientId: { type: String },
        doctorName: { type: String },
        date: { type: Date },
        indication: { type: String },
        reExamDate: { type: Date },
        note: { type: String },
        doctor: { type: Schema.Types.ObjectId, ref: DOCTOR_COLLECTION_NAME },
        patient: { type: Schema.Types.ObjectId, ref: PATIENT_COLLECTION_NAME },
        createTime: { type: Date },
    },
    {
        collection: HEALTH_HISTORY_COLLECTION_NAME,
        timestamps: true,
    },
).plugin(jsonMongo);
