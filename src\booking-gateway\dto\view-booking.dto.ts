
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

export class ViewBookingCSDTO {

    @ApiProperty({
        description: 'Id của booking v2',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    id?: string;

    @ApiProperty({
        description: 'booking code',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    bookingCode?: string;

    @ApiProperty({
        description: 'TransactionId',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    transactionId?: string;

    @ApiProperty({
        description: 'smsCode',
        required: false,
        type: String,
    })
    smsCode?: string;
}
