import { Injectable, Inject } from '@nestjs/common';
import { PKH_PATIENT_CONNECTION } from '../config/pkhPatientConnection';
import { THU_DUC_HOSPITAL_CONNECTION } from '../config/thuDucHospitalConnection';
import { OutBookingsDTO } from './dto/outBookings.dto';
import * as moment from 'moment';

@Injectable()
export class BookingService {

    constructor(
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        @Inject(THU_DUC_HOSPITAL_CONNECTION) private readonly hospitalKnex,
    ) { }

    async getBookingDetailUMCByTransactionCode(code: string): Promise<any> {
        const paymentTable = 'payment';
        const bookingTable = 'booking';
        const pTimeTable = 'ptime';
        const patientTable = 'patient';
        const scheduleTable = 'schedule';
        const subjectTable = 'subject';
        const roomTable = 'room';
        const doctorTable = 'doctor';
        return this.pkhPatientKnex()
            .select(
                `${bookingTable}.id as bookingId`,
                `${bookingTable}.booking_date as bookingDate`,
                `${bookingTable}.booking_number as bookingNumber`,
                `${bookingTable}.transaction_code_gd as transaction_code_gd`,
                `${bookingTable}.date_create as bookingCreateDate`,
                `${bookingTable}.bhyt_accept as patientBhyt`,
                `${bookingTable}.platform as platform`,
                `${bookingTable}.platform as platform`,
                `${pTimeTable}.buoi as ptimeBuoi`,
                // `${pTimeTable}.to as bookingTo`,
                /*     PAYMENT TABLE        */
                `${paymentTable}.amount as amount`,
                `${paymentTable}.amount_original as amountOriginal`,
                `${paymentTable}.transaction_code_tt as transactionCode`,
                `${paymentTable}.card_code as cardCode`,
                `${paymentTable}.method_id as methodId`,
                `${paymentTable}.result as typeResultBank`,
                /*     PATIENT TABLE        */
                `${patientTable}.id as patientId`,
                `${patientTable}.name as patientName`,
                `${patientTable}.surname as patientSurname`,
                `${patientTable}.cmnd as patientCmnd`,
                `${patientTable}.sex as patientSex`,
                `${patientTable}.mobile as patientMobile`,
                `${patientTable}.email as patientEmail`,
                `${patientTable}.address as patientAddress`,
                `${patientTable}.birthyear as birthYear`,
                `${patientTable}.birthdate as birthDate`,
                `${patientTable}.bvdhyd_msbn as bvId`,
                // `${patientTable}.old_bv_id as oldBvId`,
                `${patientTable}.medpro_id as medproId`,

                /*     SCHEDULE TABLE        */
                `${scheduleTable}.id as scheduleId`,
                /*     SUBJECT TABLE        */
                `${subjectTable}.id as subjectId`,
                `${subjectTable}.name as subjectName`,
                /*     ROOM TABLE        */
                `${roomTable}.id as roomId`,
                `${roomTable}.name as roomName`,
                `${roomTable}.description as roomDescription`,
                /*     DOCTOR TABLE        */
                `${doctorTable}.id as doctorId`,
                `${doctorTable}.name as doctorName`,
                `${doctorTable}.role as doctorRole`,
                `${doctorTable}.sex as doctorGender`,
                this.pkhPatientKnex.raw('? as ??', ['2', 'hospitalId']),
                this.pkhPatientKnex.raw('? as ??', ['Bệnh viện Đại Học Y Dược TP.HCM', 'hospitalName']),

            )
            .from(bookingTable)
            // .innerJoin(bookingTimeTable, `${bookingTable}.booking_time_id`, `${bookingTimeTable}.id`)
            .innerJoin(patientTable, `${patientTable}.id`, `${bookingTable}.patient_id`)
            .innerJoin(scheduleTable, `${scheduleTable}.id`, `${bookingTable}.schedule_id`)
            .innerJoin(pTimeTable, `${pTimeTable}.id`, `${scheduleTable}.ptime_id`)
            .innerJoin(subjectTable, `${subjectTable}.id`, `${scheduleTable}.hospital_subject_id`)
            .innerJoin(roomTable, `${roomTable}.id`, `${scheduleTable}.room_id`)
            .innerJoin(doctorTable, `${doctorTable}.id`, `${scheduleTable}.doctor_id`)
            .leftJoin(paymentTable, `${paymentTable}.id`, `${bookingTable}.payment_id`)
            .where(`${bookingTable}.transaction_code_gd`, code)
            .first();
    }

    async getBookingDetailThuDucByTransactionCode(code: string): Promise<any> {
        const paymentTable = 'payment';
        const bookingTable = 'booking';
        const bookingTimeTable = 'booking_time';
        const patientTable = 'patient';
        const scheduleTable = 'schedule';
        const subjectTable = 'subject';
        const roomTable = 'room';
        const doctorTable = 'doctor';
        return this.hospitalKnex()
            .select(
                `${bookingTable}.id as bookingId`,
                `${bookingTable}.booking_date as bookingDate`,
                `${bookingTable}.booking_number as bookingNumber`,
                `${bookingTable}.transaction_code_gd as transaction_code_gd`,
                `${bookingTable}.date_create as bookingCreateDate`,
                `${bookingTable}.bhyt_accept as patientBhyt`,
                `${bookingTable}.platform as platform`,
                `${bookingTable}.status as status`,
                `${bookingTimeTable}.buoi as bookingBuoi`,
                `${bookingTimeTable}.from as bookingFrom`,
                `${bookingTimeTable}.to as bookingTo`,
                /*     PAYMENT TABLE        */
                `${paymentTable}.amount as amount`,
                `${paymentTable}.amount_original as amountOriginal`,
                `${paymentTable}.transaction_code_tt as transactionCode`,
                `${paymentTable}.card_code as cardCode`,
                `${paymentTable}.method_id as methodId`,
                `${paymentTable}.result as typeResultBank`,
                /*     PATIENT TABLE        */
                `${patientTable}.id as patientId`,
                `${patientTable}.name as patientName`,
                `${patientTable}.surname as patientSurname`,
                `${patientTable}.cmnd as patientCmnd`,
                `${patientTable}.sex as patientSex`,
                `${patientTable}.mobile as patientMobile`,
                `${patientTable}.email as patientEmail`,
                `${patientTable}.address as patientAddress`,
                `${patientTable}.birthyear as birthYear`,
                `${patientTable}.birthdate as birthDate`,
                `${patientTable}.bv_id as bvId`,
                // `${patientTable}.old_bv_id as oldBvId`,
                `${patientTable}.medpro_id as medproId`,

                /*     SCHEDULE TABLE        */
                `${scheduleTable}.id as scheduleId`,
                /*     SUBJECT TABLE        */
                `${subjectTable}.id as subjectId`,
                `${subjectTable}.name as subjectName`,
                /*     ROOM TABLE        */
                `${roomTable}.id as roomId`,
                `${roomTable}.name as roomName`,
                `${roomTable}.description as roomDescription`,
                /*     DOCTOR TABLE        */
                `${doctorTable}.id as doctorId`,
                `${doctorTable}.name as doctorName`,
                `${doctorTable}.role as doctorRole`,
                `${doctorTable}.sex as doctorGender`,
                this.pkhPatientKnex.raw('? as ??', ['6', 'hospitalId']),
                this.pkhPatientKnex.raw('? as ??', ['Bệnh viện Quận Thủ Đức', 'hospitalName']),

            )
            .from(bookingTable)
            .innerJoin(patientTable, `${patientTable}.id`, `${bookingTable}.patient_id`)
            .innerJoin(scheduleTable, `${scheduleTable}.id`, `${bookingTable}.schedule_id`)
            .innerJoin(bookingTimeTable, `${bookingTimeTable}.id`, `${bookingTable}.booking_time_id`)
            .innerJoin(subjectTable, `${subjectTable}.id`, `${scheduleTable}.subject_id`)
            .innerJoin(roomTable, `${roomTable}.id`, `${scheduleTable}.room_id`)
            .innerJoin(doctorTable, `${doctorTable}.id`, `${scheduleTable}.doctor_id`)
            .leftJoin(paymentTable, `${paymentTable}.id`, `${bookingTable}.payment_id`)
            .where(`${bookingTable}.transaction_code_gd`, code)
            .first();
    }

    async allUMCTeleMedicineBookings(): Promise<OutBookingsDTO> {
        const bookingTable = 'booking';
        const scheduleTable = 'schedule';
        const hospitalSubject = 'hospital_subject';
        const subjectTable = 'subject';
        const doctorTable = 'doctor';
        const patientTable = 'patient';
        const currentDate = moment().format('YYYY-MM-DD');
        const list = await this.pkhPatientKnex('booking')
            .select(
                `${bookingTable}.id`,
                `booking_date as bookingDate`,
                `transaction_code_gd as transactionCode`,
                `payment_id as paymentId`,
                `schedule_id as scheduleId`,
                `patient_id as patientId`,
                `user_id as userId`,
                `booking_time_id as bookingTimeId`,
                `booking_number as bookingNumber`,
                `booking_phone as bookingPhone`,
                `bhyt_accept as bhytAccept`,
                `${bookingTable}.status`,
                `${bookingTable}.date_create as dateCreate`,
                /*     DOCTOR TABLE        */
                `${doctorTable}.id as doctorId`,
                `${doctorTable}.name as doctorName`,
                `${doctorTable}.role as doctorRole`,
                `${doctorTable}.sex as doctorGender`,
                /*     SUBJECT TABLE        */
                `${subjectTable}.id as subjectId`,
                `${subjectTable}.code as subjectCode`,
                `${subjectTable}.name as subjectName`,
                /*     PATIENT       */
                `${patientTable}.surname as patientSur`,
                `${patientTable}.name as patientName`,
                `${patientTable}.sex as patientGender`,
                `${patientTable}.email`,
            )
            .innerJoin(scheduleTable, `${scheduleTable}.id`, `${bookingTable}.schedule_id`)
            .innerJoin(patientTable, `${patientTable}.id`, `${bookingTable}.patient_id`)
            .leftJoin(hospitalSubject, `${hospitalSubject}.id`, `${scheduleTable}.hospital_subject_id`)
            .leftJoin(subjectTable, `${subjectTable}.id`, `${hospitalSubject}.subject_id`)
            .leftJoin(doctorTable, `${doctorTable}.id`, `${scheduleTable}.doctor_id`)
            .where(`${bookingTable}.status`, 1)
            // .where('booking_date', currentDate)
            .orderBy(`${bookingTable}.booking_date`, 'desc');
        // .offset(0)
        // .limit(15);
        return { list };
    }

    returnBookingStatus(status: number): string {
        switch (status) {
            case -1:
                return 'Hủy thanh toán';
            case -2:
                return 'Hủy không khám';
            case -3:
                return 'Quá ngày khám';
            case 0:
                return 'Chưa thanh toán';
            case 1:
                return 'Chưa khám';
            case 2:
                return 'Đã khám';
            default:
                return 'Đang xử lý';
        }
    }

}
