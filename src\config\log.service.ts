import { Injectable } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class LogService {
    constructor(private readonly pinoLogger: PinoLogger) {}

    // for logging and format for elk
    logError(msg: string, args?: any) {
        this.pinoLogger.assign(args);
        this.pinoLogger.error(msg, args);
    }

    logInfo(msg: string, args?: any) {
        this.pinoLogger.assign(args);
        this.pinoLogger.info(msg);
    }

    tracingInfo(msg: string, args?: any) {
        this.pinoLogger.assign(args);
        this.pinoLogger.trace(msg, args);
    }

    logWarn(msg: string, args?: any) {
        this.pinoLogger.assign(args);
        this.pinoLogger.warn(msg, args);
    }
}
