
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsDateString } from 'class-validator';

export class CrExamResultsDTO {

    @ApiProperty({
        description: 'PatientId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    PatientId: string;

    @ApiProperty({
        description: 'insuranceId',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    insuranceId?: string;

    @ApiProperty({
        description: 'Format kiểu YYYY-MM-DD String',
        required: false,
        type: String,
    })
    FromDate?: string;

    @ApiProperty({
        description: 'Format kiểu YYYY-MM-DD String',
        required: false,
        type: String,
    })
    ToDate?: string;
}
