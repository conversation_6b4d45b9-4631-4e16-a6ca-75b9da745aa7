
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class BookingShareToPayDTO {

    @ApiProperty({
        description: 'id',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    id: string;

    @ApiProperty({
        description: 'transactionId',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    transactionId?: string;
}
