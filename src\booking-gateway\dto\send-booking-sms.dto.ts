
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, IsMobilePhone } from 'class-validator';

export class SendBookingSMSDTO {

    @ApiProperty({
        description: 'bookingCode',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    readonly bookingCode: string;

    @ApiProperty({
        description: 'Số điện thoại',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @IsMobilePhone('vi-VN', { strictMode: false }, {
        message: 'Số điện thoại không hợp lệ.',
    })
    mobile: string;

}
