import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, IsNumber } from 'class-validator';

export class PageOptionnsDto {

    @ApiProperty({ description: 'Số trang', required: false})
    @Transform(value => Number(value))
    page: number = 0;

    @ApiProperty({ description: 'Số lượng', required: false})
    @Transform(value => Number(value))
    limit: number = 100;

    @ApiProperty({ description: 'Tìm kiếm', required: false})
    q?: string;

    get skip(): number {
        return this.page * this.limit;
    }

}
