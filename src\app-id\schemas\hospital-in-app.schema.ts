import * as mongoose from 'mongoose';
import * as json<PERSON>ongo from '@meanie/mongoose-to-json';
import { SUBJECT_COLLECTION_NAME } from 'src/subject-mongo/schemas/constants';
import { FeatureSchema } from 'src/feature-mongo/schemas/feature.schema';
import { SERVICE_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { DOCTOR_COLLECTION_NAME } from 'src/doctor-mongo/schemas/constants';
import { ROOM_COLLECTION_NAME } from 'src/room-mongo/schemas/constants';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { FeatureInAppSchema } from './feature-in-app.schema';

const Schema = mongoose.Schema;

const HospitalInAppSchema = new Schema(
    {
        partnerId: { type: String },
        name: String,
        short_name: String,
        sms_name: { type: String, uppercase: true },
        image: String,
        banner: { type: String, default: '' },
        status: Number,
        city_id: String,
        city: { type: Schema.Types.ObjectId, ref: CITY_COLLECTION_NAME },
        address: String,
        base_url: String,
        lat: Number,
        long: Number,
        hotline: String,
        subjects: [{ type: Schema.Types.ObjectId, ref: SUBJECT_COLLECTION_NAME }],
        services: [{ type: Schema.Types.ObjectId, ref: SERVICE_COLLECTION_NAME }],
        doctors: [{ type: Schema.Types.ObjectId, ref: DOCTOR_COLLECTION_NAME }],
        rooms: [{ type: Schema.Types.ObjectId, ref: ROOM_COLLECTION_NAME }],
        notifAppId: { type: String },
        notifApiKey: { type: String },
        features: [FeatureInAppSchema],
        message: { type: String, default: '' },
        sortOrder: { type: Number, default: 100 },
        website: String,
        phone: String,
        email: String,
        hotlinePartner: String,
        hotlineMedpro: String,
        workingTime: String,
        workingDate: String,
        googleMap: String,
        ios: String,
        android: String,
        bienLai: Boolean,
        deliveryStatus: { type: Number, default: 0 },
        contact: String,
    },
    { timestamps: true },
).plugin(jsonMongo);

export { HospitalInAppSchema };
