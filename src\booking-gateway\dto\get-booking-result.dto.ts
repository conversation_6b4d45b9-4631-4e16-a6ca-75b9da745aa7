import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, IsMongoId } from 'class-validator';

export class GetBookingResultDto {
    @ApiProperty({ description: 'patientId' })
    @IsNotEmpty()
    @Transform(value => `${value}`.trim())
    patientId: string;

    @ApiProperty({ description: 'userId' })
    @IsMongoId()
    @Transform(value => `${value}`.trim())
    userId: string;
}
