import { Body, Controller, Get, Param, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBody, ApiParam, ApiTags } from '@nestjs/swagger';
import { Pagination } from 'src/common/base/pagination';
import { BoGuard } from 'src/common/guards/bo.guard';
import { IUser } from 'src/user/interfaces/user.interface';
import { CskhService } from './cskh.service';
import { AddPartnersUserCskh } from './dto/add-partners-user-cskh.dto';
import { GetUsersCskhDto } from './dto/get-users-cskh.dto';
import { QueryOneUserCskh } from './dto/query-one-user-cskh.dto';
import { QueryUserCskh } from './dto/query-user-cskh.dto';
import { SearchUserCskh } from './dto/search-user-cskh.dto';
import { UpdateCskhDto } from './dto/update-cskh.dto';

@Controller('cskh')
@ApiTags('CSKH')
export class CskhController {
    constructor(private readonly service: CskhService) {}

    @Get('list')
    async getAllUserCskh(@Query() query: QueryUserCskh): Promise<GetUsersCskhDto> {
        return this.service.getAllUserCskh(query);
    }

    @Get('search')
    async searchUserCskh(@Query() search: SearchUserCskh): Promise<GetUsersCskhDto> {
        return this.service.searchUserCskhByFullname(search);
    }

    @Get()
    async getOneByCondition(@Query() query: QueryOneUserCskh): Promise<IUser> {
        return this.service.getOneByCondition(query);
    }

    @Get(':id')
    @ApiParam({
        name: 'id',
        description: 'User mongo id',
    })
    async getById(@Param('id') id: string): Promise<IUser> {
        return this.service.getById(id);
    }

    @Put('update/:id')
    @UseGuards(BoGuard)
    @ApiBody({
        type: UpdateCskhDto,
    })
    async updateUserCskh(@Param('id') id: string, @Body() formData: UpdateCskhDto): Promise<IUser> {
        return this.service.updateCskh(id, formData);
    }

    @Put('update/:id/insert-partners')
    @UseGuards(BoGuard)
    @ApiParam({
        name: 'id',
        description: 'User mongo id',
    })
    @ApiBody({
        type: AddPartnersUserCskh,
    })
    async insertPartnerIntoUser(@Param('id') id: string, @Body() formData: AddPartnersUserCskh): Promise<IUser> {
        return this.service.insertPartnerIntoUser(id, formData);
    }
}
