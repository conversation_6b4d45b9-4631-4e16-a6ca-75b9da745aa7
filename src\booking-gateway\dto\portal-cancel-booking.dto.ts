
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class PortalCancelBookingDTO {

    @ApiProperty({
        description: 'idBooking',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    id: string;

    @ApiProperty({
        description: 'apiKey',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    apiKey: string;

    @ApiProperty({
        description: 'username',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    username: string;

    @ApiProperty({
        description: 'appId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    appId: string;

    @ApiProperty({
        description: 'partnerId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    partnerId: string;

}
