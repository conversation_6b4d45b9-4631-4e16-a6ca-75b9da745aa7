import { Injectable, CanActivate, ExecutionContext, HttpException } from '@nestjs/common';
import { UrlConfigService } from 'src/config/config.url.service';

@Injectable()
export class BoGuard implements CanActivate {
    constructor(private urlConfigService: UrlConfigService) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const { appboid: appId, appbokey: appKey } = request.headers;
        const validate = appId === this.urlConfigService.getAPIBoId() && appKey === this.urlConfigService.getAPIBoKey();
        if (!validate) {
            throw new HttpException(`Thông tin xác thực gửi lên không chính xác. Vui lòng kiểm tra lại!`, 403);
        }
        return true;
    }
}
