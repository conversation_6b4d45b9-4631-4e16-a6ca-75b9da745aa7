import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { BOOKING_SEARCH_COLLECTION_NAME, BOOKING_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const BookingSearchSchema = new Schema({
    fullname: { type: String },
    msbn: { type: String },
    mobile: { type: String },
    username: { type: String }, // tương đương số điện thoại.
    idPatient: { type: String },
    bookingCode: { type: String },
    transactionId: { type: String },
    idBooking: { type: String },
    booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
    partnerId: { type: String },
    bookingType: { type: Number, default: 2 },
}, {
    collection: BOOKING_SEARCH_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);

BookingSearchSchema.index({ fullname: 'text' });
