import { ArgumentsHost, Catch, ExceptionFilter } from '@nestjs/common';
import { NotFoundException } from '../exceptions/not-found.exception';
import { Request, Response } from 'express';

@Catch(NotFoundException)
export class NotFoundFilter implements ExceptionFilter {
    catch(exception: any, host: ArgumentsHost) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse<Response>();
        const request = ctx.getRequest<Request>();
        const status = exception.getStatus();
        const message = exception.response;

        // return msg
        response.status(status).json({
            statusCode: status,
            message,
            path: request.url,
        });
    }
}
