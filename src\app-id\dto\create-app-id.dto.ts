import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class CreateAppIdDTO {
    @ApiProperty({
        description: 'name',
        required: false,
        type: String,
        default: '',
    })
    name: string;

    @ApiProperty({ description: 'appId', required: true, type: String })
    @IsNotEmpty({ message: 'appId is required' })
    appId: string;

    @ApiProperty({
        description: 'description',
        required: false,
        type: String,
        default: '',
    })
    description: string;
}
