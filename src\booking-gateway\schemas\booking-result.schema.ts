import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { BOOKING_RESULT_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const BookingResultSchema = new Schema({
    id: { type: String },
    bookingId: { type: String },
    prescriptionFiles: { type: [Schema.Types.Mixed] },
    diagnoseFiles: { type: [Schema.Types.Mixed] },
    functionExplorationFiles: { type: [Schema.Types.Mixed] },
    endoscopicFiles: { type: [Schema.Types.Mixed] },
    testFiles: { type: [Schema.Types.Mixed] },
}, {
    collection: BOOKING_RESULT_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
