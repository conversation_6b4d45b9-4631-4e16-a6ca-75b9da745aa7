import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';
import * as admin2 from 'firebase-admin';
import { FirebaseOptions } from './firebaseConnectionSecond';

@Injectable()
export class FirebaseSecondConfigService extends ConfigManager {
    provideConfigSpec() {
        return {
            FIREBASE_PROJECT_ID_2: {
                validate: Joi.string(),
                required: true,
            },
            FIREBASE_PRIVATE_KEY_2: {
                validate: Joi.string(),
                required: true,
            },
            FIREBASE_CLIENT_EMAIL_2: {
                validate: Joi.string(),
                required: true,
            },
            FIREBASE_DATABASE_URL_2: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    createServiceAccount(): FirebaseOptions {
        return {
            credential: admin2.credential.cert({
                projectId: this.get<string>('FIREBASE_PROJECT_ID_2'),
                privateKey: this.get<string>('FIREBASE_PRIVATE_KEY_2').replace(/\\n/g, '\n'), /* _______________ */
                clientEmail: this.get<string>('FIREBASE_CLIENT_EMAIL_2'),
            }),
            databaseURL: this.get<string>('FIREBASE_DATABASE_URL_2'),
        };
    }
}
