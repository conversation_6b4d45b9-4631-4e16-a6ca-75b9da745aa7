import { IsNumber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class ServiceLogRequest {
    @ApiProperty()
    @IsNumber()
    @Type(() => Number)
    pageSize: number;
    @ApiProperty()
    @IsNumber()
    @Type(() => Number)
    pageIndex: number;
    @ApiProperty()
    nameParent?: string;
    @ApiProperty()
    nameRepo?: string;
}
