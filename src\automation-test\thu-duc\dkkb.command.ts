import { Command, Positional } from 'nestjs-command';
import { Injectable } from '@nestjs/common';
// import { UserService } from './user.service';

@Injectable()
export class DKDBCommand {
    // constructor(
    //     private readonly userService: UserService,
    // ) { }

    @Command({ command: 'hello <username>', describe: 'create a user', autoExit: true })
    async create(
        @Positional({
            name: 'username',
            describe: 'the user account string',
            type: 'string',
        }) username: string,
    ) {
        // const user = await this.userService.create(account);
        // tslint:disable-next-line: no-console
        console.log(`Hello ${username}`);
    }

    @Command({ command: 'dkkb-thuduc', describe: 'Test luong dang ky kham benh - Benh vien thu duc', autoExit: true })
    async dkkbThuDuc() {
        // tslint:disable-next-line: no-console
        console.log('Hello');
    }
}
