import { Controller, Headers, Get, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { DistrictMongoService } from './district-mongo.service';

@Controller('district-mongo')
@ApiTags('Quận huyện - <PERSON>uản lý danh mục Quận huyện trên MongoDB')
export class DistrictMongoController {
    constructor(
        private readonly districtMongoService: DistrictMongoService,
    ) { }

    @Get('get-all-by-partner')
    async find(@Headers('partnerid') partnerid: string, @Query('city_id') cityId: string): Promise<any> {
        return this.districtMongoService.find(partnerid, cityId);
    }

    @Get('seed-data')
    async seed(@Headers('partnerid') partnerid: string): Promise<any> {
        return this.districtMongoService.seed(partnerid);
    }
}
