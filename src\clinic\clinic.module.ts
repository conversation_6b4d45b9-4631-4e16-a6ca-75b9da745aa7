import { CLINIC_COLLECTION_NAME } from './constant';
import { MessageEventSchema } from 'src/event/schemas/message-event.schema';
import { MESSAGE_EVENT_COLLECTION } from 'src/event/schemas/constants';
import { HttpModule, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ClinicController } from './clinic.controller';
import { ClinicService } from './clinic.service';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { ClinicSchema } from './schema/clinic.schema';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';
import { WARD_COLLECTION_NAME } from 'src/ward-mongo/schemas/constants';
import { DISTRICT_COLLECTION_NAME } from 'src/district-mongo/schemas/constants';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { WardSchema } from 'src/ward-mongo/schemas/ward.schema';
import { DistrictSchema } from 'src/district-mongo/schemas/district.schema';
import { CitySchema } from 'src/city-mongo/schemas/city.schema';

@Module({
  imports: [
    HttpModule,
    MongooseModule.forFeature([
      { name: CLINIC_COLLECTION_NAME, schema: ClinicSchema },
      // { name: GLOBAL_SETTING_COLLECTION_NAME, schema: GlobalSettingSchema },
      { name: WARD_COLLECTION_NAME, schema: WardSchema },
      { name: DISTRICT_COLLECTION_NAME, schema: DistrictSchema },
      { name: CITY_COLLECTION_NAME, schema: CitySchema },
    ]),
  ],
  controllers: [ClinicController],
  providers: [ClinicService],
})
export class ClinicModule { }
