
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

export class HealthHistoriesByExamGroupIdDTO {

    @ApiProperty({
        description: 'examId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    examId: string;

    @ApiProperty({
        description: 'examGroupId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    examGroupId: string;

    @ApiProperty({
        description: 'userId',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    userId: string;

    @ApiProperty({
        description: 'resultId',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    // @IsNotEmpty({
    //     message: 'vui lòng bổ sung thông tin',
    // })
    resultId?: string;

}
