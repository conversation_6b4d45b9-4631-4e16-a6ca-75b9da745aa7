import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';
import * as uuid from 'uuid';

export class UpdateBookingTelemedStatusDTO {

    @ApiProperty({
        description: 'id của booking',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    id: string;
}
