
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsDateString } from 'class-validator';

export class ConfirmBookingDTO {

    @ApiProperty({
        description: 'Booking Id',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    bookingId: string;

    @ApiProperty({
        description: 'Format kiểu ISO String',
        required: true,
        type: String,
    })
    @IsDateString({ strict: true }, {
        message: 'Thông tin ngày đặt khám. ISOString',
    })
    date: string;

    @ApiProperty({
        description: 'Patient Id',
        required: true,
        type: Number,
    })
    @Transform(value => `${value}`.trim())
    patientId: number;

    @ApiProperty({
        description: 'Payment Id',
        required: true,
        type: Number,
    })
    @Transform(value => `${value}`.trim())
    paymentId: number;

    @ApiProperty({
        description: 'Amount',
        required: true,
        type: Number,
    })
    @Transform(value => `${value}`.trim())
    amount: number;

    @ApiProperty({
        description: 'Payment method',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    paymentMethod: string;

    @ApiProperty({
        description: 'Payment gateway',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    paymentGateway: string;

    @ApiProperty({
        description: 'Transaction Id',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    transactionId: string;

    @ApiProperty({
        description: 'Format kiểu ISO String',
        required: true,
        type: String,
    })
    @IsDateString({ strict: true }, {
        message: 'Thông tin ngày đặt khám. ISOString',
    })
    paymentTime: string;
}
